"use strict";const e=require("../../common/vendor.js"),s=require("../../sheep/index.js");if(!Array){(e.resolveComponent("uni-search-bar")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"search",setup(t){const o=e.reactive({historyList:[]});function i(t){t&&(!function(s){o.historyList.includes(s)&&o.historyList.splice(o.historyList.indexOf(s),1);o.historyList.unshift(s),o.historyList.length>=10&&(o.historyList.length=10);e.index.setStorageSync("searchHistory",o.historyList)}(t),s.sheep.$router.go("/pages/goods/list",{keyword:t}))}function r(){e.index.showModal({title:"提示",content:"确认清除搜索历史吗？",success:function(s){s.confirm&&(o.historyTag=[],e.index.removeStorageSync("searchHistory"))}})}return e.onLoad((()=>{o.historyList=e.index.getStorageSync("searchHistory")||[]})),(s,t)=>({a:e.o((e=>i(e.value))),b:e.p({radius:"33",placeholder:"请输入关键字",cancelButton:"none",focus:!0}),c:e.o(r),d:e.f(o.historyList,((s,t,o)=>({a:e.t(s),b:e.o((e=>i(s)),t),c:t}))),e:e.p({bgStyle:{color:"#FFF"},title:"搜索"})})}},o=e._export_sfc(t,[["__scopeId","data-v-5ae75f05"]]);wx.createPage(o);
