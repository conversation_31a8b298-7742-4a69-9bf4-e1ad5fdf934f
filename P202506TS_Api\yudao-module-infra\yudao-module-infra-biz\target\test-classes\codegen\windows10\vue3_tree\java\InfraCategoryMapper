package cn.iocoder.yudao.module.infra.dal.mysql.demo;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraCategoryDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;

/**
 * 分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraCategoryMapper extends BaseMapperX<InfraCategoryDO> {

    default List<InfraCategoryDO> selectList(InfraCategoryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InfraCategoryDO>()
                .likeIfPresent(InfraCategoryDO::getName, reqVO.getName())
                .orderByDesc(InfraCategoryDO::getId));
    }

	default InfraCategoryDO selectByParentIdAndName(Long parentId, String name) {
	    return selectOne(InfraCategoryDO::getParentId, parentId, InfraCategoryDO::getName, name);
	}

    default Long selectCountByParentId(Long parentId) {
        return selectCount(InfraCategoryDO::getParentId, parentId);
    }

}