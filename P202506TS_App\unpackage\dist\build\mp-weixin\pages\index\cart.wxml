<s-layout wx:if="{{v}}" class="data-v-ce430b72" u-s="{{['d']}}" style="{{t}}" u-i="ce430b72-0" bind:__l="__l" u-p="{{v}}"><s-empty wx:if="{{a}}" class="data-v-ce430b72" u-i="ce430b72-1,ce430b72-0" bind:__l="__l" u-p="{{b}}"/><view wx:if="{{c}}" class="cart-box ss-flex ss-flex-col ss-row-between data-v-ce430b72"><view class="cart-header ss-flex ss-col-center ss-row-between ss-p-x-30 data-v-ce430b72"><view class="header-left ss-flex ss-col-center ss-font-26 data-v-ce430b72"> 共 <text class="goods-number ui-TC-Main ss-flex data-v-ce430b72">{{d}}</text> 件商品 </view><view class="header-right data-v-ce430b72"><button wx:if="{{e}}" class="ss-reset-button data-v-ce430b72" bindtap="{{f}}"> 取消 </button><button wx:else class="ss-reset-button ui-TC-Main data-v-ce430b72" bindtap="{{g}}"> 编辑 </button></view></view><view class="cart-content ss-flex-1 ss-p-x-30 ss-m-b-40 data-v-ce430b72"><view wx:for="{{h}}" wx:for-item="item" wx:key="j" class="goods-box ss-r-10 ss-m-b-14 data-v-ce430b72"><view class="ss-flex ss-col-center data-v-ce430b72"><label class="check-box ss-flex ss-col-center ss-p-l-10 data-v-ce430b72" bindtap="{{item.c}}"><radio class="data-v-ce430b72" checked="{{item.a}}" color="var(--ui-BG-Main)" style="transform:scale(0.8)" catchtap="{{item.b}}"/></label><s-goods-item wx:if="{{item.i}}" class="data-v-ce430b72" u-s="{{['tool']}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"><su-number-box wx:if="{{i}}" class="data-v-ce430b72" bindchange="{{item.d}}" u-i="{{item.e}}" bind:__l="__l" bindupdateModelValue="{{item.f}}" u-p="{{item.g}}" slot="tool"/></s-goods-item></view></view></view><su-fixed wx:if="{{j}}" class="data-v-ce430b72" u-s="{{['d']}}" u-i="ce430b72-4,ce430b72-0" bind:__l="__l" u-p="{{s}}"><view class="cart-footer ss-flex ss-col-center ss-row-between ss-p-x-30 border-bottom data-v-ce430b72"><view class="footer-left ss-flex ss-col-center data-v-ce430b72"><label class="check-box ss-flex ss-col-center ss-p-r-30 data-v-ce430b72" bindtap="{{m}}"><radio class="data-v-ce430b72" checked="{{k}}" color="var(--ui-BG-Main)" style="transform:scale(0.8)" catchtap="{{l}}"/><view class="ss-m-l-8 data-v-ce430b72"> 全选</view></label><text class="data-v-ce430b72">合计：</text><view class="text-price price-text data-v-ce430b72">{{n}}</view></view><view class="footer-right data-v-ce430b72"><button wx:if="{{o}}" class="ss-reset-button ui-BG-Main-Gradient pay-btn ui-Shadow-Main data-v-ce430b72" bindtap="{{p}}"> 删除 </button><button wx:else class="ss-reset-button ui-BG-Main-Gradient pay-btn ui-Shadow-Main data-v-ce430b72" bindtap="{{r}}"> 去结算 {{q}}</button></view></view></su-fixed></view></s-layout>