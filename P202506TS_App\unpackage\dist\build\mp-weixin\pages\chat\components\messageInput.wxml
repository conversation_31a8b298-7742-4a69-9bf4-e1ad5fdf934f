<view class="send-wrap ss-flex data-v-82ed4e2d"><view class="left ss-flex ss-flex-1 data-v-82ed4e2d"><uni-easyinput wx:if="{{b}}" class="ss-flex-1 ss-p-l-22 data-v-82ed4e2d" u-i="82ed4e2d-0" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"></uni-easyinput></view><text class="sicon-basic bq data-v-82ed4e2d" catchtap="{{c}}"></text><text wx:if="{{d}}" class="{{['sicon-edit', 'data-v-82ed4e2d', e && 'is-active']}}" catchtap="{{f}}"></text><button wx:if="{{g}}" class="ss-reset-button send-btn data-v-82ed4e2d" bindtap="{{h}}"> 发送 </button></view>