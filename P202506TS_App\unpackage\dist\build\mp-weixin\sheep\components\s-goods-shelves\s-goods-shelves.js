"use strict";const e=require("../../../common/vendor.js"),a=require("../../index.js"),t=require("../../api/product/spu.js");if(!Array){e.resolveComponent("s-goods-column")()}Math;const d={__name:"s-goods-shelves",props:{data:{type:Object,default(){}},styles:{type:Object,default(){}}},setup(d){const o=d,{layoutType:s,spuIds:r}=o.data;let{marginLeft:i,marginRight:n}=o.styles;const u=e.ref([]);return e.onMounted((async()=>{if(r.length>0){let{data:e}=await t.SpuApi.getSpuListByIds(r.join(","));u.value=e}})),(t,o)=>e.e({a:"twoCol"===e.unref(s)},"twoCol"===e.unref(s)?{b:e.f(u.value,((t,o,s)=>{var r;return{a:e.o((d=>e.unref(a.sheep).$router.go("/pages/goods/index",{id:t.id})),t.id),b:"32be2126-0-"+s,c:e.p({size:"xs",goodsFields:d.data.fields,tagStyle:d.data.badge,data:t,titleColor:null==(r=d.data.fields.name)?void 0:r.color,topRadius:d.data.borderRadiusTop,bottomRadius:d.data.borderRadiusBottom,titleWidth:(454-2*e.unref(n)-2*d.data.space-2*e.unref(i))/2}),d:t.id}})),c:e.s({padding:d.data.space+"rpx"}),d:e.s({margin:"-"+d.data.space+"rpx"})}:{},{e:"threeCol"===e.unref(s)},"threeCol"===e.unref(s)?{f:e.f(u.value,((t,o,s)=>{var r;return{a:e.o((d=>e.unref(a.sheep).$router.go("/pages/goods/index",{id:t.id})),t.id),b:"32be2126-1-"+s,c:e.p({size:"sm",goodsFields:d.data.fields,tagStyle:d.data.badge,data:t,titleColor:null==(r=d.data.fields.name)?void 0:r.color,topRadius:d.data.borderRadiusTop,bottomRadius:d.data.borderRadiusBottom}),d:t.id}})),g:e.s({padding:d.data.space+"rpx"}),h:e.s({margin:"-"+d.data.space+"rpx"})}:{},{i:"horizSwiper"===e.unref(s)},"horizSwiper"===e.unref(s)?{j:e.f(u.value,((t,o,s)=>{var r;return{a:e.o((d=>e.unref(a.sheep).$router.go("/pages/goods/index",{id:t.id})),t.id),b:"32be2126-2-"+s,c:e.p({size:"sm",goodsFields:d.data.fields,tagStyle:d.data.badge,data:t,titleColor:null==(r=d.data.fields.name)?void 0:r.color,titleWidth:(750-2*e.unref(n)-4*d.data.space-2*e.unref(i))/3}),d:t.id}})),k:e.s({marginRight:2*d.data.space+"rpx"})}:{})}},o=e._export_sfc(d,[["__scopeId","data-v-32be2126"]]);wx.createComponent(o);
