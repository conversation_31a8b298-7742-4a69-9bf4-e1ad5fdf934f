"use strict";const o=require("../../../common/vendor.js");if(!Array){(o.resolveComponent("s-coupon-list")+o.resolveComponent("su-popup"))()}Math||((()=>"../s-coupon-list/s-coupon-list.js")+(()=>"../../ui/su-popup/su-popup.js"))();const e={__name:"s-coupon-select",props:{modelValue:{type:Object,default(){}},show:{type:Boolean,default:!1}},emits:["confirm","close"],setup(e,{emit:c}){const t=e,s=c,p=o.reactive({couponInfo:o.computed((()=>t.modelValue)),couponId:void 0});function n(o){p.couponId===o?p.couponId=void 0:p.couponId=o}const u=()=>{s("confirm",p.couponId)};return(c,t)=>({a:o.f(p.couponInfo.filter((o=>o.match)),((e,c,t)=>({a:p.couponId===e.id,b:o.o((o=>n(e.id)),c),c:o.o((o=>n(e.id)),c),d:"a2598bc0-1-"+t+",a2598bc0-0",e:o.p({data:e,type:"user",disabled:!1}),f:c}))),b:o.f(p.couponInfo.filter((o=>!o.match)),((e,c,t)=>({a:o.t(e.mismatchReason||"未达到使用门槛"),b:"a2598bc0-2-"+t+",a2598bc0-0",c:o.p({data:e,type:"user",disabled:!0}),d:e.id}))),c:o.o(u),d:o.o((o=>s("close"))),e:o.p({show:e.show,type:"bottom",round:"20",showClose:!0,backgroundColor:"#f2f2f2"})})}},c=o._export_sfc(e,[["__scopeId","data-v-a2598bc0"]]);wx.createComponent(c);
