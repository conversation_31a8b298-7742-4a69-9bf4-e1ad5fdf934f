"use strict";const o=require("../../common/vendor.js"),e=require("../../sheep/index.js"),t=require("../../sheep/api/promotion/coupon.js"),n=require("../../sheep/hooks/useGoods.js"),p=require("../../sheep/api/product/spu.js"),a=require("../../sheep/api/product/category.js"),s=require("../../sheep/util/index.js");if(!Array){(o.resolveComponent("uni-collapse-item")+o.resolveComponent("uni-collapse")+o.resolveComponent("su-tabs")+o.resolveComponent("su-sticky")+o.resolveComponent("s-goods-column")+o.resolveComponent("uni-load-more")+o.resolveComponent("s-empty")+o.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.js")+(()=>"../../uni_modules/uni-collapse/components/uni-collapse/uni-collapse.js")+(()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../sheep/components/s-goods-column/s-goods-column.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const i={__name:"detail",setup(i){const c=o.reactive({id:0,couponId:0,coupon:{},pagination:{list:[],total:0,pageNo:1,pageSize:8},categoryId:0,tabMaps:[],currentTab:0,loadStatus:""});function u(o){s.resetPagination(c.pagination),c.currentTab=o.index,c.categoryId=o.value,d()}async function d(){c.loadStatus="loading";const{code:e,data:t}=await p.SpuApi.getSpuPage({categoryId:c.categoryId,pageNo:c.pagination.pageNo,pageSize:c.pagination.pageSize});0===e&&(c.pagination.list=o.lodash.concat(c.pagination.list,t.list),c.pagination.total=t.total,c.loadStatus=c.pagination.list.length<c.pagination.total?"more":"noMore")}async function r(){const{code:e}=await t.CouponApi.takeCoupon(c.id);0===e&&(o.index.showToast({title:"领取成功"}),setTimeout((()=>{l()}),1e3))}async function l(){const{code:o,data:e}=c.id>0?await t.CouponApi.getCouponTemplate(c.id):await t.CouponApi.getCoupon(c.couponId);0===o&&(c.coupon=e,2===c.coupon.productScope?await async function(){const{data:o,code:e}=await p.SpuApi.getSpuListByIds(c.coupon.productScopeValues.join(","));0===e&&(c.pagination.list=o)}():3===c.coupon.productScope&&await async function(){const{data:o,code:e}=await a.CategoryApi.getCategoryListByIds(c.coupon.productScopeValues.join(","));0===e&&(c.tabMaps=o.map((o=>({name:o.name,value:o.id}))),c.tabMaps.length>0&&(c.categoryId=c.tabMaps[0].value,await d()))}())}function g(){"noMore"!==c.loadStatus&&(c.pagination.pageNo++,d())}return o.onLoad((o=>{c.id=o.id,c.couponId=o.couponId,l(c.id,c.couponId)})),o.onReachBottom((()=>{g()})),(t,p)=>o.e({a:o.unref(e.sheep).$url.static("/static/img/shop/app/coupon_icon.png"),b:o.t(c.coupon.name),c:o.t(o.unref(n.fen2yuan)(c.coupon.usePrice)),d:o.t(1===c.coupon.discountType?"减 "+o.unref(n.fen2yuan)(c.coupon.discountPrice)+" 元":"打 "+c.coupon.discountPercent/10+" 折"),e:c.id>0},c.id>0?{f:o.t(c.coupon.canTake?"立即领取":"已领取")}:{g:o.t(1===c.coupon.status?"可使用":2===c.coupon.status?"已使用":"已过期")},{h:o.n(c.coupon.canTake||1===c.coupon.status?"use-btn":"disable-btn"),i:!c.coupon.canTake,j:o.o(r),k:2===c.coupon.validityType},2===c.coupon.validityType?{l:o.t(c.coupon.fixedEndTerm)}:{m:o.t(o.unref(e.sheep).$helper.timeFormat(c.coupon.validStartTime,"yyyy-mm-dd")),n:o.t(o.unref(e.sheep).$helper.timeFormat(c.coupon.validEndTime,"yyyy-mm-dd"))},{o:o.t(1===c.coupon.discountType?"满减券":"折扣券"),p:c.coupon.description},c.coupon.description?{q:o.t(c.coupon.description),r:o.p({title:"优惠券说明"})}:{},{s:1===c.coupon.productScope},1===c.coupon.productScope?{}:o.e({t:o.t(2===c.coupon.productScope?"指定商品可用":"指定分类可用"),v:3===c.coupon.productScope},3===c.coupon.productScope?{w:o.o(u),x:o.p({scrollable:!0,list:c.tabMaps,current:c.currentTab})}:{},{y:o.p({bgColor:"#fff"})}),{z:2===c.coupon.productScope},2===c.coupon.productScope?{A:o.f(c.pagination.list,((t,n,p)=>({a:o.o((n=>o.unref(e.sheep).$router.go("/pages/goods/index",{id:t.id})),n),b:"7e5bf54c-5-"+p+",7e5bf54c-0",c:o.p({size:"lg",data:t,goodsFields:{title:{show:!0},subtitle:{show:!0},price:{show:!0},original_price:{show:!0},sales:{show:!0},stock:{show:!1}}}),d:n})))}:{},{B:3===c.coupon.productScope},3===c.coupon.productScope?{C:o.f(c.pagination.list,((t,n,p)=>({a:o.o((n=>o.unref(e.sheep).$router.go("/pages/goods/index",{id:t.id})),n),b:"7e5bf54c-6-"+p+",7e5bf54c-0",c:o.p({size:"lg",data:t,goodsFields:{title:{show:!0},subtitle:{show:!0},price:{show:!0},original_price:{show:!0},sales:{show:!0},stock:{show:!1}}}),d:n})))}:{},{D:c.pagination.total>0&&3===c.coupon.productScope},c.pagination.total>0&&3===c.coupon.productScope?{E:o.o(g),F:o.p({status:c.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{G:3===c.coupon.productScope&&0===c.pagination.total},3===c.coupon.productScope&&0===c.pagination.total?{H:o.p({paddingTop:"0",icon:"/static/soldout-empty.png",text:"暂无商品"})}:{},{I:o.p({title:"优惠券详情"})})}},c=o._export_sfc(i,[["__scopeId","data-v-7e5bf54c"]]);wx.createPage(c);
