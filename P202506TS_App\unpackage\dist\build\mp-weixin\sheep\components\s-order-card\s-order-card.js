"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js");if(!Array){e.resolveComponent("uni-badge")()}Math;const o={__name:"s-order-card",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(o){const a=[{title:"待付款",value:"1",icon:"/static/img/shop/order/no_pay.png",path:"/pages/order/list",type:"unpaid",count:"unpaidCount"},{title:"待收货",value:"3",icon:"/static/img/shop/order/no_take.png",path:"/pages/order/list",type:"noget",count:"deliveredCount"},{title:"待评价",value:"4",icon:"/static/img/shop/order/no_comment.png",path:"/pages/order/list",type:"nocomment",count:"uncommentedCount"},{title:"售后单",value:"0",icon:"/static/img/shop/order/change_order.png",path:"/pages/order/aftersale/list",type:"aftersale",count:"afterSaleCount"},{title:"全部订单",value:"0",icon:"/static/img/shop/order/all_order.png",path:"/pages/order/list"}],r=o,n=e.computed((()=>t.sheep.$store("user").numData)),s=e.computed((()=>{const{bgType:e,bgImg:t,bgColor:o}=r.styles;return{background:"img"===e?`url(${t}) no-repeat top center / 100% 100%`:o}}));return(r,p)=>({a:e.f(a,((o,a,r)=>({a:e.unref(t.sheep).$url.static(o.icon),b:"d94ba49c-0-"+r,c:e.p({text:n.value.orderCount[o.count],absolute:"rightTop",size:"small"}),d:e.t(o.title),e:o.title,f:e.o((a=>e.unref(t.sheep).$router.go(o.path,{type:o.value})),o.title)}))),b:e.s(s.value),c:e.s({marginLeft:`${o.data.space}px`})})}},a=e._export_sfc(o,[["__scopeId","data-v-d94ba49c"]]);wx.createComponent(a);
