"use strict";const t=require("../store/index.js"),e=require("../config/index.js"),s=(e="",s="")=>e?0===e.indexOf("http")?e:(""===s&&(s=t.$store("app").info.cdnurl),s+e):"",r={cdn:s,thumb:(e="",r)=>function(e,s){const r=t.$store("app").info.filesystem;if("public"===r)return e;let i=s.width||"200",n=s.height||"200",o=s.mode||"lfit",c=s.quality||90,u=s.gravity||"center",l="",h="",f="",w=i+"x"+n;switch(r){case"aliyun":u||"center"==u||(o="mfit",h="/crop,g_"+a("aliyun",u)+",w_"+i+",h_"+n),c>0&&c<100&&(f="/quality,q_"+c),l="x-oss-process=image/resize,m_"+o+",w_"+i+",h_"+n,l+=h+f;break;case"qcloud":let t="thumbnail";switch(("fill"==o||!u&&"center"!=u)&&(t="crop",o="fill",h="/gravity/"+a("qcloud",u)),c>0&&c<100&&(f="/rquality/"+c),o){case"lfit":w+=">";break;case"mfit":w="!"+w+"r";case"fill":break;case"pad":w+="/pad/1";break;case"fixed":w+="!"}l="imageMogr2/"+t+"/"+w+h+f;break;case"qiniu":switch(("fill"==o||!u&&"center"!=u)&&(o="mfit",h="/gravity/"+a("qiniu",u)+"/crop/"+w),c>0&&c<100&&(f="/quality/"+c),o){case"lfit":case"pad":w+=">";break;case"mfit":w="!"+w+"r";break;case"fill":break;case"fixed":w+="!"}l="imageMogr2/thumbnail/"+w+h+f}return e+"?"+l}(e=s(e),r),static:(t="",r="")=>(""===r&&(r=e.staticUrl),"local"!==r&&(t=s(t,r)),t),css:(t="",r="")=>(""===r&&(r=e.staticUrl),"local"!==r&&(t=s(t,r)),`url(${t})`)};function a(t,e){return{aliyun:{north_west:"nw",north:"north",north_east:"ne",west:"west",center:"center",east:"east",south_west:"sw",south:"south",south_east:"se"},qcloud:{northwest:"nw",north:"north",northeast:"ne",west:"west",center:"center",east:"east",southwest:"sw",south:"south",southeast:"se"},qiniu:{NorthWest:"nw",North:"north",NorthEast:"ne",West:"west",Center:"center",East:"east",SouthWest:"sw",South:"south",SouthEast:"se"}}[t][e]}exports.$url=r;
