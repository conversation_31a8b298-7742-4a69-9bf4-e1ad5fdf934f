"use strict";const e=require("../../../common/vendor.js");if(!Array){(e.resolveComponent("su-toolbar")+e.resolveComponent("su-popup"))()}Math||((()=>"../su-toolbar/su-toolbar.js")+(()=>"../su-popup/su-popup.js"))();const n={__name:"su-region-picker",props:{show:{type:Boolean,default:!1},cancelColor:{type:String,default:"#6666"},confirmColor:{type:String,default:"var(--ui-BG-Main)"},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"}},emits:["confirm","cancel","change"],setup(n,{emit:t}){const r=e.index.getStorageSync("areaData"),o=e=>{let n=e.length;return n<=7?"":n<9?"font-size:28rpx":"font-size: 24rpx"},a=e.reactive({currentIndex:[0,0,0],moving:!1}),c=t,i=r,l=e.computed((()=>r[a.currentIndex[0]].children)),u=e.computed((()=>{var e;return null==(e=l.value[a.currentIndex[1]])?void 0:e.children})),d=()=>{a.moving=!0},m=()=>{a.moving=!1},s=()=>{c("cancel")},p=e=>{a.currentIndex[0]!==e.detail.value[0]||a.currentIndex[1]!==e.detail.value[1]?(a.currentIndex[0]!==e.detail.value[0]&&(e.detail.value[1]=0),e.detail.value[2]=0,a.currentIndex=e.detail.value,c("change",a.currentIndex)):a.currentIndex[2]=e.detail.value[2]};return(t,r)=>({a:e.o(s),b:e.o((e=>((e=null)=>{if(a.moving)return;let n=a.currentIndex,t=i[n[0]],r=l.value[n[1]],o=u.value[n[2]],d={province_name:t.name,province_id:t.id,city_name:r.name,city_id:r.id,district_name:o.name,district_id:o.id};e&&c(e,d)})("confirm"))),c:e.p({cancelColor:n.cancelColor,confirmColor:n.confirmColor,cancelText:n.cancelText,confirmText:n.confirmText,title:"选择区域"}),d:e.f(e.unref(i),((n,t,r)=>({a:e.t(n.name),b:e.s(o(n.name)),c:n.id}))),e:e.f(l.value,((n,t,r)=>({a:e.t(n.name),b:e.s(o(n.name)),c:n.id}))),f:e.f(u.value,((n,t,r)=>({a:e.t(n.name),b:e.s(o(n.name)),c:n.id}))),g:a.currentIndex,h:e.o(p),i:e.o(d),j:e.o(m),k:e.o(s),l:e.p({show:n.show,round:"20"})})}},t=e._export_sfc(n,[["__scopeId","data-v-1df68b1d"]]);wx.createComponent(t);
