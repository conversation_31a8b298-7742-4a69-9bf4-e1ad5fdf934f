"use strict";const e=require("../../request/index.js"),s={getUserInfo:()=>e.request({url:"/member/user/get",method:"GET",custom:{showLoading:!1,auth:!0}}),updateUser:s=>e.request({url:"/member/user/update",method:"PUT",data:s,custom:{auth:!0,showSuccess:!0,successMsg:"更新成功"}}),updateUserMobile:s=>e.request({url:"/member/user/update-mobile",method:"PUT",data:s,custom:{loadingMsg:"验证中",showSuccess:!0,successMsg:"修改成功"}}),updateUserMobileByWeixin:s=>e.request({url:"/member/user/update-mobile-by-weixin",method:"PUT",data:{code:s},custom:{showSuccess:!0,loadingMsg:"获取中",successMsg:"修改成功"}}),updateUserPassword:s=>e.request({url:"/member/user/update-password",method:"PUT",data:s,custom:{loadingMsg:"验证中",showSuccess:!0,successMsg:"修改成功"}}),resetUserPassword:s=>e.request({url:"/member/user/reset-password",method:"PUT",data:s,custom:{loadingMsg:"验证中",showSuccess:!0,successMsg:"修改成功"}})};exports.UserApi=s;
