<s-layout wx:if="{{r}}" class="data-v-fdf46d5d" u-s="{{['d']}}" u-i="fdf46d5d-0" bind:__l="__l" u-p="{{r}}"><view class="change-password-wrap ss-p-x-30 data-v-fdf46d5d"><view class="ss-m-t-40 data-v-fdf46d5d"><uni-forms wx:if="{{q}}" class="r data-v-fdf46d5d" u-s="{{['d']}}" u-r="changePasswordRef" u-i="fdf46d5d-1,fdf46d5d-0" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"><uni-forms-item wx:if="{{g}}" class="data-v-fdf46d5d" u-s="{{['d']}}" u-i="fdf46d5d-2,fdf46d5d-1" bind:__l="__l" u-p="{{g}}"><uni-easyinput wx:if="{{f}}" class="data-v-fdf46d5d" u-s="{{['right']}}" u-i="fdf46d5d-3,fdf46d5d-2" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><button class="{{['ss-reset-button', 'code-btn', 'data-v-fdf46d5d', b && 'code-btn-disabled']}}" disabled="{{c}}" bindtap="{{d}}" slot="right">{{a}}</button></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{j}}" class="data-v-fdf46d5d" u-s="{{['d']}}" u-i="fdf46d5d-4,fdf46d5d-1" bind:__l="__l" u-p="{{j}}"><uni-easyinput wx:if="{{i}}" class="data-v-fdf46d5d" u-i="fdf46d5d-5,fdf46d5d-4" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{m}}" class="data-v-fdf46d5d" u-s="{{['d']}}" u-i="fdf46d5d-6,fdf46d5d-1" bind:__l="__l" u-p="{{m}}"><uni-easyinput wx:if="{{l}}" class="data-v-fdf46d5d" u-i="fdf46d5d-7,fdf46d5d-6" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"></uni-easyinput></uni-forms-item><view class="ss-m-t-60 data-v-fdf46d5d"><button class="ss-reset-button submit-btn data-v-fdf46d5d" bindtap="{{n}}"> 确认修改 </button></view></uni-forms></view></view></s-layout>