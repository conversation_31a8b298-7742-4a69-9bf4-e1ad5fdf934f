"use strict";const e=require("../../request/index.js"),t={getAfterSalePage:t=>e.request({url:"/trade/after-sale/page",method:"GET",params:t,custom:{showLoading:!1}}),createAfterSale:t=>e.request({url:"/trade/after-sale/create",method:"POST",data:t}),getAfterSale:t=>e.request({url:"/trade/after-sale/get",method:"GET",params:{id:t}}),cancelAfterSale:t=>e.request({url:"/trade/after-sale/cancel",method:"DELETE",params:{id:t}}),getAfterSaleLogList:t=>e.request({url:"/trade/after-sale-log/list",method:"GET",params:{afterSaleId:t}}),deliveryAfterSale:t=>e.request({url:"/trade/after-sale/delivery",method:"PUT",data:t})};exports.AfterSaleApi=t;
