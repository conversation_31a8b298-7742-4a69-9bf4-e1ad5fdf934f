"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),r=require("../../hooks/useGoods.js"),i={__name:"s-goods-item",props:{img:{type:String,default:"https://img1.baidu.com/it/u=1601695551,235775011&fm=26&fmt=auto"},title:{type:String,default:""},titleWidth:{type:Number,default:0},skuText:{type:[String,Array],default:""},price:{type:[String,Number],default:""},priceColor:{type:[String],default:""},num:{type:[String,Number],default:0},point:{type:[String,Number],default:""},radius:{type:[String],default:""},marginBottom:{type:[String],default:""}},setup(i){const u=i,o=t.computed((()=>u.skuText?"object"==typeof u.skuText?u.skuText.join(","):u.skuText:""));return(u,p)=>t.e({a:t.unref(e.sheep).$url.cdn(i.img),b:i.title},i.title?{c:t.t(i.title)}:{},{d:o.value},o.value?{e:t.t(o.value)}:{},{f:i.price&&Number(i.price)>0},i.price&&Number(i.price)>0?{g:t.t(t.unref(r.fen2yuan)(i.price)),h:t.s({color:i.priceColor})}:{},{i:i.point&&Number(i.price)>0},(i.point&&Number(i.price),{}),{j:i.point},i.point?{k:t.unref(e.sheep).$url.static("/static/img/shop/goods/score1.svg"),l:t.t(i.point)}:{},{m:i.num},i.num?{n:t.t(i.num)}:{},{o:t.s({width:i.titleWidth?i.titleWidth+"rpx":""}),p:t.s({borderRadius:i.radius+"rpx",marginBottom:i.marginBottom+"rpx"})})}},u=t._export_sfc(i,[["__scopeId","data-v-acc36881"]]);wx.createComponent(u);
