"use strict";const e=require("../../../common/vendor.js"),o=require("../../index.js");if(!Array){(e.resolveComponent("uni-grid-item")+e.resolveComponent("uni-grid"))()}Math||((()=>"../../../uni_modules/uni-grid/components/uni-grid-item/uni-grid-item.js")+(()=>"../../../uni_modules/uni-grid/components/uni-grid/uni-grid.js"))();const t={__name:"s-menu-grid",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(t){const r=t,n=e.computed((()=>{const{bgType:e,bgImg:o,bgColor:t}=r.styles;return{background:"img"===e?`url(${o}) no-repeat top center / 100% 100%`:t}}));return(r,s)=>({a:e.f(t.data.list,((t,r,n)=>e.e({a:t.badge.show},t.badge.show?{b:e.t(t.badge.text),c:e.s({background:t.badge.bgColor,color:t.badge.textColor})}:{},{d:e.unref(o.sheep).$url.cdn(t.iconUrl),e:e.t(t.title),f:e.s({color:t.titleColor}),g:e.t(t.subtitle),h:e.s({color:t.subtitleColor}),i:r,j:e.o((r=>e.unref(o.sheep).$router.go(t.url)),r),k:"87781ccd-1-"+n+",87781ccd-0"}))),b:e.p({showBorder:Boolean(t.data.border),column:t.data.column}),c:e.s(n.value),d:e.s({marginLeft:`${t.data.space}px`})})}},r=e._export_sfc(t,[["__scopeId","data-v-87781ccd"]]);wx.createComponent(r);
