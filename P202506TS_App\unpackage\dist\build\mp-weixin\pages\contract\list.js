"use strict";const t=require("../../common/vendor.js"),a=require("../../sheep/index.js"),e=require("../../sheep/util/index.js"),n=require("../../sheep/api/member/contract.js");require("../../sheep/config/index.js");const o=require("../../sheep/util/contract-status-const.js");if(!Array){(t.resolveComponent("su-tabs")+t.resolveComponent("su-sticky")+t.resolveComponent("uni-list-item")+t.resolveComponent("uni-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-empty")+t.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const u={__name:"list",setup(u){const s=t.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:6},auditStatus:o.ContractStatusEnum.DRAFTING.value,keyword:"",tabMaps:[],loadStatus:""});function i(){e.resetPagination(s.pagination)}function r(t){s.keyword=t,i(),d()}function c(t){i(),s.currentTab=t.index,s.auditStatus=s.tabMaps[t.index].value,d()}async function d(){s.loadStatus="loading";const a=t.index.getStorageSync("enterprise"),{code:e,data:o}=await n.ContractApi.getContractPage({pageNo:s.pagination.pageNo,pageSize:s.pagination.pageSize,auditStatus:s.auditStatus,enterpriseId:a.id,name:s.keyword});0===e&&(s.pagination.list=t.lodash.concat(s.pagination.list,o.list),s.pagination.total=o.total,s.loadStatus=s.pagination.list.length<s.pagination.total?"more":"noMore")}function l(){"noMore"!==s.loadStatus&&(s.pagination.pageNo++,d())}return t.onLoad((t=>{s.keyword=t.keyword,s.tabMaps=Object.values(o.ContractStatusEnum).map((t=>({...t,name:t.label}))),d()})),t.onReachBottom((()=>{l()})),t.onPullDownRefresh((()=>{d(),setTimeout((function(){t.index.stopPullDownRefresh()}),800)})),(u,i)=>t.e({a:t.o(c),b:t.p({list:s.tabMaps,scrollable:!1,current:s.currentTab}),c:t.p({bgColor:"#fff"}),d:s.pagination.total>0},s.pagination.total>0?{e:t.f(s.pagination.list,((u,i,c)=>{var d;return t.e({a:t.t(u.no),b:t.t((null==(d=Object.values(t.unref(o.ContractStatusEnum)).find((t=>t.value===u.auditStatus)))?void 0:d.label)||"未知状态"),c:t.unref(o.ContractStatusColorEnum)[u.auditStatus],d:t.t(u.name),e:t.f(u.signList,((a,e,n)=>({a:t.t(a.seqLabel),b:t.t(a.fullName),c:t.t(a.userName),d:t.t(a.userMobile),e:e}))),f:t.t(t.unref(e.formatDate)(u.createTime,"YYYY.MM.DD")),g:t.o((t=>{return e=u.id,void a.sheep.$router.go("/pages/contract/risk-list",{id:e});var e}),i),h:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"确认删除此合同吗？",success:async function(t){if(!t.confirm)return;const{code:o}=await n.ContractApi.deleteContract(e);0===o&&(a.sheep.$helper.toast("删除成功"),await r())}})})(u.id)),i),i:s.auditStatus!==t.unref(o.ContractStatusEnum).INVALID.value&&u.auditStatus!==t.unref(o.ContractStatusEnum).DONE.value},s.auditStatus!==t.unref(o.ContractStatusEnum).INVALID.value&&u.auditStatus!==t.unref(o.ContractStatusEnum).DONE.value?{j:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"确定要作废该合同吗？",success:async function(u){if(!u.confirm)return;const s=t.index.getStorageSync("enterprise"),i={id:e,enterpriseId:s.id,auditStatus:o.ContractStatusEnum.INVALID.value},{code:c}=await n.ContractApi.updateContract(i);0===c&&(a.sheep.$helper.toast("作废成功"),await r())}})})(u.id)),i)}:{},{k:s.auditStatus===t.unref(o.ContractStatusEnum).INVALID.value&&u.auditStatus===t.unref(o.ContractStatusEnum).INVALID.value},s.auditStatus===t.unref(o.ContractStatusEnum).INVALID.value&&u.auditStatus===t.unref(o.ContractStatusEnum).INVALID.value?{l:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"确定要恢复该合同吗？",success:async function(u){if(!u.confirm)return;const s=t.index.getStorageSync("enterprise"),i={id:e,enterpriseId:s.id,auditStatus:o.ContractStatusEnum.DRAFTING.value},{code:c}=await n.ContractApi.updateContract(i);0===c&&(a.sheep.$helper.toast("合同已恢复"),await r())}})})(u.id)),i)}:{},{m:s.auditStatus===t.unref(o.ContractStatusEnum).SIGNED.value&&u.auditStatus===t.unref(o.ContractStatusEnum).SIGNED.value},s.auditStatus===t.unref(o.ContractStatusEnum).SIGNED.value&&u.auditStatus===t.unref(o.ContractStatusEnum).SIGNED.value?{n:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"确定要将此合同标记为已完成吗？",success:async function(u){if(!u.confirm)return;const s=t.index.getStorageSync("enterprise"),i={id:e,enterpriseId:s.id,auditStatus:o.ContractStatusEnum.DONE.value},{code:c}=await n.ContractApi.updateContract(i);0===c&&(a.sheep.$helper.toast("合同已标记为完成"),await r())}})})(u.id)),i)}:{},s.auditStatus!==t.unref(o.ContractStatusEnum).DRAFTING.value?t.e({o:s.auditStatus===t.unref(o.ContractStatusEnum).TO_BE_SIGN.value&&u.auditStatus===t.unref(o.ContractStatusEnum).TO_BE_SIGN.value},s.auditStatus===t.unref(o.ContractStatusEnum).TO_BE_SIGN.value&&u.auditStatus===t.unref(o.ContractStatusEnum).TO_BE_SIGN.value?{p:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"点击确定发起电子签约",success:async function(t){if(!t.confirm)return;const{code:o,data:u}=await n.ContractApi.getSignUrl(e);0===o&&(a.sheep.$router.go(u),r())}})})(u.id)),i)}:{},{q:s.auditStatus===t.unref(o.ContractStatusEnum).SIGNING.value&&u.auditStatus===t.unref(o.ContractStatusEnum).SIGNING.value},s.auditStatus===t.unref(o.ContractStatusEnum).SIGNING.value&&u.auditStatus===t.unref(o.ContractStatusEnum).SIGNING.value?{r:t.o((a=>{return e=u.id,void t.index.showModal({title:"提示",content:"点击确定撤回电子签约"+e,success:async function(t){t.confirm}});var e}),i)}:{},{s:"COMPLETE"===u.qysStatus},"COMPLETE"===u.qysStatus?{t:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"点击确定下载电子签约",success:async function(t){if(!t.confirm)return;const{code:o,data:u}=await n.ContractApi.getSignFile(e);0===o&&(a.sheep.$router.go(u),r())}})})(u.id,u.name)),i)}:{}):{},{v:t.o((t=>{return e=u.id,void a.sheep.$router.go("/pages/contractdraft/list",{id:e});var e}),i),w:t.o((t=>{return e=u.id,void a.sheep.$router.go("/pages/contractsign/list",{id:e});var e}),i),x:t.o((t=>{return e=u.id,void a.sheep.$router.go("/pages/contractdraft/reportList",{contractId:e});var e}),i),y:t.o((t=>{return e=u.id,void a.sheep.$router.go("/pages/enterprisepartnerthird/list",{contractId:e});var e}),i),z:t.o((t=>{u.id}),i),A:i,B:"5c02a061-4-"+c+",5c02a061-3"})})),f:s.auditStatus!==t.unref(o.ContractStatusEnum).DRAFTING.value}:{},{g:s.pagination.total>0},s.pagination.total>0?{h:t.o(l),i:t.p({status:s.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{j:0===s.pagination.total},0===s.pagination.total?{k:t.p({icon:t.unref(a.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无合同"})}:{},{l:t.o(r),m:t.p({navbar:"normal",leftWidth:40,rightWidth:100,tools:"search",defaultSearch:s.keyword})})}},s=t._export_sfc(u,[["__scopeId","data-v-5c02a061"]]);wx.createPage(s);
