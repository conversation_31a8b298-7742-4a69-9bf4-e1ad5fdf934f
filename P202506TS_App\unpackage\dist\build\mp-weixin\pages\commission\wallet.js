"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),o=require("../../sheep/api/trade/brokerage.js"),a=require("../../sheep/hooks/useGoods.js"),n=require("../../sheep/util/index.js");if(!Array){(e.resolveComponent("uni-datetime-picker")+e.resolveComponent("su-tabs")+e.resolveComponent("su-sticky")+e.resolveComponent("s-empty")+e.resolveComponent("uni-easyinput")+e.resolveComponent("su-popup")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js")+(()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../sheep/ui/su-popup/su-popup.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const i={__name:"wallet",setup(i){e.useCssVars((t=>({"555b5e32":e.unref(s)})));const s=t.sheep.$url.css("/static/img/shop/user/wallet_card_bg.png"),r=e.reactive({showMoney:!1,summary:{},today:"",date:[],currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:8},loadStatus:"",price:void 0,showModal:!1}),p=[{name:"分佣",value:"1"},{name:"提现",value:"2"}],u=e.computed((()=>r.date[0]===r.date[1]?r.date[0]:r.date.join("~")));async function c(){r.loadStatus="loading";let{code:t,data:a}=await o.BrokerageApi.getBrokerageRecordPage({pageSize:r.pagination.pageSize,pageNo:r.pagination.pageNo,bizType:p[r.currentTab].value,"createTime[0]":r.date[0]+" 00:00:00","createTime[1]":r.date[1]+" 23:59:59"});0===t&&(r.pagination.list=e.lodash.concat(r.pagination.list,a.list),r.pagination.total=a.total,r.loadStatus=r.pagination.list.length<r.pagination.total?"more":"noMore")}function d(e){n.resetPagination(r.pagination),r.currentTab=e.index,c()}function l(e){r.date[0]=e[0],r.date[1]=e[e.length-1],n.resetPagination(r.pagination),c()}async function m(){r.price<=0?t.sheep.$helper.toast("请输入正确的金额"):e.index.showModal({title:"提示",content:"确认把您的佣金转入到余额钱包中？",success:async function(e){if(!e.confirm)return;const{code:t}=await o.BrokerageApi.createBrokerageWithdraw({type:1,price:100*r.price});0===t&&(r.showModal=!1,await y(),d({index:1}))}})}async function y(){const{code:e,data:t}=await o.BrokerageApi.getBrokerageUserSummary();0===e&&(r.summary=t)}return e.onLoad((async t=>{r.today=e.dayjs().format("YYYY-MM-DD"),r.date=[r.today,r.today],2===t.type&&(r.currentTab=1),c(),y()})),e.onReachBottom((()=>{"noMore"!==r.loadStatus&&(r.pagination.pageNo++,c())})),(o,n)=>e.e({a:e.o((e=>r.showMoney=!r.showMoney)),b:e.n(r.showMoney?"cicon-eye":"cicon-eye-off"),c:e.t(r.showMoney?e.unref(a.fen2yuan)(r.summary.withdrawPrice||0):"*****"),d:e.o((o=>e.unref(t.sheep).$router.go("/pages/commission/withdraw"))),e:e.o((e=>r.showModal=!0)),f:e.t(r.showMoney?e.unref(a.fen2yuan)(r.summary.frozenPrice||0):"*****"),g:e.t(r.showMoney?e.unref(a.fen2yuan)(r.summary.brokeragePrice||0):"*****"),h:e.t(u.value),i:e.o(l),j:e.o((e=>r.date=e)),k:e.p({type:"daterange",end:r.today,modelValue:r.date}),l:e.o(d),m:e.p({list:p,scrollable:!1,current:r.currentTab}),n:0===r.pagination.total},0===r.pagination.total?{o:e.p({icon:"/static/data-empty.png",text:"暂无数据"})}:{},{p:e.o((e=>r.price=e)),q:e.p({inputBorder:!1,type:"number",placeholder:"请输入金额",modelValue:r.price}),r:e.o(m),s:e.o((e=>r.showModal=!1)),t:e.p({show:r.showModal,type:"bottom",round:"20",showClose:!0}),v:r.pagination.total>0},r.pagination.total>0?{w:e.f(r.pagination.list,((o,n,i)=>e.e({a:e.t(o.title),b:o.price>=0},o.price>=0?{c:e.t(e.unref(a.fen2yuan)(o.price))}:{d:e.t(e.unref(a.fen2yuan)(o.price))},{e:e.t(e.unref(t.sheep).$helper.timeFormat(o.createTime,"yyyy-mm-dd hh:MM:ss")),f:o.id})))}:{},{x:r.pagination.total>0},r.pagination.total>0?{y:e.p({status:r.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{z:e.s(o.__cssVars()),A:e.p({title:"佣金"})})}},s=e._export_sfc(i,[["__scopeId","data-v-0d50fb0d"]]);wx.createPage(s);
