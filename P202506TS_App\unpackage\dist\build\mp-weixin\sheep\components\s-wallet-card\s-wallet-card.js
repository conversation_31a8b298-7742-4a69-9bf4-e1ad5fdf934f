"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),s=require("../../hooks/useGoods.js"),o={__name:"s-wallet-card",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(o){const r=o,u=e.computed((()=>{const{bgType:e,bgImg:t,bgColor:s}=r.styles;return{background:"img"===e?`url(${t}) no-repeat top center / 100% 100%`:s}})),a=e.computed((()=>t.sheep.$store("user").userWallet)),n=e.computed((()=>t.sheep.$store("user").userInfo)),p=e.computed((()=>t.sheep.$store("user").numData));return(r,c)=>({a:e.t(e.unref(s.fen2yuan)(a.value.balance)||"0.00"),b:e.o((s=>e.unref(t.sheep).$router.go("/pages/user/wallet/money"))),c:e.t(n.value.point||0),d:e.o((s=>e.unref(t.sheep).$router.go("/pages/user/wallet/score"))),e:e.t(p.value.unusedCouponCount),f:e.o((s=>e.unref(t.sheep).$router.go("/pages/coupon/list",{type:"geted"}))),g:e.unref(t.sheep).$url.static("/static/img/shop/user/wallet_icon.png"),h:e.o((s=>e.unref(t.sheep).$router.go("/pages/user/wallet/money"))),i:e.s(u.value),j:e.s({marginLeft:`${o.data.space}px`})})}},r=e._export_sfc(o,[["__scopeId","data-v-dd183fae"]]);wx.createComponent(r);
