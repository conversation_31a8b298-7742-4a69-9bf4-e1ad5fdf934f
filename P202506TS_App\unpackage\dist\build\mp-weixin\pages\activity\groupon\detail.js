"use strict";const e=require("../../../common/vendor.js"),a=require("../../../sheep/index.js"),o=require("../../../sheep/hooks/useGoods.js"),d=require("../../../sheep/hooks/useModal.js"),t=require("../../../sheep/api/promotion/combination.js"),r=require("../../../sheep/api/product/spu.js"),s=require("../../../sheep/util/const.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("s-select-groupon-sku")+e.resolveComponent("s-empty")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../../sheep/components/s-select-groupon-sku/s-select-groupon-sku.js")+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const i={__name:"detail",setup(i){e.useCssVars((a=>({"408bcc16":e.unref(n)})));const n=a.sheep.$url.css("/static/img/shop/user/withdraw_bg.png"),c=2*a.sheep.$platform.device.statusBarHeight,u=e.reactive({data:{},goodsId:0,goodsInfo:{},showSelectSku:!1,selectedSkuPrice:{},activity:{},grouponId:0,grouponNum:0,grouponAction:"create",combinationHeadId:null,loading:!0}),p=e.computed((()=>{var d;return e.isEmpty(u.data)?{}:a.sheep.$platform.share.getShareInfo({title:u.data.headRecord.spuName,image:a.sheep.$url.cdn(u.data.headRecord.picUrl),desc:null==(d=u.data.goods)?void 0:d.subtitle,params:{page:s.SharePageEnum.GROUPON_DETAIL.value,query:u.data.headRecord.id}},{type:"groupon",title:u.data.headRecord.spuName,image:a.sheep.$url.cdn(u.data.headRecord.picUrl),price:o.fen2yuan(u.data.headRecord.combinationPrice)})}));function h(e){a.sheep.$router.go("/pages/order/detail",{id:e})}function m(){u.grouponAction="create",u.grouponId=0,u.showSelectSku=!0}function g(e){u.selectedSkuPrice=e}function l(e){a.sheep.$router.go("/pages/order/confirm",{data:JSON.stringify({order_type:"goods",combinationActivityId:u.activity.id,combinationHeadId:u.combinationHeadId,items:[{skuId:e.id,count:e.count}]})})}const R=e.computed((()=>o.useDurationTime(u.data.headRecord.expireTime)));function I(){d.showShareModal()}return e.onLoad((e=>{!async function(e){const{code:a,data:o}=await t.CombinationApi.getCombinationRecordDetail(e);if(0===a){u.data=o;const e=Number(u.data.headRecord.userSize-u.data.headRecord.userCount);u.remainNumber=e>0?e:0;const{data:a}=await t.CombinationApi.getCombinationActivity(o.headRecord.activityId);u.activity=a,u.grouponNum=a.userSize;const{data:d}=await r.SpuApi.getSpuDetail(a.spuId);u.goodsId=d.id,a.products.forEach((e=>{d.price=Math.min(d.price,e.combinationPrice)})),u.goodsInfo=d,d.skus.forEach((e=>{const o=a.products.find((a=>a.skuId===e.id));o?e.price=o.combinationPrice:e.stock=0}))}else u.data=null;u.loading=!1}(e.id)})),(o,d)=>e.e({a:u.loading},(u.loading,{}),{b:u.data&&!u.loading},u.data&&!u.loading?e.e({c:u.data.headRecord},u.data.headRecord?{d:e.t(u.data.headRecord.userSize),e:e.t(u.data.headRecord.userCount),f:e.o((o=>e.unref(a.sheep).$router.go("/pages/goods/groupon",{id:u.data.headRecord.activityId}))),g:e.s({top:Number(c+108)+"rpx"}),h:e.p({img:u.data.headRecord.picUrl,title:u.data.headRecord.spuName,price:u.data.headRecord.combinationPrice,priceColor:"#E1212B"}),i:e.s({marginTop:"-"+Number(c+88)+"rpx",paddingTop:Number(c+108)+"rpx"})}:{},{j:1===u.data.headRecord.status},1===u.data.headRecord.status?e.e({k:u.data.orderId},(u.data.orderId,{})):{},{l:2===u.data.headRecord.status},2===u.data.headRecord.status?{m:e.t(u.data.orderId?"拼团超时,已自动退款":"该团已解散")}:{},{n:0===u.data.headRecord.status},0===u.data.headRecord.status?e.e({o:u.data.headRecord.expireTime<=(new Date).getTime()},u.data.headRecord.expireTime<=(new Date).getTime()?{}:{p:e.t(u.data.headRecord.userSize-u.data.headRecord.userCount),q:e.t(R.value.h),r:e.t(R.value.m),s:e.t(R.value.s)}):{},{t:e.unref(a.sheep).$url.cdn(u.data.headRecord.avatar),v:e.f(u.data.memberRecords,((o,d,t)=>e.e({a:e.unref(a.sheep).$url.cdn(o.avatar),b:"1"==o.is_leader},(o.is_leader,{}),{c:o.id}))),w:e.f(u.remainNumber,((e,a,o)=>({a:e}))),x:e.unref(a.sheep).$url.static("/static/img/shop/avatar/unknown.png"),y:1===u.data.headRecord.status||2===u.data.headRecord.status},1===u.data.headRecord.status||2===u.data.headRecord.status?e.e({z:u.data.orderId},u.data.orderId?{A:e.o((e=>h(u.data.orderId)))}:{B:e.o(m)}):{},{C:0===u.data.headRecord.status},0===u.data.headRecord.status?e.e({D:u.data.headRecord.expireTime<=(new Date).getTime()},u.data.headRecord.expireTime<=(new Date).getTime()?e.e({E:u.data.orderId},u.data.orderId?{F:e.o((e=>h(u.data.orderId)))}:{G:e.o((e=>h(u.data.orderId)))}):e.e({H:u.data.orderId},u.data.orderId?{I:R.value.ms<=0,J:e.o(I)}:{K:R.value.ms<=0,L:e.o((e=>(u.grouponAction="join",u.grouponId=u.data.headRecord.activityId,u.combinationHeadId=u.data.headRecord.id,u.grouponNum=u.data.headRecord.userSize,void(u.showSelectSku=!0))))})):{},{M:!e.unref(e.isEmpty)(u.goodsInfo)},e.unref(e.isEmpty)(u.goodsInfo)?{}:{N:e.o(l),O:e.o(g),P:e.o((e=>u.showSelectSku=!1)),Q:e.p({show:u.showSelectSku,goodsInfo:u.goodsInfo,grouponAction:u.grouponAction,grouponNum:u.grouponNum})}):{},{R:!u.data&&!u.loading},u.data||u.loading?{}:{S:e.p({icon:"/static/goods-empty.png"})},{T:e.s(o.__cssVars()),U:e.p({title:"拼团详情",navbar:u.data&&!u.loading?"inner":"normal",onShareAppMessage:p.value})})}},n=e._export_sfc(i,[["__scopeId","data-v-9258f5ad"]]);i.__runtimeHooks=2,wx.createPage(n);
