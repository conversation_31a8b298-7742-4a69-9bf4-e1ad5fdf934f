"use strict";const e=require("../../common/vendor.js"),r=require("../store/index.js"),a=require("./index.js"),s=require("../router/index.js"),i=require("../url/index.js"),t=require("../api/trade/brokerage.js"),n=require("../util/const.js"),o=["H5","WechatOfficialAccount","WechatMiniProgram","App"],d=["forward","poster","link"],u=e=>{const s=r.$store("user");let i="0";void 0===e.shareId&&s.isLogin&&(i=s.userInfo.id);let t=n.SharePageEnum.HOME.value;void 0!==e.page&&(t=e.page);let d="0";void 0!==e.query&&(d=e.query);let u=o.indexOf(a._platform.name)+1,g="1";return void 0!==e.from&&(g=o.indexOf(e.from)+1),`spm=${i}.${t}.${d}.${u}.${g}`},g=e=>void 0===e?"pages/index/index":`pages/index/index?${e}`,p=(e,r="")=>`${r}?${e}`,c=async r=>{try{const a=r||e.index.getStorageSync("shareId");if(!a)return;const{data:s}=await t.BrokerageApi.bindBrokerageUser({bindUserId:a});s&&e.index.removeStorageSync("shareId")}catch(a){console.error(a)}},m={getShareInfo:(e={title:"",desc:"",image:"",params:{}},a={type:"user"})=>{const s={title:"",desc:"",image:"",path:"",link:"",query:"",poster:a,forward:{}};s.title=e.title,s.image=i.$url.cdn(e.image),s.desc=e.desc;const t=r.$store("app").platform.share,n=u(e.params);return s.query=n,s.link=p(n,t.linkAddress),s.path=g(),t.methods.includes("forward")&&(s.forward.path=g(n)),s},updateShareInfo:e=>{},decryptSpm:a=>{const i=r.$store("user");let t,u=a.split("."),g={spm:a,shareId:0,page:"",query:{},platform:"",from:""};switch(g.shareId=u[0],u[1]){case n.SharePageEnum.HOME.value:g.page=n.SharePageEnum.HOME.page;break;case n.SharePageEnum.GOODS.value:g.page=n.SharePageEnum.GOODS.page,g.query={id:u[2]};break;case n.SharePageEnum.GROUPON.value:g.page=n.SharePageEnum.GROUPON.page,t=u[2].split(","),g.query={id:t[0],activity_id:t[1]};break;case n.SharePageEnum.SECKILL.value:g.page=n.SharePageEnum.SECKILL.page,t=u[2].split(","),g.query={id:t[0]};break;case n.SharePageEnum.GROUPON_DETAIL.value:g.page=n.SharePageEnum.GROUPON_DETAIL.page,g.query={id:u[2]};break;case n.SharePageEnum.POINT.value:g.page=n.SharePageEnum.POINT.page,g.query={id:u[2]}}return g.platform=o[u[3]-1],g.from=d[u[4]-1],0!==g.shareId&&(e.index.setStorageSync("shareId",g.shareId),i.isLogin&&c(g.shareId)),g.page!==n.SharePageEnum.HOME.page&&s.$router.go(g.page,g.query),g},bindBrokerageUser:c};exports.$share=m;
