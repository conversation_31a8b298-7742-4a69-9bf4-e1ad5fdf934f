"use strict";const e=require("../../../common/vendor.js"),r=require("../../index.js");if(!Array){e.resolveComponent("su-image")()}Math;const t={__name:"s-image-block",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup:t=>(o,a)=>({a:e.p({src:e.unref(r.sheep).$url.cdn(t.data.imgUrl),mode:"widthFix"}),b:e.o((o=>{var a;return e.unref(r.sheep).$router.go(null==(a=t.data)?void 0:a.url)}))})};wx.createComponent(t);
