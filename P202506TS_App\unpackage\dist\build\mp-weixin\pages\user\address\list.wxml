<s-layout wx:if="{{i}}" class="data-v-c0548352" u-s="{{['d']}}" u-i="c0548352-0" bind:__l="__l" u-p="{{i}}"><view wx:if="{{a}}" class="data-v-c0548352"><s-address-item wx:for="{{b}}" wx:for-item="item" wx:key="a" class="data-v-c0548352" bindtap="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"/></view><su-fixed wx:if="{{f}}" class="data-v-c0548352" u-s="{{['d']}}" u-i="c0548352-2,c0548352-0" bind:__l="__l" u-p="{{f}}"><view class="footer-box ss-flex ss-row-between ss-p-20 data-v-c0548352"><button wx:if="{{c}}" bindtap="{{d}}" class="border ss-reset-button sync-wxaddress ss-m-20 ss-flex ss-row-center ss-col-center data-v-c0548352"><text class="cicon-weixin ss-p-r-10 data-v-c0548352" style="color:#09bb07;font-size:40rpx"></text> 导入微信地址 </button><button class="add-btn ss-reset-button ui-Shadow-Main data-v-c0548352" bindtap="{{e}}"> 新增收货地址 </button></view></su-fixed><s-empty wx:if="{{g}}" class="data-v-c0548352" u-i="c0548352-3,c0548352-0" bind:__l="__l" u-p="{{h}}"/></s-layout>