<s-layout wx:if="{{h}}" class="data-v-907371c6" u-s="{{['d']}}" u-i="907371c6-0" bind:__l="__l" u-p="{{h}}"><view wx:if="{{a}}" class="contract-content ss-m-x-20 ss-m-t-20 data-v-907371c6"><uni-list class="data-v-907371c6" u-s="{{['d']}}" u-i="907371c6-1,907371c6-0" bind:__l="__l"><uni-list-item wx:for="{{b}}" wx:for-item="item" wx:key="e" u-s="{{['body']}}" class="ss-radius-20 ss-m-b-20 data-v-907371c6" style="background-color:#fff;overflow:hidden" u-i="{{item.f}}" bind:__l="__l"><view slot="body"><view class="ss-flex-col data-v-907371c6"><view class="contract-text title data-v-907371c6">{{item.a}}</view><view class="contract-text data-v-907371c6"> 创建时间：{{item.b}}</view></view><view class="ss-m-t-20 ss-flex ss-flex-wrap ss-col-center data-v-907371c6" style="gap:20rpx"><button class="tool-btn ss-reset-button data-v-907371c6" catchtap="{{item.c}}"> 详情 </button><button class="tool-btn ss-reset-button data-v-907371c6" catchtap="{{item.d}}"> 删除 </button></view></view></uni-list-item></uni-list></view><uni-load-more wx:if="{{c}}" class="data-v-907371c6" bindtap="{{d}}" u-i="907371c6-3,907371c6-0" bind:__l="__l" u-p="{{e}}"/><s-empty wx:if="{{f}}" class="data-v-907371c6" u-i="907371c6-4,907371c6-0" bind:__l="__l" u-p="{{g}}"/></s-layout>