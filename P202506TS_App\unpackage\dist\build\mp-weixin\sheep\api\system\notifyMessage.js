"use strict";const e=require("../../request/index.js"),s={getNotifyMessagePage:s=>e.request({url:"/system/notify-message/page",method:"GET",params:s}),getMyNotifyMessagePage:s=>e.request({url:"/system/notify-message/my-page",method:"GET",params:s}),getNotifyMessage:s=>e.request({url:"/system/notify-message/get?id="+s,method:"GET"}),updateNotifyMessageRead:s=>e.request({url:"/system/notify-message/update-read",method:"PUT",params:{ids:s}}),updateNotifyMessage:()=>e.request({url:"/system/notify-message/update-all-read",method:"PUT"}),getUnreadNotifyMessageList:()=>e.request({url:"/system/notify-message/get-unread-list",method:"GET"}),getUnreadNotifyMessageCount:()=>e.request({url:"/system/notify-message/get-unread-count",method:"GET"})};exports.NotifyMessageApi=s;
