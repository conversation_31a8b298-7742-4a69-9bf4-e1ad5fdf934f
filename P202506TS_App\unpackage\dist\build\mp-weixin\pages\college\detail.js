"use strict";const e=require("../../common/vendor.js");require("../../sheep/index.js");const o=require("../../sheep/helper/index.js"),s=require("../../sheep/api/college/course.js");if(!Array){(e.resolveComponent("su-video")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-video/su-video.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"detail",setup(t){const i=e.reactive({id:0,course:{},video:null,styles:{height:200}});return e.onLoad((e=>{i.id=e.id,async function(e){try{const{code:o,data:t}=await s.CollegeCourseApi.getEnterpriseCourse(e);0===o&&(i.course=t,i.video={videoUrl:t.fileUrl,posterUrl:t.thumbnailUrl,autoplay:!1},console.log("state.video",i.video),console.log("state.video.videoUrl",i.video.videoUrl))}catch(o){console.error("获取课程详情失败:",o)}}(i.id)})),(s,t)=>e.e({a:i.video},i.video?{b:e.p({uid:e.unref(o.guid)(),src:i.video.videoUrl,poster:i.video.posterUrl,height:2*i.styles.height,autoplay:i.video.autoplay})}:{},{c:e.t(i.course.title),d:i.course.description,e:e.p({navbar:"normal",leftWidth:40,rightWidth:100})})}},i=e._export_sfc(t,[["__scopeId","data-v-a10f06d7"]]);wx.createPage(i);
