"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),o=require("../../api/product/spu.js"),i=require("../../api/promotion/point.js"),a=require("../../util/const.js");if(!Array){t.resolveComponent("s-goods-column")()}Math;const d={__name:"s-point-block",props:{data:{type:Object,default(){}},styles:{type:Object,default(){}}},setup(d){const s="oneColBigImg",r="twoCol",n="oneColSmallImg",u=t.reactive({spuList:[],leftSpuList:[],rightSpuList:[]}),p=d,{layoutType:l,btnBuy:c,activityIds:g}=p.data||{},{marginLeft:f,marginRight:m}=p.styles||{},b=t.computed((()=>"text"===c.type?{background:`linear-gradient(to right, ${c.bgBeginColor}, ${c.bgEndColor})`}:"img"===c.type?{width:"54rpx",height:"54rpx",background:`url(${e.sheep.$url.cdn(c.imgUrl)}) no-repeat`,backgroundSize:"100% 100%"}:void 0));let y=0,h=0,v=0;function L(t=0,e="left"){u.spuList[y]&&("left"===e&&(h+=t),"right"===e&&(v+=t),h<=v?u.leftSpuList.push(u.spuList[y]):u.rightSpuList.push(u.spuList[y]),y++)}async function x(t){const{data:e}=await o.SpuApi.getSpuDetail(t);return e}return t.onMounted((async()=>{const t=await async function(t){const{data:e}=await i.PointApi.getPointActivityListByIds(t);return e}(g.join(","));for(const e of t)u.spuList.push(await x(e.spuId));t.forEach((t=>{const e=u.spuList.find((e=>t.spuId===e.id));e&&(e.pointStock=t.stock,e.pointTotalStock=t.totalStock,e.point=t.point,e.pointPrice=t.price,e.activityId=t.id,e.activityType=a.PromotionActivityTypeEnum.POINT.type)})),l===r&&L()})),(o,i)=>t.e({a:t.unref(l)===s&&u.spuList.length},t.unref(l)===s&&u.spuList.length?{b:t.f(u.spuList,((o,i,a)=>{var s;return{a:t.o((i=>t.unref(e.sheep).$router.go("/pages/goods/point",{id:o.activityId})),o.id),b:"d2031aef-0-"+a,c:t.p({size:"sl",goodsFields:d.data.fields,tagStyle:d.data.badge,data:o,titleColor:null==(s=d.data.fields.name)?void 0:s.color,subTitleColor:d.data.fields.introduction.color,topRadius:d.data.borderRadiusTop,bottomRadius:d.data.borderRadiusBottom}),d:o.id}})),c:t.t("text"===t.unref(c).type?t.unref(c).text:""),d:t.s(b.value),e:t.s({marginBottom:2*d.data.space+"rpx"})}:{},{f:t.unref(l)===n&&u.spuList.length},t.unref(l)===n&&u.spuList.length?{g:t.f(u.spuList,((o,i,a)=>{var s;return{a:t.o((i=>t.unref(e.sheep).$router.go("/pages/goods/point",{id:o.activityId})),o.id),b:"d2031aef-1-"+a,c:t.p({size:"lg",goodsFields:d.data.fields,data:o,tagStyle:d.data.badge,titleColor:null==(s=d.data.fields.name)?void 0:s.color,subTitleColor:d.data.fields.introduction.color,topRadius:d.data.borderRadiusTop,bottomRadius:d.data.borderRadiusBottom}),d:o.id}})),h:t.t("text"===t.unref(c).type?t.unref(c).text:""),i:t.s(b.value),j:t.s({marginBottom:d.data.space+"px"})}:{},{k:t.unref(l)===r&&u.spuList.length},t.unref(l)===r&&u.spuList.length?{l:t.f(u.leftSpuList,((o,i,a)=>{var s;return{a:t.o((i=>t.unref(e.sheep).$router.go("/pages/goods/point",{id:o.activityId})),o.id),b:t.o((t=>L(t,"left")),o.id),c:"d2031aef-2-"+a,d:t.p({size:"md",goodsFields:d.data.fields,tagStyle:d.data.badge,data:o,titleColor:null==(s=d.data.fields.name)?void 0:s.color,subTitleColor:d.data.fields.introduction.color,topRadius:d.data.borderRadiusTop,bottomRadius:d.data.borderRadiusBottom,titleWidth:330-t.unref(f)-t.unref(m)}),e:o.id}})),m:t.t("text"===t.unref(c).type?t.unref(c).text:""),n:t.s(b.value),o:t.s({paddingRight:d.data.space+"rpx",marginBottom:d.data.space+"px"}),p:t.f(u.rightSpuList,((o,i,a)=>{var s;return{a:t.o((i=>t.unref(e.sheep).$router.go("/pages/goods/point",{id:o.activityId})),o.id),b:t.o((t=>L(t,"right")),o.id),c:"d2031aef-3-"+a,d:t.p({size:"md",goodsFields:d.data.fields,tagStyle:d.data.badge,data:o,titleColor:null==(s=d.data.fields.name)?void 0:s.color,subTitleColor:d.data.fields.introduction.color,topRadius:d.data.borderRadiusTop,bottomRadius:d.data.borderRadiusBottom,titleWidth:330-t.unref(f)-t.unref(m)}),e:o.id}})),q:t.t("text"===t.unref(c).type?t.unref(c).text:""),r:t.s(b.value),s:t.s({paddingLeft:d.data.space+"rpx",marginBottom:d.data.space+"px"})}:{})}},s=t._export_sfc(d,[["__scopeId","data-v-d2031aef"]]);wx.createComponent(s);
