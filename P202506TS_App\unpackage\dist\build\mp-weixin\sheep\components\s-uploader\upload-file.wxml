<view class="uni-file-picker__files"><view wx:if="{{a}}" class="files-button" bindtap="{{b}}"><slot></slot></view><view wx:if="{{c}}" class="uni-file-picker__lists is-text-box" style="{{f}}"><view wx:for="{{d}}" wx:for-item="item" wx:key="h" class="{{['uni-file-picker__lists-box', item.i && 'files-border']}}" style="{{item.j}}"><view class="uni-file-picker__item"><view class="files__name">{{item.a}}</view><view wx:if="{{e}}" class="icon-del-box icon-files" bindtap="{{item.b}}"><view class="icon-del icon-files"></view><view class="icon-del rotate"></view></view></view><view wx:if="{{item.c}}" class="file-picker__progress"><progress class="file-picker__progress-item" percent="{{item.d}}" stroke-width="4" backgroundColor="{{item.e}}"/></view><view wx:if="{{item.f}}" class="file-picker__mask" catchtap="{{item.g}}"> 点击重试 </view></view></view></view>