"use strict";const e=require("../../common/vendor.js"),l=require("../../sheep/index.js"),o=require("../../sheep/api/member/contractSign.js"),a=require("../../sheep/api/member/enterprisePartner.js");if(!Array){(e.resolveComponent("uni-number-box")+e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-data-select")+e.resolveComponent("uni-easyinput")+e.resolveComponent("uni-forms")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-number-box/components/uni-number-box/uni-number-box.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-data-select/components/uni-data-select/uni-data-select.js")+(()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t="color:#BBBBBB;font-size:28rpx;line-height:normal",r={__name:"detail",setup(r){const s=e.reactive({id:"",model:{},rules:{}}),d=[{value:"甲方",text:" 甲方"},{value:"乙方",text:"乙方"},{value:"丙方",text:"丙方"},{value:"丁方",text:"丁方"},{value:"戊方",text:"戊方"},{value:"己方",text:"己方"}],n=[{value:"企业",text:" 企业"},{value:"代理人",text:"代理人"}],i=[{value:"身份证",text:" 身份证"},{value:"护照",text:" 护照"},{value:"营业执照",text:" 营业执照"},{value:"组织机构代码证",text:" 组织机构代码证"},{value:"其他",text:"其他"}],p=e.ref([]);async function u(){1!==s.model.cwfxStatus?(await o.ContractSignApi.updateContractSign(s.model),m()):l.sheep.$helper.toast("已开通")}e.computed((()=>e.index.getStorageSync("enterprise")));const m=async()=>{const{data:l}=await o.ContractSignApi.getContractSign(s.id);if(s.model=e.clone(l),l.partnerId)try{const e=await a.EnterprisePartnerApi.getEnterprisePartner(l.partnerId);console.log("partner",e),0===e.code&&(p.value=[{value:e.data.id,text:e.data.name}])}catch(t){console.error("获取合作方信息失败",t)}};return e.onLoad((e=>{s.id=e.id,s.type=e.type,s.disable="detail"===e.type,m()})),(l,o)=>e.e({a:e.o((e=>s.model.seqNo=e)),b:e.p({min:1,max:6,placeholder:"序号",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clearable:!1,modelValue:s.model.seqNo}),c:e.p({name:"seqNo",label:"序号"}),d:e.o((e=>s.model.seqLabel=e)),e:e.p({localdata:d,placeholder:"标签",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clear:!1,modelValue:s.model.seqLabel}),f:e.p({name:"seqLabel",label:"标签"}),g:e.o((e=>s.model.signType=e)),h:e.p({localdata:n,placeholder:"签署类别",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clear:!1,modelValue:s.model.signType}),i:e.p({name:"signType",label:"签署类别"}),j:e.o((e=>s.model.fullName=e)),k:e.p({placeholder:"全称",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clearable:!1,modelValue:s.model.fullName}),l:e.p({name:"fullName",label:"全称"}),m:e.o((e=>s.model.userName=e)),n:e.p({placeholder:"姓名",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clearable:!1,modelValue:s.model.userName}),o:e.p({name:"userName",label:"姓名"}),p:e.o((e=>s.model.userMobile=e)),q:e.p({placeholder:"手机号",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clearable:!1,modelValue:s.model.userMobile}),r:e.p({name:"userMobile",label:"手机号"}),s:e.o((e=>s.model.certType=e)),t:e.p({localdata:i,placeholder:"证件类型",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clear:!1,modelValue:s.model.certType}),v:e.p({name:"certType",label:"证件类型"}),w:e.o((e=>s.model.certNo=e)),x:e.p({placeholder:"证件号码",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clearable:!1,modelValue:s.model.certNo}),y:e.p({name:"certNo",label:"证件号码"}),z:e.o((e=>s.model.partnerId=e)),A:e.p({localdata:p.value,placeholder:"合作方",inputBorder:!1,disabled:s.disable,styles:{disableColor:"#fff"},placeholderStyle:t,clear:!1,modelValue:s.model.partnerId}),B:e.p({name:"partnerId",label:"合作方"}),C:e.p({model:s.model,rules:s.rules,labelPosition:"left",border:!0}),D:!s.disable},s.disable?{}:{E:e.o(u),F:e.p({bottom:!0,placeholder:!0,bg:"none"})},{G:e.p({title:"合同签署方详情"})})}},s=e._export_sfc(r,[["__scopeId","data-v-2c634ac3"]]);wx.createPage(s);
