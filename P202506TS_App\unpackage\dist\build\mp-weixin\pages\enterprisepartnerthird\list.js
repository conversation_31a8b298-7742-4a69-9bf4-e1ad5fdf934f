"use strict";const t=require("../../common/vendor.js"),e=require("../../sheep/index.js"),n=require("../../sheep/util/index.js"),o=require("../../sheep/api/member/enterprisePartnerThird.js");if(require("../../sheep/config/index.js"),!Array){(t.resolveComponent("uni-list-item")+t.resolveComponent("uni-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-empty")+t.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const a={__name:"list",setup(a){const i=t.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:6},contractId:"",partnerId:"",keyword:"",loadStatus:""});function r(t){i.keyword=t,n.resetPagination(i.pagination),s()}async function s(){i.loadStatus="loading";const e={pageNo:i.pagination.pageNo,pageSize:i.pagination.pageSize,contractId:i.contractId,enterpriseId:i.partnerId,keyword:i.keyword},{code:n,data:a}=i.contractId?await o.EnterprisePartnerThirdApi.getPageByContactId(e):await o.EnterprisePartnerThirdApi.getSignatoryThirdPage(e);0===n&&(i.pagination.list=t.lodash.concat(i.pagination.list,a.list),i.pagination.total=a.total,i.loadStatus=i.pagination.list.length<i.pagination.total?"more":"noMore")}function p(){"noMore"!==i.loadStatus&&(i.pagination.pageNo++,s())}return t.onLoad((t=>{i.contractId=t.contractId,i.partnerId=t.partnerId,i.keyword=t.keyword,s()})),t.onReachBottom((()=>{p()})),t.onPullDownRefresh((()=>{s(),setTimeout((function(){t.index.stopPullDownRefresh()}),800)})),(a,s)=>t.e({a:i.pagination.total>0},i.pagination.total>0?{b:t.f(i.pagination.list,((a,i,s)=>({a:t.t(a.checkMethod),b:t.t(a.fileType),c:t.t(t.unref(n.formatDate)(a.createTime,"YYYY.MM.DD")),d:t.o((t=>(async t=>{e.sheep.$router.go("/pages/enterprisepartnerthird/reportList",{id:t})})(a.id)),i),e:t.o((n=>(async n=>{t.index.showModal({title:"提示",content:"确认删除此第三方风险检测报告吗？",success:async function(t){if(!t.confirm)return;const{code:a}=await o.EnterprisePartnerThirdApi.deleteEnterprisePartnerThird(n);0===a&&(e.sheep.$helper.toast("删除成功"),await r())}})})(a.id)),i),f:i,g:"1546888b-2-"+s+",1546888b-1"})))}:{},{c:i.pagination.total>0},i.pagination.total>0?{d:t.o(p),e:t.p({status:i.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{f:0===i.pagination.total},0===i.pagination.total?{g:t.p({icon:t.unref(e.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无第三方风险检测报告"})}:{},{h:t.p({title:"第三方检测"})})}},i=t._export_sfc(a,[["__scopeId","data-v-1546888b"]]);wx.createPage(i);
