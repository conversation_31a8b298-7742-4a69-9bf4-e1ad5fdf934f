"use strict";const n=require("../../common/vendor.js"),s=require("../../sheep/index.js"),e=require("../../sheep/api/member/signin.js");if(!Array){(n.resolveComponent("s-empty")+n.resolveComponent("su-popup")+n.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/ui/su-popup/su-popup.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const i={__name:"sign",setup(i){n.useCssVars((s=>({"214cd1b8":n.unref(o)})));const o=s.sheep.$url.css("/static/img/shop/app/sign.png"),t=n.reactive({loading:!0,signInfo:{},signConfigList:[],maxDay:0,showModel:!1,signResult:{}});async function a(){const{code:n,data:s}=await e.SignInApi.createSignInRecord();0===n&&(t.showModel=!0,t.signResult=s,await p())}function g(){t.showModel=!1}async function p(){const{code:n,data:s}=await e.SignInApi.getSignInRecordSummary();0===n&&(t.signInfo=s,t.loading=!1)}return n.onReady((()=>{p(),async function(){const{code:n,data:s}=await e.SignInApi.getSignInConfigList();0===n&&(t.signConfigList=s,s.length>0&&(t.maxDay=s[s.length-1].day))}()})),(s,e)=>n.e({a:t.loading},t.loading?{b:n.p({icon:"/static/data-empty.png",text:"签到活动还未开始"})}:{},{c:t.loading},t.loading||t.loading?{}:n.e({e:n.t(t.signInfo.continuousDay),f:n.f(t.signConfigList,((s,e,i)=>({a:n.t(s.day),b:n.n((e===t.signConfigList.length?"reward":"")+" "+(t.signInfo.continuousDay>=s.day?"rewardTxt":"")),c:n.n((e+1===t.signConfigList.length?"reward":"")+" "+(t.signInfo.continuousDay>=s.day?"venusSelect":"")),d:n.t(s.point),e:n.n(t.signInfo.continuousDay>=s.day?"on":""),f:e}))),g:!t.signInfo.todaySignIn},t.signInfo.todaySignIn?{}:{h:n.o(a)},{i:n.t(t.signInfo.totalDay),j:n.t(t.maxDay)}),{d:!t.loading,k:t.signResult.point},t.signResult.point?{l:n.t(t.signResult.point)}:{},{m:t.signResult.experience},t.signResult.experience?{n:n.t(t.signResult.experience)}:{},{o:n.t(t.signResult.day),p:n.o(g),q:n.p({show:t.showModel,type:"center",round:"10",isMaskClick:!1}),r:n.s(s.__cssVars()),s:n.p({title:"签到有礼"})})}},o=n._export_sfc(i,[["__scopeId","data-v-0a844931"]]);wx.createPage(o);
