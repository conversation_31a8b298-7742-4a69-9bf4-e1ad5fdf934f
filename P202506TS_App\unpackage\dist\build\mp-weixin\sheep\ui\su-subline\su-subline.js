"use strict";const e=require("../../../common/vendor.js"),t={__name:"su-subline",props:{lineColor:{type:String,default:"#000"},borderType:{type:String,default:"dashed"},lineWidth:{type:Number,default:1},height:{type:[Number,String],default:"auto"},paddingType:{type:String,default:"none"}},setup(t){const r=t,o=e.computed((()=>({"border-top-width":`${r.lineWidth}px`,"border-top-color":r.lineColor,"border-top-style":r.borderType,margin:"none"===r.paddingType?"0":"0px 16px"})));return(r,p)=>({a:e.s(o.value),b:`${t.height}px`})}},r=e._export_sfc(t,[["__scopeId","data-v-bf2fb0d0"]]);wx.createComponent(r);
