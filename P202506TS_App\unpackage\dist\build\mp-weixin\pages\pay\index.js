"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),a=require("../../sheep/hooks/useGoods.js"),o=require("../../sheep/api/pay/order.js"),r=require("../../sheep/api/pay/channel.js"),s=require("../../sheep/platform/pay.js");if(!Array){e.resolveComponent("s-layout")()}Math;const n={__name:"index",setup(n){const p=e.computed((()=>t.sheep.$store("user").userWallet)),d=e.reactive({orderType:"goods",orderInfo:{},payStatus:0,payMethods:[],payment:""}),i=()=>{""!==d.payment?"wallet"===d.payment?e.index.showModal({title:"提示",content:"确定要支付吗?",success:function(e){e.confirm&&t.sheep.$platform.pay(d.payment,d.orderType,d.orderInfo.id)}}):t.sheep.$platform.pay(d.payment,d.orderType,d.orderInfo.id):t.sheep.$helper.toast("请选择支付方式")},u=e.computed((()=>{if(2===d.payStatus)return"该订单已支付";if(1===d.payStatus){const e=a.useDurationTime(d.orderInfo.expireTime);return e.ms<=0?(d.payStatus=-1,""):`剩余支付时间 ${e.h}:${e.m}:${e.s} `}return-2===d.payStatus?"未查询到支付单信息":""}));function l(e){d.payment=e.detail.value}async function y(t){const{data:a,code:n}=await o.PayOrderApi.getOrder(t,!0);0===n&&a?(d.orderInfo=a,function(){if(10===d.orderInfo.status||20===d.orderInfo.status)return d.payStatus=2,void e.index.showModal({title:"提示",content:"订单已支付",showCancel:!1,success:function(){s.goPayResult(d.orderInfo.id,d.orderType)}});30!==d.orderInfo.status?d.payStatus=1:d.payStatus=-1}(),await async function(){const{data:e,code:t}=await r.PayChannelApi.getEnableChannelCodeList(d.orderInfo.appId);if(0!==t)return;d.payMethods=s.getPayMethods(e),d.payMethods.find((e=>{if(e.value&&!e.disabled)return d.payment=e.value,!0}))}()):d.payStatus=-2}return e.onLoad((e=>{if("WechatOfficialAccount"===t.sheep.$platform.name&&"ios"===t.sheep.$platform.os&&!t.sheep.$platform.landingPage.includes("pages/pay/index"))return void location.reload();let a=e.id;e.orderType&&(d.orderType=e.orderType),y(a),t.sheep.$store("user").getWallet()})),(o,r)=>e.e({a:e.t(e.unref(a.fen2yuan)(d.orderInfo.price)),b:e.t(u.value),c:e.f(d.payMethods,((o,r,s)=>e.e({a:o.disabled},o.disabled?{b:e.unref(t.sheep).$url.static("/static/img/shop/pay/cod_disabled.png")}:{c:e.unref(t.sheep).$url.static(o.icon)},{d:e.t(o.title),e:"wallet"===o.value},"wallet"===o.value?{f:e.t(e.unref(a.fen2yuan)(p.value.balance))}:{},{g:o.value,h:o.disabled,i:d.payment===o.value,j:o.disabled?1:"",k:o.title}))),d:e.o(l),e:0===d.payStatus},0===d.payStatus||-1===d.payStatus?{}:{g:e.o(i),h:1!==d.payStatus,i:1!==d.payStatus?1:""},{f:-1===d.payStatus,j:e.p({title:"收银台"})})}},p=e._export_sfc(n,[["__scopeId","data-v-b280537f"]]);wx.createPage(p);
