"use strict";const a=require("../../../common/vendor.js"),o=require("../../../sheep/index.js"),t=require("../../../sheep/api/promotion/combination.js");if(!Array){(a.resolveComponent("s-goods-column")+a.resolveComponent("uni-load-more")+a.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/components/s-goods-column/s-goods-column.js")+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const e={__name:"list",setup(e){a.useCssVars((o=>({aec233b2:a.unref(m)})));const{safeAreaInsets:n,safeArea:s}=o.sheep.$platform.device,i=o.sheep.$platform.navbar,r=2*o.sheep.$platform.device.statusBarHeight,p=2*(s.height+n.bottom)+r-i-350,m=o.sheep.$url.css("/static/img/shop/goods/groupon-header.png"),u=a.reactive({pagination:{list:[],total:0,pageNo:1,pageSize:10},loadStatus:"",summaryData:{}});async function g(){u.loadStatus="loading";const{data:a}=await t.CombinationApi.getCombinationActivityPage({pageNo:u.pagination.pageNo,pageSize:u.pagination.pageSize});a.list.forEach((a=>{u.pagination.list.push({...a,price:a.combinationPrice})})),u.pagination.total=a.total,u.loadStatus=u.pagination.list.length<u.pagination.total?"more":"noMore"}function l(){"noMore"!==u.loadStatus&&(u.pagination.pageNo++,g())}return a.onReachBottom((()=>l())),a.onLoad((()=>{(async()=>{const{data:a}=await t.CombinationApi.getCombinationRecordSummary();u.summaryData=a})(),g()})),(t,e)=>a.e({a:a.s({marginTop:"-"+Number(r+88)+"rpx"}),b:a.f(u.summaryData.avatars,((o,t,e)=>a.e({a:a.s(`background-image: url(${o})`),b:6===t&&u.summaryData.avatars.length>3},(6===t&&u.summaryData.avatars.length,{}),{c:t,d:a.s(6===t?"position: relative":"position: static")}))),c:a.t(u.summaryData.userCount||0),d:a.f(u.pagination.list,((t,e,n)=>({a:a.o((e=>a.unref(o.sheep).$router.go("/pages/goods/groupon",{id:t.id})),t.id),b:"67f995d3-1-"+n+",67f995d3-0",c:a.p({size:"lg",data:t,grouponTag:!0}),d:t.id}))),e:u.pagination.total>0},u.pagination.total>0?{f:a.o(l),g:a.p({status:u.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{h:p+"rpx",i:a.s(t.__cssVars()),j:a.p({bgStyle:{color:"#FE832A"},navbar:"inner"})})}},n=a._export_sfc(e,[["__scopeId","data-v-67f995d3"]]);wx.createPage(n);
