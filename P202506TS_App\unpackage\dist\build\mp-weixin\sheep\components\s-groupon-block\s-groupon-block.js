"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),o=require("../../api/promotion/combination.js"),a=require("../../api/product/spu.js");if(!Array){t.resolveComponent("s-goods-column")()}Math;const i={__name:"s-groupon-block",props:{data:{type:Object,default(){}},styles:{type:Object,default(){}}},setup(i){const d="oneColBigImg",s="twoCol",r="oneColSmallImg",u=t.reactive({spuList:[],leftSpuList:[],rightSpuList:[]}),n=i,{layoutType:p,btnBuy:l,activityIds:c}=n.data||{},{marginLeft:g,marginRight:f}=n.styles||{},m=t.computed((()=>"text"===l.type?{background:`linear-gradient(to right, ${l.bgBeginColor}, ${l.bgEndColor})`}:"img"===l.type?{width:"54rpx",height:"54rpx",background:`url(${e.sheep.$url.cdn(l.imgUrl)}) no-repeat`,backgroundSize:"100% 100%"}:void 0));let b=0,h=0,y=0;function v(t=0,e="left"){u.spuList[b]&&("left"===e&&(h+=t),"right"===e&&(y+=t),h<=y?u.leftSpuList.push(u.spuList[b]):u.rightSpuList.push(u.spuList[b]),b++)}async function L(t){const{data:e}=await a.SpuApi.getSpuDetail(t);return e}return t.onMounted((async()=>{const t=await async function(t){const{data:e}=await o.CombinationApi.getCombinationActivityListByIds(t);return e}(c.join(","));for(const e of t)u.spuList.push(await L(e.spuId));t.forEach((t=>{const e=t.combinationPrice||1/0,o=u.spuList.find((e=>t.spuId===e.id));o&&(o.name=t.name,o.price=Math.min(e,o.price),o.activityId=t.id)})),p===s&&v()})),(o,a)=>t.e({a:t.unref(p)===d&&u.spuList.length},t.unref(p)===d&&u.spuList.length?{b:t.f(u.spuList,((o,a,d)=>{var s;return{a:t.o((a=>t.unref(e.sheep).$router.go("/pages/goods/groupon",{id:o.activityId})),o.id),b:"6c5851a7-0-"+d,c:t.p({size:"sl",goodsFields:i.data.fields,tagStyle:i.data.badge,data:o,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom}),d:o.id}})),c:t.t("text"===t.unref(l).type?t.unref(l).text:""),d:t.s(m.value),e:t.s({marginBottom:2*i.data.space+"rpx"})}:{},{f:t.unref(p)===r&&u.spuList.length},t.unref(p)===r&&u.spuList.length?{g:t.f(u.spuList,((o,a,d)=>{var s;return{a:t.o((a=>t.unref(e.sheep).$router.go("/pages/goods/groupon",{id:o.activityId})),o.id),b:"6c5851a7-1-"+d,c:t.p({size:"lg",goodsFields:i.data.fields,data:o,tagStyle:i.data.badge,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom}),d:o.id}})),h:t.t("text"===t.unref(l).type?t.unref(l).text:""),i:t.s(m.value),j:t.s({marginBottom:i.data.space+"px"})}:{},{k:t.unref(p)===s&&u.spuList.length},t.unref(p)===s&&u.spuList.length?{l:t.f(u.leftSpuList,((o,a,d)=>{var s;return{a:t.o((a=>t.unref(e.sheep).$router.go("/pages/goods/groupon",{id:o.activityId})),o.id),b:t.o((t=>v(t,"left")),o.id),c:"6c5851a7-2-"+d,d:t.p({size:"md",goodsFields:i.data.fields,tagStyle:i.data.badge,data:o,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom,titleWidth:330-t.unref(g)-t.unref(f)}),e:o.id}})),m:t.t("text"===t.unref(l).type?t.unref(l).text:""),n:t.s(m.value),o:t.s({paddingRight:i.data.space+"rpx",marginBottom:i.data.space+"px"}),p:t.f(u.rightSpuList,((o,a,d)=>{var s;return{a:t.o((a=>t.unref(e.sheep).$router.go("/pages/goods/groupon",{id:o.activityId})),o.id),b:t.o((t=>v(t,"right")),o.id),c:"6c5851a7-3-"+d,d:t.p({size:"md",goodsFields:i.data.fields,tagStyle:i.data.badge,data:o,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom,titleWidth:330-t.unref(g)-t.unref(f)}),e:o.id}})),q:t.t("text"===t.unref(l).type?t.unref(l).text:""),r:t.s(m.value),s:t.s({paddingLeft:i.data.space+"rpx",marginBottom:i.data.space+"px"})}:{})}},d=t._export_sfc(i,[["__scopeId","data-v-6c5851a7"]]);wx.createComponent(d);
