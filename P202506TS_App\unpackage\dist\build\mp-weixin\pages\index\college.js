"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),o=require("../../sheep/api/college/course.js");if(!Array){(e.resolveComponent("s-empty")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const a={__name:"college",setup(a){e.useCssVars((t=>({"187ea91a":e.unref(s)})));const n=0*t.sheep.$platform.device.statusBarHeight*2,s=t.sheep.$url.css("/assets/mp/index/bg_header.png"),i=e.computed((()=>{var e;return null==(e=t.sheep.$store("app").template)?void 0:e.home})),{safeArea:r}=t.sheep.$platform.device;e.computed((()=>r.height-44-50));const p=e.reactive({pagination:{list:[],total:0,pageNo:1,pageSize:5},loadStatus:"",deleteOrderId:0});async function l(){p.loadStatus="loading";try{const{code:t,data:a}=await o.CollegeCourseApi.getEnterpriseCoursePage({pageNo:p.pagination.pageNo,pageSize:p.pagination.pageSize});if(0!==t)return void(p.loadStatus="noMore");p.pagination.list=e.lodash.concat(p.pagination.list,a.list||[]),p.pagination.total=a.total||0,p.loadStatus=p.pagination.list.length<p.pagination.total?"more":"noMore"}catch(t){console.error("获取课程列表失败:",t),p.loadStatus="noMore"}}function u(){"noMore"!==p.loadStatus&&(p.pagination.pageNo++,l())}return e.onLoad((e=>{l()})),e.onReachBottom((()=>{u()})),e.onPullDownRefresh((()=>{l(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),(o,a)=>e.e({a:e.s({marginTop:Number(n)+"rpx"}),b:0===p.pagination.total},0===p.pagination.total?{c:e.p({icon:e.unref(t.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无课程"})}:{},{d:p.pagination.total>0},p.pagination.total>0?{e:e.f(p.pagination.list,((o,a,n)=>({a:o.thumbnailUrl,b:e.t(o.title),c:o.id,d:e.o((a=>e.unref(t.sheep).$router.go("/pages/college/detail",{id:o.id})),o.id)})))}:{},{f:p.pagination.total>0},p.pagination.total>0?{g:e.o(u),h:e.p({status:p.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{i:e.s(o.__cssVars()),j:e.p({title:"商学院",tabbar:"/pages/index/college",navbar:"custom",bgStyle:i.value.page,navbarStyle:i.value.navigationBar,onShareAppMessage:!0})})}},n=e._export_sfc(a,[["__scopeId","data-v-47b59174"]]);a.__runtimeHooks=2,wx.createPage(n);
