"use strict";const e=require("../../../common/vendor.js"),s=require("../../../sheep/index.js"),r=require("../../../sheep/api/trade/afterSale.js"),t=require("../../../sheep/api/trade/delivery.js");if(!Array){e.resolveComponent("s-layout")()}Math;const i={__name:"return-delivery",setup(i){const a=e.reactive({id:0,expressIndex:0,expresses:[]});function d(e){a.expressIndex=e.detail.value}async function o(t){let i={id:a.id,logisticsId:a.expresses[a.expressIndex].id,logisticsNo:t.detail.value.logisticsNo};const{code:d}=await r.AfterSaleApi.deliveryAfterSale(i);0===d&&(e.index.showToast({title:"填写退货成功"}),s.sheep.$router.go("/pages/order/aftersale/detail",{id:a.id}))}return e.onLoad((e=>{e.id?(a.id=e.id,async function(){const{code:e,data:s}=await t.DeliveryApi.getDeliveryExpressList();0===e&&(a.expresses=s)}()):s.sheep.$helper.toast("缺少订单信息，请检查")})),(s,r)=>e.e({a:a.expresses.length>0},a.expresses.length>0?{b:e.t(a.expresses[a.expressIndex].name),c:e.o(d),d:a.expressIndex,e:a.expresses}:{},{f:e.o(o),g:e.p({title:"退货物流"})})}},a=e._export_sfc(i,[["__scopeId","data-v-dea8d4f9"]]);wx.createPage(a);
