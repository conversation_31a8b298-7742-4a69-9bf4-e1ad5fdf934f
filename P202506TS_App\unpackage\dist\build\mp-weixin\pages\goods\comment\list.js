"use strict";const t=require("../../../common/vendor.js"),a=require("../../../sheep/api/product/comment.js");if(!Array){(t.resolveComponent("su-tabs")+t.resolveComponent("s-empty")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/ui/su-tabs/su-tabs.js")+e+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const e=()=>"../components/detail/comment-item.js",o={__name:"list",setup(e){const o=t.reactive({id:0,type:[{type:0,name:"全部"},{type:1,name:"好评"},{type:2,name:"中评"},{type:3,name:"差评"}],currentTab:0,loadStatus:"",pagination:{list:[],total:0,pageNo:1,pageSize:8}});function n(t){o.currentTab=t.index,o.pagination.pageNo=1,o.pagination.list=[],o.pagination.total=0,i()}async function i(){o.loadStatus="loading";let e=await a.CommentApi.getCommentPage(o.id,o.pagination.pageNo,o.pagination.pageSize,o.type[o.currentTab].type);0===e.code&&(o.pagination.list=t.lodash.concat(o.pagination.list,e.data.list),o.pagination.total=e.data.total,o.loadStatus=o.pagination.list.length<o.pagination.total?"more":"noMore")}function p(){"noMore"!==o.loadStatus&&(o.pagination.pageNo++,i())}return t.onLoad((t=>{o.id=t.id,i()})),t.onReachBottom((()=>{p()})),(a,e)=>t.e({a:t.o(n),b:t.p({list:o.type,scrollable:!1,current:o.currentTab}),c:t.f(o.pagination.list,((a,e,o)=>({a:"fef98b1e-2-"+o+",fef98b1e-0",b:t.p({item:a}),c:a}))),d:0===o.pagination.total},0===o.pagination.total?{e:t.p({text:"暂无数据",icon:"/static/data-empty.png"})}:{},{f:o.pagination.total>0},o.pagination.total>0?{g:t.o(p),h:t.p({"icon-type":"auto",status:o.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{i:t.p({title:"全部评论"})})}},n=t._export_sfc(o,[["__scopeId","data-v-fef98b1e"]]);wx.createPage(n);
