<view class="menu-list-wrap"><uni-list wx:if="{{c}}" u-s="{{['d']}}" u-i="d1954d0c-0" bind:__l="__l" u-p="{{c}}"><uni-list-item wx:for="{{a}}" wx:for-item="item" wx:key="g" u-s="{{['header','footer']}}" bindtap="{{item.h}}" u-i="{{item.i}}" bind:__l="__l" u-p="{{b}}"><view class="ss-flex ss-col-center" slot="header"><image wx:if="{{item.a}}" class="list-icon" src="{{item.b}}" mode="aspectFit"></image><view class="title-text ss-flex ss-row-center ss-col-center ss-m-l-20" style="{{item.d}}">{{item.c}}</view></view><view class="notice-text ss-flex ss-row-center ss-col-center" style="{{item.f}}" slot="footer">{{item.e}}</view></uni-list-item></uni-list></view>