"use strict";const e=require("../../../common/vendor.js"),o=require("../../../sheep/index.js"),a=require("../../../sheep/validate/form.js"),i=require("../../../sheep/api/system/area.js"),r=require("../../../sheep/api/member/address.js");if(!Array){(e.resolveComponent("uni-easyinput")+e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-icons")+e.resolveComponent("su-switch")+e.resolveComponent("uni-forms")+e.resolveComponent("su-fixed")+e.resolveComponent("su-region-picker")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js")+(()=>"../../../sheep/ui/su-switch/su-switch.js")+(()=>"../../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../../sheep/ui/su-region-picker/su-region-picker.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const s={__name:"edit",setup(s){const t=e.ref(null),l=e.reactive({showRegion:!1,model:{name:"",mobile:"",detailAddress:"",defaultStatus:!1,areaName:""},rules:{}}),n={name:{rules:[{required:!0,errorMessage:"请输入收货人姓名"}]},mobile:a.mobile,detailAddress:{rules:[{required:!0,errorMessage:"请输入详细地址"}]},areaName:{rules:[{required:!0,errorMessage:"请选择您的位置"}]}},d=e=>{l.model.areaName=`${e.province_name} ${e.city_name} ${e.district_name}`,l.model.areaId=e.district_id,l.showRegion=!1},m=async()=>{if(!(await e.unref(t).validate().catch((e=>{console.log("error: ",e)}))))return;const a={...l.model},{code:i}=l.model.id>0?await r.AddressApi.updateAddress(a):await r.AddressApi.createAddress(a);0===i&&o.sheep.$router.back()},u=()=>{e.index.showModal({title:"提示",content:"确认删除此收货地址吗？",success:async function(e){if(!e.confirm)return;const{code:a}=await r.AddressApi.deleteAddress(l.model.id);0===a&&o.sheep.$router.back()}})};return e.onLoad((async o=>{if(e.lodash.isEmpty(e.index.getStorageSync("areaData"))&&i.AreaApi.getAreaTree().then((o=>{0===o.code&&e.index.setStorageSync("areaData",o.data)})),o.id){let{code:e,data:a}=await r.AddressApi.getAddress(o.id);if(0!==e)return;l.model=a}if(o.data){let a=JSON.parse(o.data);const i=(e,o)=>e.find((e=>e.name===o));let r=i(e.index.getStorageSync("areaData"),a.province_name),s=r?i(r.children,a.city_name):void 0,t=((s?i(s.children,a.district_name):void 0)||s||r).id;l.model={...l.model,areaId:t,areaName:[a.province_name,a.city_name,a.district_name].filter(Boolean).join(" "),defaultStatus:!1,detailAddress:a.address,mobile:a.mobile,name:a.consignee}}})),(o,a)=>e.e({a:e.o((e=>l.model.name=e)),b:e.p({placeholder:"请填写收货人姓名",inputBorder:!1,placeholderStyle:"color:#BBBBBB;font-size:30rpx;font-weight:400;line-height:normal",modelValue:l.model.name}),c:e.p({name:"name",label:"收货人"}),d:e.o((e=>l.model.mobile=e)),e:e.p({type:"number",placeholder:"请输入手机号",inputBorder:!1,placeholderStyle:"color:#BBBBBB;font-size:30rpx;font-weight:400;line-height:normal",modelValue:l.model.mobile}),f:e.p({name:"mobile",label:"手机号"}),g:e.p({type:"right"}),h:e.o((e=>l.model.areaName=e)),i:e.p({disabled:!0,inputBorder:!1,styles:{disableColor:"#fff",color:"#333"},placeholderStyle:"color:#BBBBBB;font-size:30rpx;font-weight:400;line-height:normal",placeholder:"请选择省市区",modelValue:l.model.areaName}),j:e.o((e=>l.showRegion=!0)),k:e.p({name:"areaName",label:"省市区"}),l:e.o((e=>l.model.detailAddress=e)),m:e.p({inputBorder:!1,type:"textarea",placeholderStyle:"color:#BBBBBB;font-size:30rpx;font-weight:400;line-height:normal",placeholder:"请输入详细地址",clearable:!0,modelValue:l.model.detailAddress}),n:e.p({name:"detailAddress",label:"详细地址",formItemStyle:{alignItems:"flex-start"},labelStyle:{lineHeight:"5em"}}),o:e.o((e=>l.model.defaultStatus=e)),p:e.p({modelValue:l.model.defaultStatus}),q:e.sr(t,"0736fb31-1,0736fb31-0",{k:"addressFormRef"}),r:e.o((e=>l.model=e)),s:e.p({rules:n,validateTrigger:"bind",labelWidth:"160",labelAlign:"left",border:!0,labelStyle:{fontWeight:"bold"},modelValue:l.model}),t:e.o(m),v:l.model.id},l.model.id?{w:e.o(u)}:{},{x:e.p({bottom:!0,opacity:!1,bg:"",placeholder:!0,noFixed:!1,index:10}),y:e.o((e=>l.showRegion=!1)),z:e.o(d),A:e.p({show:l.showRegion}),B:e.p({title:l.model.id?"编辑地址":"新增地址"})})}},t=e._export_sfc(s,[["__scopeId","data-v-0736fb31"]]);wx.createPage(t);
