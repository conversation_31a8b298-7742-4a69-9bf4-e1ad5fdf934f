"use strict";function t(t){return"function"==typeof Array.isArray?Array.isArray(t):"[object Array]"===Object.prototype.toString.call(t)}function r(t){return"[object Object]"===Object.prototype.toString.call(t)}exports.cloneDeep=function e(n){const o=t(n)?[...n]:{};if(r(n))for(const t in n)n[t]&&(n[t]&&"object"==typeof n[t]?o[t]=e(n[t]):o[t]=n[t]);return o},exports.isEmpty=function(e){return""===e||null==e||(t(e)?0===e.length:!!r(e)&&0===Object.keys(e).length)};
