"use strict";const t=require("../../common/vendor.js"),o=require("../../sheep/index.js"),a=require("../../sheep/util/index.js"),e=require("../../sheep/api/promotion/coupon.js");if(!Array){(t.resolveComponent("su-tabs")+t.resolveComponent("su-sticky")+t.resolveComponent("s-empty")+t.resolveComponent("s-coupon-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-coupon-list/s-coupon-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const n={__name:"list",setup(n){const i=t.reactive({currentTab:0,type:"1",pagination:{list:[],total:0,pageNo:1,pageSize:5},loadStatus:""}),s=[{name:"领券中心",value:"all"},{name:"已领取",value:"1"},{name:"已使用",value:"2"},{name:"已失效",value:"3"}];function p(t){i.currentTab=t.index,i.type=t.value,a.resetPagination(i.pagination),0===i.currentTab?u():l()}async function u(){i.loadStatus="loading";const{data:o,code:a}=await e.CouponApi.getCouponTemplatePage({pageNo:i.pagination.pageNo,pageSize:i.pagination.pageSize});0===a&&(i.pagination.list=t.lodash.concat(i.pagination.list,o.list),i.pagination.total=o.total,i.loadStatus=i.pagination.list.length<i.pagination.total?"more":"noMore")}async function l(){i.loadStatus="loading";const{data:o,code:a}=await e.CouponApi.getCouponPage({pageNo:i.pagination.pageNo,pageSize:i.pagination.pageSize,status:i.type});0===a&&(i.pagination.list=t.lodash.concat(i.pagination.list,o.list),i.pagination.total=o.total,i.loadStatus=i.pagination.list.length<i.pagination.total?"more":"noMore")}function r(){"noMore"!==i.loadStatus&&(i.pagination.pageNo++,0===i.currentTab?u():l())}return t.onLoad((t=>{"all"!==t.type&&t.type?("geted"===t.type?i.currentTab=1:"used"===t.type?i.currentTab=2:i.currentTab=3,i.type=i.currentTab,l()):u()})),t.onReachBottom((()=>{r()})),(n,l)=>t.e({a:t.o(p),b:t.p({list:s,scrollable:!1,current:i.currentTab}),c:t.p({bgColor:"#fff"}),d:0===i.pagination.total},0===i.pagination.total?{e:t.p({icon:"/static/coupon-empty.png",text:"暂无优惠券"})}:{},{f:0===i.currentTab},0===i.currentTab?{g:t.f(i.pagination.list,((n,s,p)=>({a:t.t(n.canTake?"立即领取":"已领取"),b:t.n(n.canTake?"":"border-btn"),c:t.o((o=>async function(o){const{code:n}=await e.CouponApi.takeCoupon(o);0===n&&(t.index.showToast({title:"领取成功"}),setTimeout((()=>{a.resetPagination(i.pagination),u()}),1e3))}(n.id)),n.id),d:!n.canTake,e:t.o((a=>t.unref(o.sheep).$router.go("/pages/coupon/detail",{id:n.id})),n.id),f:"03f22df9-4-"+p+",03f22df9-0",g:t.p({data:n}),h:n.id})))}:{h:t.f(i.pagination.list,((a,e,n)=>({a:t.t(1===a.status?"立即使用":2===a.status?"已使用":"已过期"),b:t.n(1!==a.status?"disabled-btn":""),c:1!==a.status,d:t.o((e=>t.unref(o.sheep).$router.go("/pages/coupon/detail",{couponId:a.id})),a.id),e:t.o((e=>t.unref(o.sheep).$router.go("/pages/coupon/detail",{couponId:a.id})),a.id),f:"03f22df9-5-"+n+",03f22df9-0",g:t.p({data:a,type:"user"}),h:a.id})))},{i:i.pagination.total>0},i.pagination.total>0?{j:t.o(r),k:t.p({status:i.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{l:t.p({bgStyle:{color:"#f2f2f2"},title:"优惠券"})})}},i=t._export_sfc(n,[["__scopeId","data-v-03f22df9"]]);wx.createPage(i);
