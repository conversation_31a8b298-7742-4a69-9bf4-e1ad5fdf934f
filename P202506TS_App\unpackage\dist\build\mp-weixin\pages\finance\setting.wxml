<s-layout wx:if="{{k}}" u-s="{{['d']}}" class="set-userinfo-wrap data-v-f1fa9768" u-i="f1fa9768-0" bind:__l="__l" u-p="{{k}}"><uni-forms wx:if="{{h}}" u-s="{{['d']}}" class="form-box data-v-f1fa9768" u-i="f1fa9768-1,f1fa9768-0" bind:__l="__l" u-p="{{h}}"><view class="bg-white ss-p-x-30 data-v-f1fa9768"><uni-forms-item wx:if="{{c}}" class="data-v-f1fa9768" u-s="{{['d']}}" u-i="f1fa9768-2,f1fa9768-1" bind:__l="__l" u-p="{{c}}"><uni-easyinput wx:if="{{b}}" class="data-v-f1fa9768" u-i="f1fa9768-3,f1fa9768-2" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"/></uni-forms-item><uni-forms-item wx:if="{{e}}" class="data-v-f1fa9768" u-s="{{['d']}}" u-i="f1fa9768-4,f1fa9768-1" bind:__l="__l" u-p="{{e}}"><view class="ss-flex ss-h-100 data-v-f1fa9768">{{d}}</view></uni-forms-item><uni-forms-item wx:if="{{g}}" class="data-v-f1fa9768" u-s="{{['d']}}" u-i="f1fa9768-5,f1fa9768-1" bind:__l="__l" u-p="{{g}}"><view class="ss-flex ss-h-100 data-v-f1fa9768">{{f}}</view></uni-forms-item></view></uni-forms><su-fixed wx:if="{{j}}" class="data-v-f1fa9768" u-s="{{['d']}}" u-i="f1fa9768-6,f1fa9768-0" bind:__l="__l" u-p="{{j}}"><view class="footer-box ss-p-20 data-v-f1fa9768"><button class="ss-rest-button logout-btn ui-Shadow-Main data-v-f1fa9768" bindtap="{{i}}">开 通</button></view></su-fixed></s-layout>