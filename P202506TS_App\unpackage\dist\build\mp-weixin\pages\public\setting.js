"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),o=require("../../sheep/api/member/auth.js");if(!Array){(e.resolveComponent("uni-list-item")+e.resolveComponent("uni-list")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const s={__name:"setting",setup(s){const r=e.computed((()=>t.sheep.$store("app").info)),i=e.computed((()=>t.sheep.$store("user").isLogin)),n=e.index.getStorageInfoSync().currentSize+"Kb";function u(){t.sheep.$platform.checkUpdate()}function l(){e.index.showModal({title:"提示",content:"确认注销账号？",success:async function(e){if(!e.confirm)return;const{code:s}=await o.AuthUtil.logout();0===s&&(t.sheep.$store("user").logout(),t.sheep.$router.go("/pages/index/user"))}})}function p(){e.index.showModal({title:"提示",content:"确认退出账号？",success:async function(e){if(!e.confirm)return;const{code:s}=await o.AuthUtil.logout();0===s&&(t.sheep.$store("user").logout(),t.sheep.$router.go("/pages/index/user"))}})}return e.reactive({showModal:!1}),(o,s)=>e.e({a:e.unref(t.sheep).$url.cdn(r.value.logo),b:e.t(r.value.name),c:e.o(u),d:e.p({title:"当前版本",rightText:r.value.version,showArrow:!0,clickable:!0,border:!1}),e:e.p({title:"本地缓存",rightText:n,showArrow:!0,border:!1}),f:e.o((o=>e.unref(t.sheep).$router.go("/pages/public/richtext",{title:"关于我们"}))),g:e.p({title:"关于我们",showArrow:!0,clickable:!0,border:!1}),h:i.value&&"ios"===e.unref(t.sheep).$platform.os&&"App"===e.unref(t.sheep).$platform.name},i.value&&"ios"===e.unref(t.sheep).$platform.os&&"App"===e.unref(t.sheep).$platform.name?{i:e.o(l),j:e.p({title:"注销账号",rightText:"",showArrow:!0,clickable:!0,border:!1})}:{},{k:e.p({border:!1}),l:e.o((o=>e.unref(t.sheep).$router.go("/pages/public/richtext",{title:"用户协议"}))),m:e.o((o=>e.unref(t.sheep).$router.go("/pages/public/richtext",{title:"隐私协议"}))),n:e.t(r.value.copyright),o:e.t(r.value.copytime),p:i.value},i.value?{q:e.o(p)}:{},{r:e.p({bottom:!0,placeholder:!0}),s:e.p({bgStyle:{color:"#fff"},title:"系统设置"})})}},r=e._export_sfc(s,[["__scopeId","data-v-e8e8d771"]]);wx.createPage(r);
