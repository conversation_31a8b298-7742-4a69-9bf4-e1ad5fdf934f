"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),n=require("../../sheep/hooks/useModal.js"),i=require("../../sheep/api/infra/file.js"),a=require("../../sheep/api/member/user.js");if(!Array){(e.resolveComponent("su-image")+e.resolveComponent("uni-easyinput")+e.resolveComponent("uni-forms-item")+e.resolveComponent("su-radio")+e.resolveComponent("uni-list-item")+e.resolveComponent("uni-list")+e.resolveComponent("uni-forms")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-image/su-image.js")+(()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../sheep/ui/su-radio/su-radio.js")+(()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"info",setup(t){const s=e.reactive({model:{},rules:{},thirdInfo:{}}),r=[{name:"男",value:"1"},{name:"女",value:"2"}],l=e.computed((()=>o.sheep.$store("user").userInfo));function u(e){s.model.sex=e.detail.value}const m=()=>{n.showAuthModal("changeMobile")};function p(e){!async function(e){if(!e)return;let{data:o}=await i.FileApi.uploadFile(e);s.model.avatar=o}(e.detail.avatarUrl||"")}async function c(){await o.sheep.$platform.useProvider("wechat").bind()&&await h()}function f(){e.index.showModal({title:"解绑提醒",content:"解绑后您将无法通过微信登录此账号",cancelText:"再想想",confirmText:"确定",success:async function(e){if(!e.confirm)return;await o.sheep.$platform.useProvider("wechat").unbind(s.thirdInfo.openid)&&await h()}})}async function d(){const{code:e}=await a.UserApi.updateUser({avatar:s.model.avatar,nickname:s.model.nickname,sex:s.model.sex});0===e&&await h()}const h=async()=>{const n=await o.sheep.$store("user").getInfo();if(s.model=e.clone(n),"H5"!==o.sheep.$platform.name){const e=await o.sheep.$platform.useProvider("wechat").getInfo();s.thirdInfo=e||{}}};return e.onBeforeMount((()=>{h()})),(n,i)=>{var a,t,h;return e.e({a:e.p({isPreview:!0,current:0,src:null==(a=s.model)?void 0:a.avatar,height:160,width:160,radius:80,mode:"scaleToFill"}),b:e.o(p),c:e.o((e=>s.model.nickname=e)),d:e.p({type:"nickname",placeholder:"设置昵称",inputBorder:!1,placeholderStyle:"color:#BBBBBB;font-size:28rpx;line-height:normal",modelValue:s.model.nickname}),e:e.p({name:"nickname",label:"昵称"}),f:e.f(r,((o,n,i)=>{var a;return{a:o.value,b:parseInt(o.value)===(null==(a=s.model)?void 0:a.sex),c:e.t(o.name),d:o.value}})),g:e.o(u),h:e.p({name:"sex",label:"性别"}),i:null==(t=l.value.verification)?void 0:t.mobile},(null==(h=l.value.verification)?void 0:h.mobile)?{j:e.p({modelValue:!0})}:{},{k:e.o((e=>l.value.mobile=e)),l:e.p({placeholder:"请绑定手机号",inputBorder:!1,disabled:!0,styles:{disableColor:"#fff"},placeholderStyle:"color:#BBBBBB;font-size:28rpx;line-height:normal",clearable:!1,modelValue:l.value.mobile}),m:e.o(m),n:e.p({name:"mobile",label:"手机号"})},{},{},{x:e.p({model:s.model,rules:s.rules,labelPosition:"left",border:!0}),y:"H5"!==e.unref(o.sheep).$platform.name},"H5"!==e.unref(o.sheep).$platform.name?e.e({z:"WechatOfficialAccount"===e.unref(o.sheep).$platform.name},"WechatOfficialAccount"===e.unref(o.sheep).$platform.name?{A:e.unref(o.sheep).$url.static("/static/img/shop/platform/WechatOfficialAccount.png")}:{},{B:"WechatMiniProgram"===e.unref(o.sheep).$platform.name},"WechatMiniProgram"===e.unref(o.sheep).$platform.name?{C:e.unref(o.sheep).$url.static("/static/img/shop/platform/WechatMiniProgram.png")}:{},{D:"App"===e.unref(o.sheep).$platform.name},"App"===e.unref(o.sheep).$platform.name?{E:e.unref(o.sheep).$url.static("/static/img/shop/platform/wechat.png")}:{},{F:s.thirdInfo},s.thirdInfo?{G:e.unref(o.sheep).$url.cdn(s.thirdInfo.avatar),H:e.t(s.thirdInfo.nickname)}:{},{I:s.thirdInfo.openid},s.thirdInfo.openid?{J:e.o(f)}:{K:e.o(c)}):{},{L:e.o(d),M:e.p({bottom:!0,placeholder:!0,bg:"none"}),N:e.p({title:"用户信息"})})}}},s=e._export_sfc(t,[["__scopeId","data-v-190a72a4"]]);wx.createPage(s);
