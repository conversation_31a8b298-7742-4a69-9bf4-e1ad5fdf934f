"use strict";const e=require("../../../common/vendor.js");if(!Array){(e.resolveComponent("s-search-block")+e.resolveComponent("s-notice-block")+e.resolveComponent("s-menu-button")+e.resolveComponent("s-menu-list")+e.resolveComponent("s-menu-grid")+e.resolveComponent("s-popup-image")+e.resolveComponent("s-float-menu")+e.resolveComponent("s-image-block")+e.resolveComponent("s-image-banner")+e.resolveComponent("s-title-block")+e.resolveComponent("s-image-cube")+e.resolveComponent("s-video-block")+e.resolveComponent("s-line-block")+e.resolveComponent("s-hotzone-block")+e.resolveComponent("s-goods-card")+e.resolveComponent("s-goods-shelves")+e.resolveComponent("s-groupon-block")+e.resolveComponent("s-seckill-block")+e.resolveComponent("s-point-block")+e.resolveComponent("s-live-block")+e.resolveComponent("s-coupon-block")+e.resolveComponent("s-richtext-block")+e.resolveComponent("s-user-card")+e.resolveComponent("s-order-card")+e.resolveComponent("s-wallet-card")+e.resolveComponent("s-coupon-card"))()}Math||((()=>"../s-search-block/s-search-block.js")+(()=>"../s-notice-block/s-notice-block.js")+(()=>"../s-menu-button/s-menu-button.js")+(()=>"../s-menu-list/s-menu-list.js")+(()=>"../s-menu-grid/s-menu-grid.js")+(()=>"../s-popup-image/s-popup-image.js")+(()=>"../s-float-menu/s-float-menu.js")+(()=>"../s-image-block/s-image-block.js")+(()=>"../s-image-banner/s-image-banner.js")+(()=>"../s-title-block/s-title-block.js")+(()=>"../s-image-cube/s-image-cube.js")+(()=>"../s-video-block/s-video-block.js")+(()=>"../s-line-block/s-line-block.js")+(()=>"../s-hotzone-block/s-hotzone-block.js")+(()=>"../s-goods-card/s-goods-card.js")+(()=>"../s-goods-shelves/s-goods-shelves.js")+(()=>"../s-groupon-block/s-groupon-block.js")+(()=>"../s-seckill-block/s-seckill-block.js")+(()=>"../s-point-block/s-point-block.js")+(()=>"../s-live-block/s-live-block.js")+(()=>"../s-coupon-block/s-coupon-block.js")+(()=>"../s-richtext-block/s-richtext-block.js")+(()=>"../s-user-card/s-user-card.js")+(()=>"../s-order-card/s-order-card.js")+(()=>"../s-wallet-card/s-wallet-card.js")+(()=>"../s-coupon-card/s-coupon-card.js"))();const t={__name:"s-block-item",props:{type:{type:String,default:""},data:{type:Object,default(){}},styles:{type:Object,default(){}}},setup:t=>(s,o)=>e.e({a:"SearchBar"===t.type},"SearchBar"===t.type?{b:e.p({data:t.data,styles:t.styles,navbar:!1})}:{},{c:"NoticeBar"===t.type},"NoticeBar"===t.type?{d:e.p({data:t.data})}:{},{e:"MenuSwiper"===t.type},"MenuSwiper"===t.type?{f:e.p({data:t.data,styles:t.styles})}:{},{g:"MenuList"===t.type},"MenuList"===t.type?{h:e.p({data:t.data})}:{},{i:"MenuGrid"===t.type},"MenuGrid"===t.type?{j:e.p({data:t.data,styles:t.styles})}:{},{k:"Popover"===t.type},"Popover"===t.type?{l:e.p({data:t.data})}:{},{m:"FloatingActionButton"===t.type},"FloatingActionButton"===t.type?{n:e.p({data:t.data})}:{},{o:"ImageBar"===t.type},"ImageBar"===t.type?{p:e.p({data:t.data,styles:t.styles})}:{},{q:"Carousel"===t.type},"Carousel"===t.type?{r:e.p({data:t.data,styles:t.styles})}:{},{s:"TitleBar"===t.type},"TitleBar"===t.type?{t:e.p({data:t.data,styles:t.styles})}:{},{v:"MagicCube"===t.type},"MagicCube"===t.type?{w:e.p({data:t.data,styles:t.styles})}:{},{x:"VideoPlayer"===t.type},"VideoPlayer"===t.type?{y:e.p({data:t.data,styles:t.styles})}:{},{z:"Divider"===t.type},"Divider"===t.type?{A:e.p({data:t.data})}:{},{B:"HotZone"===t.type},"HotZone"===t.type?{C:e.p({data:t.data,styles:t.styles})}:{},{D:"ProductCard"===t.type},"ProductCard"===t.type?{E:e.p({data:t.data,styles:t.styles})}:{},{F:"ProductList"===t.type},"ProductList"===t.type?{G:e.p({data:t.data,styles:t.styles})}:{},{H:"PromotionCombination"===t.type},"PromotionCombination"===t.type?{I:e.p({data:t.data,styles:t.styles})}:{},{J:"PromotionSeckill"===t.type},"PromotionSeckill"===t.type?{K:e.p({data:t.data,styles:t.styles})}:{},{L:"PromotionPoint"===t.type},"PromotionPoint"===t.type?{M:e.p({data:t.data,styles:t.styles})}:{},{N:"MpLive"===t.type},"MpLive"===t.type?{O:e.p({data:t.data,styles:t.styles})}:{},{P:"CouponCard"===t.type},"CouponCard"===t.type?{Q:e.p({data:t.data,styles:t.styles})}:{},{R:"PromotionArticle"===t.type},"PromotionArticle"===t.type?{S:e.p({data:t.data,styles:t.styles})}:{},{T:"UserCard"===t.type},"UserCard"===t.type?{U:e.p({data:t.data,styles:t.styles})}:{},{V:"UserOrder"===t.type},"UserOrder"===t.type?{W:e.p({data:t.data,styles:t.styles})}:{},{X:"UserWallet"===t.type},"UserWallet"===t.type?{Y:e.p({data:t.data,styles:t.styles})}:{},{Z:"UserCoupon"===t.type},"UserCoupon"===t.type?{aa:e.p({data:t.data,styles:t.styles})}:{})};wx.createComponent(t);
