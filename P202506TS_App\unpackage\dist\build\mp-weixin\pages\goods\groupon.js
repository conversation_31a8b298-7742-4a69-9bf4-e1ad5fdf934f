"use strict";const o=require("../../common/vendor.js"),e=require("../../sheep/index.js"),t=require("../../sheep/hooks/useGoods.js"),s=require("../../sheep/api/promotion/combination.js"),n=require("../../sheep/api/product/spu.js"),i=require("../../sheep/util/const.js");if(!Array){(o.resolveComponent("s-empty")+o.resolveComponent("su-swiper")+o.resolveComponent("s-select-groupon-sku")+o.resolveComponent("s-layout"))()}Math||(a+r+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/ui/su-swiper/su-swiper.js")+c+g+(()=>"../../sheep/components/s-select-groupon-sku/s-select-groupon-sku.js")+u+p+d+(()=>"../../sheep/components/s-layout/s-layout.js"))();const a=()=>"./components/detail/detail-navbar.js",c=()=>"./components/detail/detail-cell-sku.js",d=()=>"./components/detail/detail-tabbar.js",r=()=>"./components/detail/detail-skeleton.js",u=()=>"./components/detail/detail-comment-card.js",p=()=>"./components/detail/detail-content-card.js",g=()=>"./components/groupon/groupon-card-list.js",l={__name:"groupon",setup(a){o.useCssVars((e=>({"120fd2d6":o.unref(c),"601c09c6":o.unref(d),"977a3bfe":o.unref(r),"376d7242":o.unref(u)})));const c=e.sheep.$url.css("/static/img/shop/goods/groupon-bg.png"),d=e.sheep.$url.css("/static/img/shop/goods/groupon-btn.png"),r=e.sheep.$url.css("/static/img/shop/goods/activity-btn-disabled.png"),u=e.sheep.$url.css("/static/img/shop/goods/groupon-tip-bg.png");o.onPageScroll((()=>{}));const p=o.reactive({skeletonLoading:!0,goodsId:0,goodsInfo:{},goodsSwiper:[],showSelectSku:!1,selectedSku:{},activity:{},grouponId:0,grouponNum:0,grouponAction:"create",combinationHeadId:null}),g=o.computed((()=>t.useDurationTime(p.activity.endTime)));function l(o){p.selectedSku=o}function m(){p.showSelectSku=!1}function f(){p.grouponAction="create",p.grouponId=0,p.showSelectSku=!0}function I(o){p.grouponAction="join",p.grouponId=o.activityId,p.combinationHeadId=o.id,p.grouponNum=o.userSize,p.showSelectSku=!0}function y(o){e.sheep.$router.go("/pages/order/confirm",{data:JSON.stringify({order_type:"goods",combinationActivityId:p.activity.id,combinationHeadId:p.combinationHeadId,items:[{skuId:o.id,count:o.count}]})})}const h=o.computed((()=>o.isEmpty(p.activity)?{}:e.sheep.$platform.share.getShareInfo({title:p.activity.name,image:e.sheep.$url.cdn(p.goodsInfo.picUrl),params:{page:i.SharePageEnum.GROUPON.value,query:p.activity.id}},{type:"goods",title:p.activity.name,image:e.sheep.$url.cdn(p.goodsInfo.picUrl),price:t.fen2yuan(p.goodsInfo.price),marketPrice:t.fen2yuan(p.goodsInfo.marketPrice)})));return o.onLoad((async o=>{if(!o.id)return void(p.goodsInfo=null);p.grouponId=o.id;const{code:e,data:i}=await s.CombinationApi.getCombinationActivity(p.grouponId);p.activity=i;const{data:a}=await n.SpuApi.getSpuDetail(i.spuId);p.goodsId=a.id,a.price=i.products.reduce(((o,e)=>Math.min(o,e.combinationPrice||1/0)),1/0),a.skus.forEach((o=>{const e=i.products.find((e=>e.skuId===o.id));e?o.price=e.combinationPrice:o.stock=0})),p.skeletonLoading=!1,0===e?(p.goodsInfo=a,p.grouponNum=i.userSize,p.goodsSwiper=t.formatGoodsSwiper(p.goodsInfo.sliderPicUrls)):p.goodsInfo=null})),(s,n)=>o.e({a:p.skeletonLoading},p.skeletonLoading?{}:null===p.goodsInfo||0!==p.activity.status||p.activity.endTime<(new Date).getTime()?{c:o.o((t=>o.unref(e.sheep).$router.back())),d:o.p({text:"活动不存在或已结束",icon:"/static/soldout-empty.png",showAction:!0,actionText:"返回上一页"})}:o.e({e:o.p({isPreview:!0,list:p.goodsSwiper,dotStyle:"tag",imageMode:"widthFix",dotCur:"bg-mask-40",seizeHeight:750}),f:o.t(o.unref(t.fen2yuan)(p.activity.price||p.goodsInfo.price)),g:o.unref(e.sheep).$url.static("/static/img/shop/goods/groupon-tag.png"),h:p.goodsInfo.price},p.goodsInfo.price?{i:o.t(o.unref(t.fen2yuan)(p.goodsInfo.marketPrice))}:{},{j:g.value.ms>0},g.value.ms>0?{k:o.t(g.value.h),l:o.t(g.value.m),m:o.t(g.value.s)}:{},{n:o.t(p.goodsInfo.name),o:o.t(p.goodsInfo.introduction),p:o.o((o=>p.showSelectSku=!0)),q:o.p({sku:p.selectedSku}),r:o.o(I),s:o.o((o=>p.activity=o)),t:o.p({modelValue:p.activity}),v:o.o(y),w:o.o(l),x:o.o(m),y:o.p({show:p.showSelectSku,goodsInfo:p.goodsInfo,grouponAction:p.grouponAction,grouponNum:p.grouponNum}),z:o.p({goodsId:p.goodsId}),A:o.p({content:p.goodsInfo.description}),B:o.t(o.unref(t.fen2yuan)(p.goodsInfo.marketPrice)),C:o.o((t=>o.unref(e.sheep).$router.go("/pages/goods/index",{id:p.goodsInfo.id}))),D:o.t(o.unref(t.fen2yuan)(p.selectedSku.price*p.selectedSku.count||p.activity.price*p.selectedSku.count||p.goodsInfo.price*p.selectedSku.count||p.goodsInfo.price)),E:p.activity.startTime>(new Date).getTime()},p.activity.startTime>(new Date).getTime()||p.activity.endTime<=(new Date).getTime()?{}:o.e({G:0===p.goodsInfo.stock},(p.goodsInfo.stock,{})),{F:p.activity.endTime<=(new Date).getTime(),H:o.o(f),I:o.n(0===p.activity.status&&0!==p.goodsInfo.stock?"check-btn-box":"disabled-btn-box"),J:0===p.goodsInfo.stock||0!==p.activity.status,K:o.o((o=>p.goodsInfo=o)),L:o.p({modelValue:p.goodsInfo})}),{b:null===p.goodsInfo||0!==p.activity.status||p.activity.endTime<(new Date).getTime(),M:o.s(s.__cssVars()),N:o.p({onShareAppMessage:h.value,navbar:"goods"})})}},m=o._export_sfc(l,[["__scopeId","data-v-7811599b"]]);l.__runtimeHooks=3,wx.createPage(m);
