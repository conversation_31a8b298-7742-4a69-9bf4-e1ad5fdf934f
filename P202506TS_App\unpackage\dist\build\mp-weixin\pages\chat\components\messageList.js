"use strict";const e=require("../../../common/vendor.js"),a=require("../../../sheep/api/promotion/kefu.js"),t=require("../../../sheep/helper/utils.js"),o=require("../../../sheep/index.js"),s=require("../../../sheep/util/index.js");if(!Array){e.resolveComponent("z-paging")()}Math||i();const i=()=>"./messageListItem.js",r={__name:"messageList",setup(i,{expose:r}){const l=o.sheep.$platform.navbar,u=e.ref([]),n=e.ref(!1),c=e.ref(!1),m=e.reactive({width:"100px","background-color":"#fff","border-radius":"30px","box-shadow":"0 2px 4px rgba(0, 0, 0, 0.1)",display:"flex",justifyContent:"center",alignItems:"center"}),p=e.reactive({no:1,limit:20,createTime:void 0}),d=e.ref(null),f=async(e,a)=>{p.no=e,p.limit=a,await v()},v=async()=>{const{data:e}=await a.KeFuApi.getKefuMessageList(p);if(t.isEmpty(e))d.value.completeByNoMore([],!0);else{if(p.no>1&&c.value){const a=[];for(const t of e)u.value.some((e=>e.id===t.id))||a.push(t);return u.value=[...a,...u.value],d.value.updateCache(),void(c.value=!1)}e.slice(-1).length>0&&(p.createTime=s.formatDate(e.slice(-1)[0].createTime)),d.value.completeByNoMore(e,!1)}},g=e=>{e(!1),d.value.scrollToBottom()},h=()=>{1!==p.no&&(n.value=!1)};return r({getMessageList:v,refreshMessageList:async e=>{void 0!==e?d.value.addChatRecordData([e],!1):(p.createTime=void 0,c.value=!0,await v()),p.no>1?n.value=!0:h()}}),(a,t)=>({a:e.unref(l)+"px",b:e.w((({item:a,index:t},o,s)=>({a:"ab6fa3c4-1-"+s+",ab6fa3c4-0",b:e.p({message:a,"message-index":t,"message-list":u.value}),c:s,d:o})),{name:"cell",path:"b",vueId:"ab6fa3c4-0"}),c:e.sr(d,"ab6fa3c4-0",{k:"pagingRef"}),d:e.o(g),e:e.o(h),f:e.o(f),g:e.o((e=>u.value=e)),h:e.p({"use-chat-record-mode":!0,"use-virtual-list":!0,"cell-height-mode":"dynamic","default-page-size":"20","auto-clean-list-when-reload":!1,"safe-area-inset-bottom":!0,"bottom-bg-color":"#f8f8f8","back-to-top-style":m,"auto-show-back-to-top":n.value,modelValue:u.value})})}};wx.createComponent(r);
