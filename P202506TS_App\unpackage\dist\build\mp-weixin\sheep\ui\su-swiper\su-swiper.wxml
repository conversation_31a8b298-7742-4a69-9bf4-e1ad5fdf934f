<view class="data-v-e03a1d3b"><view class="{{['ui-swiper', 'data-v-e03a1d3b', v, w, x]}}"><swiper class="data-v-e03a1d3b" circular="{{b}}" current="{{c}}" autoplay="{{d}}" interval="{{e}}" duration="{{f}}" bindtransition="{{g}}" bindanimationfinish="{{h}}" style="{{i}}" bindchange="{{j}}"><swiper-item wx:for="{{a}}" wx:for-item="item" wx:key="j" class="{{['swiper-item', 'data-v-e03a1d3b', item.k && 'cur']}}" bindtap="{{item.l}}"><view class="ui-swiper-main data-v-e03a1d3b"><image wx:if="{{item.a}}" class="swiper-image data-v-e03a1d3b" mode="{{item.b}}" src="{{item.c}}" width="100%" height="100%" bindload="{{item.d}}"></image><su-video wx:else class="r-i-f data-v-e03a1d3b" u-r="{{item.f}}" bindvideoTimeupdate="{{item.g}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i||''}}"></su-video></view></swiper-item></swiper><block wx:if="{{k}}"><view wx:if="{{l}}" class="{{['ui-swiper-dot', 'data-v-e03a1d3b', o]}}"><view wx:for="{{m}}" wx:for-item="item" wx:key="a" class="{{['line-box', 'data-v-e03a1d3b', item.b, n]}}"></view></view><view wx:if="{{p}}" class="{{['ui-swiper-dot', 'data-v-e03a1d3b', t]}}"><view class="{{['ui-tag', 'radius-lg', 'data-v-e03a1d3b', s]}}" style="pointer-events:none;padding:0 10rpx"><view class="data-v-e03a1d3b" style="transform:scale(0.7)">{{q}} / {{r}}</view></view></view></block></view></view>