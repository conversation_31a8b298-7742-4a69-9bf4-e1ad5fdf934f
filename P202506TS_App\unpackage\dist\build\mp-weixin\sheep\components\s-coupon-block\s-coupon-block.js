"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),o=require("../../api/promotion/coupon.js"),n=require("../../util/const.js"),a=require("../../util/index.js");if(!Array){e.resolveComponent("su-coupon")()}Math;const i={__name:"s-coupon-block",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(i){const u=i,{columns:r,button:p}=u.data,s=["lg","md","xs"],c={background:`url(${t.sheep.$url.cdn(u.data.bgImg)}) no-repeat top center / 100% 100%`},d={background:p.bgColor,color:p.color},l={display:"flex","justify-content":"space-between"},y={display:"flex","justify-content":"space-around"},m=e.computed((()=>{const{bgType:e,bgImg:t,bgColor:o}=u.styles;return{background:"img"===e?`url(${t}) no-repeat top center / 100% 100%`:o}})),T=e=>e.discountType===n.PromotionDiscountTypeEnum.PRICE.type?a.floatToFixed2(e.discountPrice):e.discountType===n.PromotionDiscountTypeEnum.PERCENT.type?e.discountPercent:`未知【${e.discountType}】`,f=e.ref([]);async function g(t){const{error:n,msg:a}=await o.CouponApi.takeCoupon(t);0!==n?await b():e.index.showToast({title:a,icon:"none"})}const b=async()=>{const{data:e}=await o.CouponApi.getCouponTemplateListByIds(u.data.couponIds.join(","));f.value=e};return e.onMounted((()=>{b()})),(t,o)=>({a:e.f(f.value,((t,o,u)=>{return e.e(2===e.unref(r)?{a:e.o((e=>g(t.id)),o),b:e.s(d)}:{c:e.s(d),d:e.o((e=>g(t.id)),o)},{e:"9bfdd9ec-0-"+u,f:e.p({size:s[e.unref(r)-1],textColor:i.data.textColor,background:"",couponId:t.id,title:t.name,type:(c=t,c.discountType===n.PromotionDiscountTypeEnum.PRICE.type?"reduce":c.discountType===n.PromotionDiscountTypeEnum.PERCENT.type?"percent":`未知【${c.discountType}】`),value:T(t),sellBy:(p=t,p.validityType===n.CouponTemplateValidityTypeEnum.DATE.type?`${a.formatDate(p.validStartTime)} 至 ${a.formatDate(p.validEndTime)}`:p.validityType===n.CouponTemplateValidityTypeEnum.TERM.type?`领取后第 ${p.fixedStartTerm} - ${p.fixedEndTerm} 天内可用`:"未知【"+p.validityType+"】")}),g:o});var p,c})),b:2===e.unref(r),c:e.s(c),d:e.s({marginLeft:`${i.data.space}px`}),e:e.s(2===f.value.length?y:l),f:e.s(m.value),g:e.s({marginLeft:`${i.data.space}px`})})}},u=e._export_sfc(i,[["__scopeId","data-v-9bfdd9ec"]]);wx.createComponent(u);
