# 开发环境：本地只启动前端项目，依赖开发环境（后端、APP）
NODE_ENV=production

VITE_DEV=true

# 文档地址的开关
VITE_APP_DOCALERT_ENABLE=false

# 百度统计
VITE_APP_BAIDU_CODE= 

# 默认账户密码
VITE_APP_DEFAULT_LOGIN_TENANT=税眸
VITE_APP_DEFAULT_LOGIN_USERNAME=
VITE_APP_DEFAULT_LOGIN_PASSWORD=

# 请求路径
# VITE_BASE_URL='http://localhost:48080'
# VITE_BASE_URL='http://***************:34903'
# VITE_BASE_URL='https://yqfop.qdprxx.com'
# VITE_BASE_URL='https://op.1788plus.com'
VITE_BASE_URL='https://www.taxvision.cn'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.yudao.iocoder.cn'

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'