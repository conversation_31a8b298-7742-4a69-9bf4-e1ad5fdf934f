<s-layout wx:if="{{i}}" class="data-v-64e79162" u-s="{{['d']}}" u-i="64e79162-0" bind:__l="__l" u-p="{{i}}"><view wx:if="{{a}}" class="contract-content ss-m-x-20 ss-m-t-20 data-v-64e79162"><uni-list class="data-v-64e79162" u-s="{{['d']}}" u-i="64e79162-1,64e79162-0" bind:__l="__l"><uni-list-item wx:for="{{b}}" wx:for-item="item" wx:key="m" u-s="{{['body']}}" class="ss-radius-20 ss-m-b-20 data-v-64e79162" style="background-color:#fff;overflow:hidden" u-i="{{item.n}}" bind:__l="__l"><view slot="body"><view class="ss-flex-col data-v-64e79162"><view class="contract-header ss-flex data-v-64e79162"><view class="{{['contract-status', 'data-v-64e79162', item.b]}}">{{item.a}}</view></view><view class="ss-w-100 data-v-64e79162" style="height:58rpx"></view><view class="contract-text title data-v-64e79162">{{item.c}}</view><view class="contract-text data-v-64e79162">评测周期：{{item.d}}</view><view wx:if="{{item.e}}" class="contract-text data-v-64e79162">订单信息：{{item.f}}</view><view class="contract-text data-v-64e79162"> 创建时间：{{item.g}}</view></view><view class="ss-m-t-20 ss-flex ss-flex-wrap ss-col-center data-v-64e79162" style="gap:20rpx"><button class="tool-btn ss-reset-button data-v-64e79162" catchtap="{{item.h}}"> 详情 </button><button wx:if="{{item.i}}" class="tool-btn ss-reset-button data-v-64e79162" catchtap="{{item.j}}"> 查看报告 </button><button wx:elif="{{item.k}}" class="tool-btn ss-reset-button data-v-64e79162" catchtap="{{item.l}}"> 下载 </button></view></view></uni-list-item></uni-list></view><uni-load-more wx:if="{{c}}" class="data-v-64e79162" bindtap="{{d}}" u-i="64e79162-3,64e79162-0" bind:__l="__l" u-p="{{e}}"/><s-empty wx:if="{{f}}" class="data-v-64e79162" u-i="64e79162-4,64e79162-0" bind:__l="__l" u-p="{{g}}"/><view class="btn_add data-v-64e79162" bindtap="{{h}}">+</view></s-layout>