"use strict";const e=require("../../../common/vendor.js"),i=require("../../api/infra/file.js"),o="chooseAndUploadFile:fail";function t(e,i){return e.tempFiles.forEach(((e,o)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),i&&(e.fileType=i),e.cloudPath=Date.now()+"_"+o+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}async function s(e,{onChooseFile:o,onUploadProgress:t}){const s=await e;let n=s.tempFiles||[];if(o){const e=o(s);void 0!==e&&(n=await Promise.resolve(e),void 0===n&&(n=s.tempFiles||[]))}for(let r of n){const{data:e}=await i.FileApi.uploadFile(r.path);r.url=e}return n}exports.chooseAndUploadFile=function(i={type:"all"}){return"image"===i.type?s(function(i){const{count:s,sizeType:n=["original","compressed"],sourceType:r=["album","camera"],extension:a}=i;return new Promise(((i,l)=>{e.index.chooseImage({count:s,sizeType:n,sourceType:r,extension:a,success(e){i(t(e,"image"))},fail(e){l({errMsg:e.errMsg.replace("chooseImage:fail",o)})}})}))}(i),i):"video"===i.type?s(function(i){const{camera:s,compressed:n,maxDuration:r,sourceType:a=["album","camera"],extension:l}=i;return new Promise(((i,c)=>{e.index.chooseVideo({camera:s,compressed:n,maxDuration:r,sourceType:a,extension:l,success(e){const{tempFilePath:o,duration:s,size:n,height:r,width:a}=e;i(t({errMsg:"chooseVideo:ok",tempFilePaths:[o],tempFiles:[{name:e.tempFile&&e.tempFile.name||"",path:o,size:n,type:e.tempFile&&e.tempFile.type||"",width:a,height:r,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",o)})}})}))}(i),i):s(function(i){const{count:s,extension:n}=i;return new Promise(((i,r)=>{let a=e.index.chooseFile;if(void 0!==e.wx$1&&"function"==typeof e.wx$1.chooseMessageFile&&(a=e.wx$1.chooseMessageFile),"function"!=typeof a)return r({errMsg:o+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});a({type:"all",count:s,extension:n,success(e){i(t(e))},fail(e){r({errMsg:e.errMsg.replace("chooseFile:fail",o)})}})}))}(i),i)},exports.uploadCloudFiles=function(i,o=5,t){const s=(i=JSON.parse(JSON.stringify(i))).length;let n=0,r=this;return new Promise((a=>{for(;n<o;)l();function l(){let o=n++;if(o>=s)return void(!i.find((e=>!e.url&&!e.errMsg))&&a(i));const c=i[o],p=r.files.findIndex((e=>e.uuid===c.uuid));c.url="",delete c.errMsg,e.nr.uploadFile({filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,onUploadProgress:e=>{e.index=p,t&&t(e)}}).then((e=>{c.url=e.fileID,c.index=p,o<s&&l()})).catch((e=>{c.errMsg=e.errMsg||e.message,c.index=p,o<s&&l()}))}}))};
