"use strict";const e=require("../../request/index.js"),t={getContractPage:t=>e.request({url:"/member/contract/page",method:"GET",params:t}),getContract:t=>e.request({url:"/member/contract/get?id="+t,method:"GET"}),createContract:t=>e.request({url:"/member/contract/create",method:"POST",data:t}),updateContract:t=>e.request({url:"/member/contract/update",method:"PUT",data:t}),deleteContract:t=>e.request({url:"/member/contract/delete?id="+t,method:"DELETE"}),exportContract:t=>e.request({url:"/member/contract/export-excel",method:"GET",params:t}),getJrCourtRegister:(t,r,a)=>e.request({url:"/member/contract/get-jr-court-register",method:"GET",params:{enterpriseId:t,id:r,name:a}}),getMrIllegalInfo:(t,r,a)=>e.request({url:"/member/contract/get-mr-illegal-info",method:"GET",params:{enterpriseId:t,id:r,name:a}}),getCbJudicial:(t,r)=>e.request({url:"/member/contract/get-cb-judicial",method:"GET",params:{enterpriseId:t,enterpriseName:r}}),getSignUrl:t=>e.request({url:"/member/contract/get-sign-url",method:"GET",params:{id:t}}),getSignFile:t=>e.request({url:"/member/contract/get-sign-file?id="+t,method:"GET"})};exports.ContractApi=t;
