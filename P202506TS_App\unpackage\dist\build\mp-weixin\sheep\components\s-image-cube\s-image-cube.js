"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),a={__name:"s-image-cube",props:{data:{type:Object,default(){}},styles:{type:Object,default(){}}},setup(a){const d=a,r=t.sheep.$platform.device.windowWidth,o=e.computed((()=>(r-((d.styles.marginLeft||0)+(d.styles.marginRight||0)+2*(d.styles.padding||0)))/4)),i=e.computed((()=>{var e,t,a,i;let s=d.data.list.reduce(((e,t)=>e.includes(t.height+t.top)?e:[...e,t.height+t.top]),[]);return{height:Math.max(...s)*o.value+"px",width:r-2*((null==(t=null==(e=d.data)?void 0:e.style)?void 0:t.marginLeft)+(null==(i=null==(a=d.data)?void 0:a.style)?void 0:i.marginRight)+2*d.styles.padding)+"px"}})),s=e=>({width:e.width*o.value-d.data.space+"px",height:e.height*o.value-d.data.space+"px",left:e.left*o.value+"px",top:e.top*o.value+"px","border-top-left-radius":d.data.borderRadiusTop+"px","border-top-right-radius":d.data.borderRadiusTop+"px","border-bottom-left-radius":d.data.borderRadiusBottom+"px","border-bottom-right-radius":d.data.borderRadiusBottom+"px"});return(d,r)=>({a:e.f(a.data.list,((a,d,r)=>({a:e.unref(t.sheep).$url.cdn(a.imgUrl),b:e.s(s(a)),c:e.o((d=>e.unref(t.sheep).$router.go(a.url)),d),d:d}))),b:e.s({margin:a.data.space+"px"}),c:e.s(i.value)})}},d=e._export_sfc(a,[["__scopeId","data-v-1ee0eac9"]]);wx.createComponent(d);
