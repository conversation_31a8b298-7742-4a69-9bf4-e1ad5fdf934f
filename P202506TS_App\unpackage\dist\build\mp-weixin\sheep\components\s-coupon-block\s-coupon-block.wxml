<scroll-view class="scroll-box data-v-9bfdd9ec" scroll-x scroll-anchoring style="{{f + ';' + g}}"><view class="coupon-box ss-flex data-v-9bfdd9ec" style="{{e}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="g" class="coupon-item data-v-9bfdd9ec" style="{{c + ';' + d}}"><su-coupon wx:if="{{item.f}}" class="data-v-9bfdd9ec" u-s="{{['btn']}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"><view slot="btn"><button wx:if="{{b}}" catchtap="{{item.a}}" class="ss-reset-button card-btn vertical data-v-9bfdd9ec" style="{{item.b}}"><view class="btn-text data-v-9bfdd9ec">立即领取</view></button><button wx:else class="ss-reset-button card-btn data-v-9bfdd9ec" style="{{item.c}}" catchtap="{{item.d}}"> 立即领取 </button></view></su-coupon></view></view></scroll-view>