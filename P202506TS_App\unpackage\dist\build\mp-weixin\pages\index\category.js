"use strict";const t=require("../../common/vendor.js"),e=require("../../sheep/index.js"),o=require("../../sheep/api/product/category.js"),a=require("../../sheep/api/product/spu.js"),i=require("../../sheep/util/index.js");if(!Array){(t.resolveComponent("uni-load-more")+t.resolveComponent("s-layout"))()}Math||(s+r+n+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const n=()=>"./components/second-one.js",s=()=>"./components/first-one.js",r=()=>"./components/first-two.js",c={__name:"category",setup(n){const s=t.reactive({style:"second_one",categoryList:[],activeMenu:0,pagination:{list:[],total:[],pageNo:1,pageSize:6},loadStatus:""}),{safeArea:r}=e.sheep.$platform.device,c=t.computed((()=>r.height-44-50));const p=t=>{s.activeMenu=t,"first_one"!==s.style&&"first_two"!==s.style||(s.pagination.pageNo=1,s.pagination.list=[],s.pagination.total=0,l())};async function l(){s.loadStatus="loading";const e=await a.SpuApi.getSpuPage({categoryId:s.categoryList[s.activeMenu].id,pageNo:s.pagination.pageNo,pageSize:s.pagination.pageSize});0===e.code&&(s.pagination.list=t.lodash.concat(s.pagination.list,e.data.list),s.pagination.total=e.data.total,s.loadStatus=s.pagination.list.length<s.pagination.total?"more":"noMore")}function g(){"noMore"!==s.loadStatus&&(s.pagination.pageNo++,l())}function u(){g()}return t.onLoad((async t=>{await async function(){const{code:t,data:e}=await o.CategoryApi.getCategoryList();0===t&&(s.categoryList=i.handleTree(e))}();const e=s.categoryList.find((e=>e.id===Number(t.id)));p(e?s.categoryList.indexOf(e):0)})),(o,a)=>{var i,n;return t.e({a:t.f(s.categoryList,((e,o,a)=>({a:t.t(e.name),b:e.id,c:t.n({"menu-item-active":o===s.activeMenu}),d:t.o((t=>p(o)),e.id)}))),b:t.s({height:c.value+"px"}),c:null==(i=s.categoryList)?void 0:i.length},(null==(n=s.categoryList)?void 0:n.length)?t.e({d:s.categoryList[s.activeMenu].picUrl},s.categoryList[s.activeMenu].picUrl?{e:t.unref(e.sheep).$url.cdn(s.categoryList[s.activeMenu].picUrl)}:{},{f:"first_one"===s.style},"first_one"===s.style?{g:t.p({pagination:s.pagination})}:{},{h:"first_two"===s.style},"first_two"===s.style?{i:t.p({pagination:s.pagination})}:{},{j:"second_one"===s.style},"second_one"===s.style?{k:t.p({data:s.categoryList,activeMenu:s.activeMenu})}:{},{l:("first_one"===s.style||"first_two"===s.style)&&s.pagination.total>0},("first_one"===s.style||"first_two"===s.style)&&s.pagination.total>0?{m:t.o(g),n:t.p({status:s.loadStatus,"content-text":{contentdown:"点击查看更多"}})}:{},{o:t.s({height:c.value+"px"}),p:t.o(u)}):{},{q:t.s({height:c.value+"px"}),r:t.p({bgStyle:{color:"#fff"},tabbar:"/pages/index/category",title:"分类"})})}}},p=t._export_sfc(c,[["__scopeId","data-v-10b160c4"]]);wx.createPage(p);
