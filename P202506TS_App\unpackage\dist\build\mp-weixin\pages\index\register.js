"use strict";const e=require("../../common/vendor.js"),s=require("../../sheep/index.js");require("../../sheep/store/index.js"),require("../../sheep/helper/index.js"),require("../../sheep/request/index.js"),Math||(r+t+a)();const r=()=>"./components/register/enterprise-selector.js",t=()=>"./components/register/enterprise-register.js",a=()=>"./components/register/enterprise-package.js",n={__name:"register",setup(r){e.useCssVars((s=>({"3eddd5f0":e.unref(t)})));const t=s.sheep.$url.css("/assets/mp/login/bg_login.png");e.computed((()=>s.sheep.$store("user").isLogin));const a=s.sheep.$store("modal"),n=e.computed((()=>a.auth||"enterpriseSelector"));console.log("authType",n),e.reactive({protocol:!1});const o=e.ref(!1);function i(e){o.value=e,setTimeout((()=>{o.value=!1}),1e3)}return e.onLoad((e=>{e.payState&&"success"===e.payState&&(n.value="enterpriseSelector")})),(r,t)=>e.e({a:"enterprisePackage"!==n.value},"enterprisePackage"!==n.value?{b:e.unref(s.sheep).$url.static("/assets/mp/login/logo.png")}:{},{c:"enterprisePackage"===n.value},(n.value,{}),{d:"enterpriseSelector"===n.value},"enterpriseSelector"===n.value?{e:e.o(i)}:{},{f:"enterpriseRegister"===n.value},"enterpriseRegister"===n.value?{g:e.o(i)}:{},{h:"enterprisePackage"===n.value},"enterprisePackage"===n.value?{i:e.o(i)}:{},{j:"enterprisePackage"===n.value},"enterprisePackage"===n.value?{k:e.unref(s.sheep).$url.static("/assets/mp/login/logo.png")}:{},{l:"enterprisePackage"===n.value?1:"",m:e.s(r.__cssVars())})}},o=e._export_sfc(n,[["__scopeId","data-v-e43e7729"]]);wx.createPage(o);
