<s-layout wx:if="{{h}}" class="data-v-1546888b" u-s="{{['d']}}" u-i="1546888b-0" bind:__l="__l" u-p="{{h}}"><view wx:if="{{a}}" class="contract-content ss-m-x-20 ss-m-t-20 data-v-1546888b"><uni-list class="data-v-1546888b" u-s="{{['d']}}" u-i="1546888b-1,1546888b-0" bind:__l="__l"><uni-list-item wx:for="{{b}}" wx:for-item="item" wx:key="f" u-s="{{['body']}}" class="ss-radius-20 ss-m-b-20 data-v-1546888b" style="background-color:#fff;overflow:hidden" u-i="{{item.g}}" bind:__l="__l"><view slot="body"><view class="ss-flex-col data-v-1546888b"><view class="contract-text title data-v-1546888b">{{item.a}}</view><view class="contract-text data-v-1546888b">{{item.b}}</view><view class="contract-text data-v-1546888b"> 创建时间：{{item.c}}</view></view><view class="ss-m-t-20 ss-flex ss-flex-wrap ss-col-center data-v-1546888b" style="gap:20rpx"><button class="tool-btn ss-reset-button data-v-1546888b" catchtap="{{item.d}}"> 详情 </button><button class="tool-btn ss-reset-button data-v-1546888b" catchtap="{{item.e}}"> 删除 </button></view></view></uni-list-item></uni-list></view><uni-load-more wx:if="{{c}}" class="data-v-1546888b" bindtap="{{d}}" u-i="1546888b-3,1546888b-0" bind:__l="__l" u-p="{{e}}"/><s-empty wx:if="{{f}}" class="data-v-1546888b" u-i="1546888b-4,1546888b-0" bind:__l="__l" u-p="{{g}}"/></s-layout>