"use strict";const t=require("../../common/vendor.js"),e=require("../api/trade/cart.js"),s=t.defineStore({id:"cart",state:()=>({list:[],selectedIds:[],isAllSelected:!1,totalPriceSelected:0}),actions:{async getList(){const{data:t,code:s}=await e.CartApi.getCartList();0===s&&(this.list=t.validList,this.selectedIds=[],this.isAllSelected=!0,this.totalPriceSelected=0,this.list.forEach((t=>{t.selected?(this.selectedIds.push(t.id),this.totalPriceSelected+=t.count*t.sku.price):this.isAllSelected=!1})))},async add(t){const{code:s}=await e.CartApi.addCart({skuId:t.id,count:t.goods_num});0===s&&await this.getList()},async update(t){const{code:s}=await e.CartApi.updateCartCount({id:t.goods_id,count:t.goods_num});0===s&&await this.getList()},async delete(t){let s="";s=Array.isArray(t)?t.join(","):t;const{code:i}=await e.CartApi.deleteCart(s);0===i&&await this.getList()},async selectSingle(t){const{code:s}=await e.CartApi.updateCartSelected({ids:[t],selected:!this.selectedIds.includes(t)});0===s&&await this.getList()},async selectAll(t){const{code:s}=await e.CartApi.updateCartSelected({ids:this.list.map((t=>t.id)),selected:t});0===s&&await this.getList()},emptyList(){this.list=[],this.selectedIds=[],this.isAllSelected=!0,this.totalPriceSelected=0}},persist:{enabled:!0,strategies:[{key:"cart-store"}]}}),i=Object.freeze(Object.defineProperty({__proto__:null,default:s},Symbol.toStringTag,{value:"Module"}));exports.__vite_glob_0_1=i,exports.cart=s;
