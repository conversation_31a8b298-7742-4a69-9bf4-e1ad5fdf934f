<s-layout wx:if="{{B}}" class="data-v-0736fb31" u-s="{{['d']}}" u-i="0736fb31-0" bind:__l="__l" u-p="{{B}}"><uni-forms wx:if="{{s}}" class="r data-v-0736fb31" u-s="{{['d']}}" u-r="addressFormRef" u-i="0736fb31-1,0736fb31-0" bind:__l="__l" bindupdateModelValue="{{r}}" u-p="{{s}}"><view class="bg-white form-box ss-p-x-30 data-v-0736fb31"><uni-forms-item wx:if="{{c}}" u-s="{{['d']}}" class="form-item data-v-0736fb31" u-i="0736fb31-2,0736fb31-1" bind:__l="__l" u-p="{{c}}"><uni-easyinput wx:if="{{b}}" class="data-v-0736fb31" u-i="0736fb31-3,0736fb31-2" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"/></uni-forms-item><uni-forms-item wx:if="{{f}}" u-s="{{['d']}}" class="form-item data-v-0736fb31" u-i="0736fb31-4,0736fb31-1" bind:__l="__l" u-p="{{f}}"><uni-easyinput wx:if="{{e}}" class="data-v-0736fb31" u-i="0736fb31-5,0736fb31-4" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{k}}" u-s="{{['d']}}" bindtap="{{j}}" class="form-item data-v-0736fb31" u-i="0736fb31-6,0736fb31-1" bind:__l="__l" u-p="{{k}}"><uni-easyinput wx:if="{{i}}" class="data-v-0736fb31" u-s="{{['right']}}" u-i="0736fb31-7,0736fb31-6" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"><uni-icons class="data-v-0736fb31" u-i="0736fb31-8,0736fb31-7" bind:__l="__l" u-p="{{g}}" slot="right"/></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{n}}" u-s="{{['d']}}" class="textarea-item data-v-0736fb31" u-i="0736fb31-9,0736fb31-1" bind:__l="__l" u-p="{{n}}"><uni-easyinput wx:if="{{m}}" class="data-v-0736fb31" u-i="0736fb31-10,0736fb31-9" bind:__l="__l" bindupdateModelValue="{{l}}" u-p="{{m}}"/></uni-forms-item></view><view class="ss-m-y-20 bg-white ss-p-x-30 ss-flex ss-row-between ss-col-center default-box data-v-0736fb31"><view class="default-box-title data-v-0736fb31"> 设为默认地址 </view><su-switch wx:if="{{p}}" class="data-v-0736fb31" style="transform:scale(0.8)" u-i="0736fb31-11,0736fb31-1" bind:__l="__l" bindupdateModelValue="{{o}}" u-p="{{p}}"/></view></uni-forms><su-fixed wx:if="{{x}}" class="data-v-0736fb31" u-s="{{['d']}}" u-i="0736fb31-12,0736fb31-0" bind:__l="__l" u-p="{{x}}"><view class="footer-box ss-flex-col ss-row-between ss-p-20 data-v-0736fb31"><view class="ss-m-b-20 data-v-0736fb31"><button class="ss-reset-button save-btn ui-Shadow-Main data-v-0736fb31" bindtap="{{t}}">保存</button></view><button wx:if="{{v}}" class="ss-reset-button cancel-btn data-v-0736fb31" bindtap="{{w}}"> 删除 </button></view></su-fixed><su-region-picker wx:if="{{A}}" class="data-v-0736fb31" bindcancel="{{y}}" bindconfirm="{{z}}" u-i="0736fb31-13,0736fb31-0" bind:__l="__l" u-p="{{A}}"/></s-layout>