"use strict";const t=require("../../../common/vendor.js"),e={name:"SuToolbar",props:{show:{type:Boolean,default:!0},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"},cancelColor:{type:String,default:"#909193"},confirmColor:{type:String,default:"#3c9cff"},title:{type:String,default:""}},methods:{cancel(){this.$emit("cancel")},confirm(){this.$emit("confirm")},preventEvent(t){t&&"function"==typeof t.stopPropagation&&t.stopPropagation()},noop(t){this.preventEvent(t)}}};const o=t._export_sfc(e,[["render",function(e,o,n,c,r,i){return t.e({a:n.show},n.show?t.e({b:t.t(n.cancelText),c:t.o(((...t)=>i.cancel&&i.cancel(...t))),d:n.cancelColor,e:n.title},n.title?{f:t.t(n.title)}:{},{g:t.t(n.confirmText),h:t.o(((...t)=>i.confirm&&i.confirm(...t))),i:n.confirmColor,j:t.o(((...t)=>i.noop&&i.noop(...t)))}):{})}],["__scopeId","data-v-7128d250"]]);wx.createComponent(o);
