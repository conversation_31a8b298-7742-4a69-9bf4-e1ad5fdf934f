"use strict";const e=require("../../common/vendor.js"),s=require("../../sheep/index.js"),t=require("../../sheep/platform/share.js"),n=require("../../sheep/api/member/aisino.js"),r=require("../../sheep/api/college/course.js");if(require("../../sheep/request/index.js"),!Array){(e.resolveComponent("s-image-banner")+e.resolveComponent("uni-section")+e.resolveComponent("uni-list-item")+e.resolveComponent("uni-list")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-image-banner/s-image-banner.js")+(()=>"../../uni_modules/uni-section/components/uni-section/uni-section.js")+(()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const o={__name:"index",setup(o){e.useCssVars((s=>({fe8c9528:e.unref(a),"8b63bc62":e.unref(p)}))),e.index.hideTabBar({fail:()=>{}});const i=0*s.sheep.$platform.device.statusBarHeight*2,a=s.sheep.$url.css("/assets/mp/index/bg_header.png"),p=s.sheep.$url.css("/assets/mp/index/bg_decoration.png"),u=e.computed((()=>{var e;return null==(e=s.sheep.$store("app").template)?void 0:e.home}));e.computed((()=>s.sheep.$store("user").userInfo));const l=e.reactive({enterpriseInfo:{}}),c=e.ref({type:"default",indicator:"dot",autoplay:!0,interval:3,items:[{type:"img",imgUrl:s.sheep.$url.static("/assets/mp/index/banner01.png"),videoUrl:""}],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}),g=async()=>{const{code:e,data:t,msg:r}=await n.AisinoApi.getLoginUrl({id:l.enterpriseInfo.id});0===e?s.sheep.$router.go(t):s.sheep.$helper.toast(r)},m=async()=>{s.sheep.$router.go("/pages/tax/list")},d=e.ref([]);return e.ref([]),e.ref([]),e.onShow((async()=>{l.enterpriseInfo=await e.index.getStorageSync("enterprise"),l.enterpriseInfo||s.sheep.$router.redirect("/pages/index/register"),async function(){try{const{code:e,data:s}=await r.CollegeCourseApi.getEnterpriseCoursePage({pageNo:1,pageSize:2});if(0!==e)return;d.value=s.list||[]}catch(e){console.error("获取课程列表失败:",e),d.value=[]}}()})),e.onLoad((e=>{if(e.scene){const s=decodeURIComponent(e.scene).split("=");e[s[0]]=s[1]}s.sheep.$store("app").init(e.templateId),e.spm&&t.$share.decryptSpm(e.spm),e.page&&s.sheep.$router.go(decodeURIComponent(e.page))})),e.onPullDownRefresh((()=>{s.sheep.$store("app").init(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),e.onPageScroll((()=>{})),(t,n)=>e.e({a:e.s({marginTop:Number(i)+"rpx"}),b:e.t(l.enterpriseInfo.name),c:e.t(l.enterpriseInfo.legalPersonName),d:e.unref(s.sheep).$url.static("/assets/mp/index/btn_change.png"),e:e.o((t=>e.unref(s.sheep).$router.go("/pages/index/register"))),f:e.p({data:c.value}),g:e.unref(s.sheep).$url.static("/assets/mp/index/ic_tax.png"),h:e.o(g),i:e.unref(s.sheep).$url.static("/assets/mp/index/ic_tax_report.png"),j:e.o(m),k:e.p({title:"税务风险监测"}),l:d.value.length>0},d.value.length>0?{m:e.unref(s.sheep).$url.static("/assets/mp/index/ic_more.png"),n:e.o((t=>e.unref(s.sheep).$router.go("/pages/index/college"))),o:e.f(d.value,((t,n,r)=>({a:t.thumbnailUrl,b:e.t(t.title),c:t.id,d:e.o((n=>e.unref(s.sheep).$router.go("/pages/college/detail",{id:t.id})),t.id)}))),p:e.p({title:"商学院"})}:{},{},{w:!1},{},{B:e.s(t.__cssVars()),C:e.p({title:"首页",tabbar:"/pages/index/index",navbar:"custom",bgStyle:u.value.page,navbarStyle:u.value.navigationBar,onShareAppMessage:!0})})}},i=e._export_sfc(o,[["__scopeId","data-v-954af78b"]]);o.__runtimeHooks=3,wx.createPage(i);
