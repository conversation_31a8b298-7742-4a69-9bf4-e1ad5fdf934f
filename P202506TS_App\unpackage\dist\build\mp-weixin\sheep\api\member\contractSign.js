"use strict";const e=require("../../request/index.js"),t={getContractSignPage:t=>e.request({url:"/member/contract-sign/page",method:"GET",params:t}),getContractSign:t=>e.request({url:"/member/contract-sign/get?id="+t,method:"GET"}),createContractSign:t=>e.request({url:"/member/contract-sign/create",method:"POST",data:t}),updateContractSign:t=>e.request({url:"/member/contract-sign/update",method:"PUT",data:t}),deleteContractSign:t=>e.request({url:"/member/contract-sign/delete?id="+t,method:"DELETE"}),exportContractSign:t=>e.request({url:"/member/contract-sign/export-excel",method:"GET",params:t})};exports.ContractSignApi=t;
