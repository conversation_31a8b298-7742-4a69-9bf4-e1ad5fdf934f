package cn.iocoder.yudao.module.trade.convert.cart;

import cn.iocoder.yudao.module.product.api.property.dto.ProductPropertyValueDetailRespDTO;
import cn.iocoder.yudao.module.product.api.sku.dto.ProductSkuRespDTO;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import cn.iocoder.yudao.module.trade.controller.app.base.property.AppProductPropertyValueDetailRespVO;
import cn.iocoder.yudao.module.trade.controller.app.base.sku.AppProductSkuBaseRespVO;
import cn.iocoder.yudao.module.trade.controller.app.base.spu.AppProductSpuBaseRespVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:14:21+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
public class TradeCartConvertImpl implements TradeCartConvert {

    @Override
    public AppProductSpuBaseRespVO convert(ProductSpuRespDTO spu) {
        if ( spu == null ) {
            return null;
        }

        AppProductSpuBaseRespVO appProductSpuBaseRespVO = new AppProductSpuBaseRespVO();

        appProductSpuBaseRespVO.setId( spu.getId() );
        appProductSpuBaseRespVO.setName( spu.getName() );
        appProductSpuBaseRespVO.setPicUrl( spu.getPicUrl() );
        appProductSpuBaseRespVO.setCategoryId( spu.getCategoryId() );

        return appProductSpuBaseRespVO;
    }

    @Override
    public AppProductSkuBaseRespVO convert(ProductSkuRespDTO sku) {
        if ( sku == null ) {
            return null;
        }

        AppProductSkuBaseRespVO appProductSkuBaseRespVO = new AppProductSkuBaseRespVO();

        appProductSkuBaseRespVO.setId( sku.getId() );
        appProductSkuBaseRespVO.setPicUrl( sku.getPicUrl() );
        appProductSkuBaseRespVO.setPrice( sku.getPrice() );
        appProductSkuBaseRespVO.setStock( sku.getStock() );
        appProductSkuBaseRespVO.setProperties( productPropertyValueDetailRespDTOListToAppProductPropertyValueDetailRespVOList( sku.getProperties() ) );

        return appProductSkuBaseRespVO;
    }

    protected AppProductPropertyValueDetailRespVO productPropertyValueDetailRespDTOToAppProductPropertyValueDetailRespVO(ProductPropertyValueDetailRespDTO productPropertyValueDetailRespDTO) {
        if ( productPropertyValueDetailRespDTO == null ) {
            return null;
        }

        AppProductPropertyValueDetailRespVO appProductPropertyValueDetailRespVO = new AppProductPropertyValueDetailRespVO();

        appProductPropertyValueDetailRespVO.setPropertyId( productPropertyValueDetailRespDTO.getPropertyId() );
        appProductPropertyValueDetailRespVO.setPropertyName( productPropertyValueDetailRespDTO.getPropertyName() );
        appProductPropertyValueDetailRespVO.setValueId( productPropertyValueDetailRespDTO.getValueId() );
        appProductPropertyValueDetailRespVO.setValueName( productPropertyValueDetailRespDTO.getValueName() );

        return appProductPropertyValueDetailRespVO;
    }

    protected List<AppProductPropertyValueDetailRespVO> productPropertyValueDetailRespDTOListToAppProductPropertyValueDetailRespVOList(List<ProductPropertyValueDetailRespDTO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppProductPropertyValueDetailRespVO> list1 = new ArrayList<AppProductPropertyValueDetailRespVO>( list.size() );
        for ( ProductPropertyValueDetailRespDTO productPropertyValueDetailRespDTO : list ) {
            list1.add( productPropertyValueDetailRespDTOToAppProductPropertyValueDetailRespVO( productPropertyValueDetailRespDTO ) );
        }

        return list1;
    }
}
