<s-layout wx:if="{{y}}" class="data-v-3d941d61" u-s="{{['d']}}" u-i="3d941d61-0" bind:__l="__l" u-p="{{y}}"><view class="goods-box data-v-3d941d61"><s-goods-item wx:if="{{a}}" class="data-v-3d941d61" u-i="3d941d61-1,3d941d61-0" bind:__l="__l" u-p="{{a}}"/></view><uni-forms wx:if="{{o}}" class="r data-v-3d941d61" u-s="{{['d']}}" u-r="form" u-i="3d941d61-2,3d941d61-0" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"><view class="refund-item data-v-3d941d61"><view class="item-title ss-m-b-20 data-v-3d941d61">售后类型</view><view class="ss-flex-col data-v-3d941d61"><radio-group class="data-v-3d941d61" bindchange="{{c}}"><label wx:for="{{b}}" wx:for-item="item" wx:key="d" class="ss-flex ss-col-center ss-p-y-10 data-v-3d941d61"><radio class="data-v-3d941d61" checked="{{item.a}}" color="var(--ui-BG-Main)" style="transform:scale(0.8)" value="{{item.b}}"/><view class="item-value ss-m-l-8 data-v-3d941d61">{{item.c}}</view></label></radio-group></view></view><view class="refund-item ss-flex ss-col-center ss-row-between data-v-3d941d61" bindtap="{{e}}"><text class="item-title data-v-3d941d61">退款金额</text><view class="ss-flex refund-cause ss-col-center data-v-3d941d61"><text class="ss-m-r-20 data-v-3d941d61">￥{{d}}</text></view></view><view class="refund-item ss-flex ss-col-center ss-row-between data-v-3d941d61" bindtap="{{h}}"><text class="item-title data-v-3d941d61">申请原因</text><view class="ss-flex refund-cause ss-col-center data-v-3d941d61"><text wx:if="{{f}}" class="ss-m-r-20 data-v-3d941d61">{{g}}</text><text wx:else class="ss-m-r-20 data-v-3d941d61">请选择申请原因~</text><text class="cicon-forward data-v-3d941d61" style="height:28rpx"></text></view></view><view class="refund-item data-v-3d941d61"><view class="item-title ss-m-b-20 data-v-3d941d61">相关描述</view><view class="describe-box data-v-3d941d61"><uni-easyinput wx:if="{{j}}" class="describe-content data-v-3d941d61" u-i="3d941d61-3,3d941d61-2" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"/><view class="upload-img data-v-3d941d61"><s-uploader wx:if="{{l}}" class="data-v-3d941d61" u-i="3d941d61-4,3d941d61-2" bind:__l="__l" bindupdateUrl="{{k}}" u-p="{{l}}"/></view></view></view></uni-forms><su-fixed wx:if="{{r}}" class="data-v-3d941d61" u-s="{{['d']}}" u-i="3d941d61-5,3d941d61-0" bind:__l="__l" u-p="{{r}}"><view class="foot-wrap data-v-3d941d61"><view class="foot_box ss-flex ss-col-center ss-row-between ss-p-x-30 data-v-3d941d61"><button class="ss-reset-button contcat-btn data-v-3d941d61" bindtap="{{p}}"> 联系客服 </button><button class="ss-reset-button ui-BG-Main-Gradient sub-btn data-v-3d941d61" bindtap="{{q}}">提交</button></view></view></su-fixed><su-popup wx:if="{{x}}" class="data-v-3d941d61" u-s="{{['d']}}" bindclose="{{w}}" u-i="3d941d61-6,3d941d61-0" bind:__l="__l" u-p="{{x}}"><view class="modal-box page_box data-v-3d941d61"><view class="modal-head item-title head_box ss-flex ss-row-center ss-col-center data-v-3d941d61"> 申请原因 </view><view class="modal-content content_box data-v-3d941d61"><radio-group class="data-v-3d941d61" bindchange="{{t}}"><label wx:for="{{s}}" wx:for-item="item" wx:key="d" class="radio ss-flex ss-col-center data-v-3d941d61"><view class="ss-flex-1 ss-p-20 data-v-3d941d61">{{item.a}}</view><radio class="data-v-3d941d61" value="{{item.b}}" color="var(--ui-BG-Main)" checked="{{item.c}}"/></label></radio-group></view><view class="modal-foot foot_box ss-flex ss-row-center ss-col-center data-v-3d941d61"><button class="ss-reset-button close-btn ui-BG-Main-Gradient data-v-3d941d61" bindtap="{{v}}"> 确定 </button></view></view></su-popup></s-layout>