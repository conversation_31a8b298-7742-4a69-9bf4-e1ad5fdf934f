"use strict";const e=require("../../../common/vendor.js"),t=require("../../../sheep/index.js"),r=require("../../../sheep/api/trade/order.js");if(!Array){(e.resolveComponent("uni-swiper-dot")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../uni_modules/uni-swiper-dot/components/uni-swiper-dot/uni-swiper-dot.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const s={__name:"log",setup(s){const o=e.reactive({info:[],tracks:[]}),n=e.computed((()=>{let e=[];return o.info.items&&o.info.items.forEach((t=>{e.push({image:t.picUrl})})),e}));return e.onLoad((e=>{!async function(e){const{data:t}=await r.OrderApi.getOrderExpressTrackList(e);o.tracks=t.reverse()}(e.id),async function(e){const{data:t}=await r.OrderApi.getOrderDetail(e);o.info=t}(e.id)})),(r,s)=>e.e({a:n.value.length>0},n.value.length>0?{b:e.f(n.value,((r,s,o)=>({a:e.unref(t.sheep).$url.static(r.image),b:s}))),c:e.p({info:n.value,current:o.current,mode:"round"}),d:e.t(o.info.logisticsNo),e:e.t(o.info.logisticsName)}:{},{f:e.f(o.tracks,((r,s,n)=>e.e({a:o.tracks.length-1!==s},(o.tracks.length,{}),{b:e.t(r.content),c:e.t(e.unref(t.sheep).$helper.timeFormat(r.time,"yyyy-mm-dd hh:MM:ss")),d:r.title}))),g:e.p({title:"物流追踪"})})}},o=e._export_sfc(s,[["__scopeId","data-v-f40a4cdd"]]);wx.createPage(o);
