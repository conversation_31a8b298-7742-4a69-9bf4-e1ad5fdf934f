"use strict";const t=require("../../request/index.js"),i={getSeckillConfigList:()=>t.request({url:"promotion/seckill-config/list",method:"GET"}),getNowSeckillActivity:()=>t.request({url:"promotion/seckill-activity/get-now",method:"GET"}),getSeckillActivityPage:i=>t.request({url:"promotion/seckill-activity/page",method:"GET",params:i}),getSeckillActivityListByIds:i=>t.request({url:"/promotion/seckill-activity/list-by-ids",method:"GET",params:{ids:i}}),getSeckillActivity:i=>t.request({url:"promotion/seckill-activity/get-detail",method:"GET",params:{id:i}})};exports.SeckillApi=i;
