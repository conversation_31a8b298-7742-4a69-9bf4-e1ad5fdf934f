<s-layout wx:if="{{s}}" class="data-v-9499e2ac" u-s="{{['d']}}" style="{{r}}" u-i="9499e2ac-0" bind:__l="__l" u-p="{{s}}"><view class="page-bg data-v-9499e2ac" style="{{a}}"></view><view wx:if="{{b}}" class="header data-v-9499e2ac"><swiper class="data-v-9499e2ac" indicator-dots="true" autoplay="true" circular="{{true}}" interval="3000" duration="1500" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff"><block wx:for="{{c}}" wx:for-item="picUrl" wx:key="b"><swiper-item class="borRadius14 data-v-9499e2ac"><image src="{{picUrl.a}}" class="slide-image borRadius14 data-v-9499e2ac" lazy-load/></swiper-item></block></swiper></view><view class="flex align-center justify-between ss-p-25 data-v-9499e2ac"><view class="time-icon data-v-9499e2ac"><image class="ss-w-100 ss-h-100 data-v-9499e2ac" src="http://mall.yudao.iocoder.cn/static/images/priceTag.png"/></view><scroll-view class="time-list data-v-9499e2ac" scroll-into-view="{{e}}" scroll-x scroll-with-animation><view wx:for="{{d}}" wx:for-item="config" wx:key="c" class="{{['data-v-9499e2ac', 'item', config.d]}}" id="{{config.e}}" bindtap="{{config.f}}"><view class="time data-v-9499e2ac">{{config.a}}</view><view class="status data-v-9499e2ac">{{config.b}}</view></view></scroll-view></view><view class="list-content data-v-9499e2ac"><view class="content-header ss-flex-col ss-col-center ss-row-center data-v-9499e2ac"><view class="content-header-box ss-flex ss-row-center data-v-9499e2ac"><view wx:if="{{f}}" class="countdown-box ss-flex data-v-9499e2ac"><view class="countdown-title ss-m-r-12 data-v-9499e2ac">距结束</view><view class="ss-flex countdown-time data-v-9499e2ac"><view class="ss-flex countdown-h data-v-9499e2ac">{{g}}</view><view class="ss-m-x-4 data-v-9499e2ac">:</view><view class="countdown-num ss-flex ss-row-center data-v-9499e2ac">{{h}}</view><view class="ss-m-x-4 data-v-9499e2ac">:</view><view class="countdown-num ss-flex ss-row-center data-v-9499e2ac">{{i}}</view></view></view><view wx:else class="data-v-9499e2ac">{{j}}</view></view></view><scroll-view class="scroll-box data-v-9499e2ac" style="{{'height:' + q}}" scroll-y="true" scroll-with-animation="{{false}}" enable-back-to-top="{{true}}"><view wx:for="{{k}}" wx:for-item="activity" wx:key="k" class="goods-box ss-m-b-20 data-v-9499e2ac"><s-goods-column wx:if="{{activity.j}}" class="data-v-9499e2ac" u-s="{{['activity','cart']}}" u-i="{{activity.i}}" bind:__l="__l" u-p="{{activity.j}}"><view slot="activity"><view class="limit data-v-9499e2ac"> 限量 <text class="ss-m-l-5 data-v-9499e2ac">{{activity.a}} {{activity.b}}</text></view><su-progress wx:if="{{activity.d}}" class="data-v-9499e2ac" u-i="{{activity.c}}" bind:__l="__l" u-p="{{activity.d}}"/></view><view slot="cart"><button wx:if="{{l}}" class="{{['data-v-9499e2ac', 'ss-reset-button cart-btn', activity.e]}}"><label class="data-v-9499e2ac">未开始</label></button><button wx:elif="{{m}}" class="{{['data-v-9499e2ac', 'ss-reset-button cart-btn', activity.f]}}" bindtap="{{activity.g}}"><label class="data-v-9499e2ac">马上抢</label></button><button wx:else class="{{['data-v-9499e2ac', 'ss-reset-button cart-btn', activity.h]}}"><label class="data-v-9499e2ac">已结束</label></button></view></s-goods-column></view><uni-load-more wx:if="{{n}}" class="data-v-9499e2ac" bindtap="{{o}}" u-i="9499e2ac-3,9499e2ac-0" bind:__l="__l" u-p="{{p}}"/></scroll-view></view></s-layout>