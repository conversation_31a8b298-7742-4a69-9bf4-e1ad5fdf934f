<s-layout wx:if="{{v}}" u-s="{{['d']}}" class="wallet-wrap data-v-e29a2373" style="{{t}}" u-i="e29a2373-0" bind:__l="__l" u-p="{{v}}"><view class="header-box ss-flex ss-row-center ss-col-center data-v-e29a2373"><view class="card-box ui-BG-Main ui-Shadow-Main data-v-e29a2373"><view class="card-head ss-flex ss-col-center data-v-e29a2373"><view class="card-title ss-m-r-10 data-v-e29a2373">钱包余额（元）</view><view bindtap="{{a}}" class="{{['ss-eye-icon', 'data-v-e29a2373', b]}}"/></view><view class="ss-flex ss-row-between ss-col-center ss-m-t-64 data-v-e29a2373"><view class="money-num data-v-e29a2373">{{c}}</view><button class="ss-reset-button topup-btn data-v-e29a2373" bindtap="{{d}}"> 充值 </button></view></view></view><su-sticky class="data-v-e29a2373" u-s="{{['d']}}" u-i="e29a2373-1,e29a2373-0" bind:__l="__l"><view class="filter-box ss-p-x-30 ss-flex ss-col-center ss-row-between data-v-e29a2373"><uni-datetime-picker wx:if="{{h}}" class="data-v-e29a2373" u-s="{{['d']}}" bindchange="{{f}}" u-i="e29a2373-2,e29a2373-1" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"><button class="ss-reset-button date-btn data-v-e29a2373"><text class="data-v-e29a2373">{{e}}</text><text class="cicon-drop-down ss-seldate-icon data-v-e29a2373"></text></button></uni-datetime-picker><view class="total-box data-v-e29a2373"><view class="ss-m-b-10 data-v-e29a2373">总收入￥{{i}}</view><view class="data-v-e29a2373">总支出￥{{j}}</view></view></view><su-tabs wx:if="{{l}}" class="data-v-e29a2373" bindchange="{{k}}" u-i="e29a2373-3,e29a2373-1" bind:__l="__l" u-p="{{l}}"></su-tabs></su-sticky><s-empty wx:if="{{m}}" class="data-v-e29a2373" u-i="e29a2373-4,e29a2373-0" bind:__l="__l" u-p="{{n}}"/><view wx:if="{{o}}" class="data-v-e29a2373"><view wx:for="{{p}}" wx:for-item="item" wx:key="e" class="wallet-list ss-flex border-bottom data-v-e29a2373"><view class="list-content data-v-e29a2373"><view class="title-box ss-flex ss-row-between ss-m-b-20 data-v-e29a2373"><text class="title ss-line-1 data-v-e29a2373">{{item.a}}</text><view class="money data-v-e29a2373"><text wx:if="{{item.b}}" class="add data-v-e29a2373">+{{item.c}}</text><text wx:else class="minus data-v-e29a2373">{{item.d}}</text></view></view><text class="time data-v-e29a2373">{{q}}</text></view></view></view><uni-load-more wx:if="{{r}}" class="data-v-e29a2373" u-i="e29a2373-5,e29a2373-0" bind:__l="__l" u-p="{{s}}"/></s-layout>