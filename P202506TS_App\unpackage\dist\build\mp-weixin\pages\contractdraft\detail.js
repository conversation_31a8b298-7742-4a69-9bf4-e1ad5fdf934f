"use strict";const e=require("../../common/vendor.js");require("../../sheep/index.js");const n=require("../../sheep/util/index.js"),o=require("../../sheep/api/member/contractDraft.js");if(!Array){(e.resolveComponent("uni-forms-item")+e.resolveComponent("zero-markdown-view")+e.resolveComponent("uni-forms")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/zero-markdown-view/components/zero-markdown-view/zero-markdown-view.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"detail",setup(t){const i=e.reactive({id:"",model:{},content:""});e.computed((()=>e.index.getStorageSync("enterprise")));return e.onLoad((n=>{i.id=n.id,(async()=>{const{data:n}=await o.ContractDraftApi.getContractDraft(i.id);i.model=e.clone(n)})()})),(o,t)=>e.e({a:e.t(i.model.draftName||"未命名稿件"),b:e.p({name:"draftName",label:"稿件名称"}),c:e.t(i.model.draftType),d:e.p({name:"draftType",label:"稿件类型"}),e:e.t(i.model.status),f:e.p({name:"status",label:"状态"}),g:e.t(i.model.summary),h:e.p({name:"summary",label:"摘要"}),i:e.t(e.unref(n.formatDate)(i.model.draftTime)),j:e.p({name:"draftTime",label:"创建时间"}),k:e.t(e.unref(n.formatDate)(i.model.lastModifyTime)),l:e.p({name:"lastModifyTime",label:"最后修改时间"}),m:"md"===i.model.fileType},"md"===i.model.fileType?e.e({n:!i.content},i.content?{p:e.p({markdown:i.content})}:{o:e.o((n=>(async n=>{e.index.showLoading({title:"加载中",mask:!0,fail:()=>{e.index.hideLoading()}}),e.index.request({url:n,method:"GET",header:{Accept:"text/json","Content-Type":"application/json;charset=UTF-8"}}).then((e=>{i.content=e.data})).catch((()=>{e.index.showToast({title:"文件获取失败",icon:"none"})})).finally((()=>{e.index.hideLoading()}))})(i.model.files)))}):{},{q:"pdf"===i.model.fileType},"pdf"===i.model.fileType?{r:e.o((n=>(async n=>{e.index.showLoading({title:"加载中",mask:!0,fail:()=>{e.index.hideLoading()}}),e.index.request({url:n,method:"GET",dataType:"arraybuffer",header:{Accept:"text/json","Content-Type":"application/json;charset=UTF-8"},responseType:"arraybuffer"}).then((n=>{const o=e.index.getFileSystemManager(),t=n.header["Content-Disposition"]?decodeURIComponent(n.header["Content-Disposition"].match(/filename="?(.+)"?/)[1]):"稿件.pdf";o.writeFile({filePath:e.wx$1.env.USER_DATA_PATH+"/"+t,data:n.data,encoding:"binary",success(n){e.index.openDocument({filePath:e.wx$1.env.USER_DATA_PATH+"/"+t,showMenu:!0,success:n=>{e.index.hideLoading(),e.index.showToast({title:"文件获取成功，请通过右上角菜单进行分享保存",icon:"none"})},fail:n=>{e.index.hideLoading(),e.index.showToast({title:"打开文件失败",icon:"none"})}})},fail(n){e.index.hideLoading(),e.index.showToast({title:"写文件失败",icon:"none"})}})})).catch((()=>{e.index.hideLoading(),e.index.showToast({title:"文件获取失败",icon:"none"})}))})(i.model.files)))}:{},{s:e.p({model:i.model,rules:i.rules,labelPosition:"left",border:!0}),t:e.p({title:"稿件详情"})})}},i=e._export_sfc(t,[["__scopeId","data-v-1f633e30"]]);wx.createPage(i);
