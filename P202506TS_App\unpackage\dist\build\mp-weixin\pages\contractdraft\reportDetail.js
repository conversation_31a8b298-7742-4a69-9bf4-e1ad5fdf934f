"use strict";const e=require("../../common/vendor.js");if(require("../../sheep/index.js"),!Array){(e.resolveComponent("zero-markdown-view")+e.resolveComponent("uni-forms")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/zero-markdown-view/components/zero-markdown-view/zero-markdown-view.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const o={__name:"reportDetail",setup(o){const n=e.reactive({id:"",model:{},content:""});e.computed((()=>e.index.getStorageSync("enterprise")));return e.onLoad((o=>{(async o=>{e.index.showLoading({title:"加载中",mask:!0,fail:()=>{e.index.hideLoading()}}),e.index.request({url:o,method:"GET",header:{Accept:"text/json","Content-Type":"application/json;charset=UTF-8"}}).then((e=>{n.content=e.data})).catch((()=>{e.index.showToast({title:"文件获取失败",icon:"none"})})).finally((()=>{e.index.hideLoading()}))})(o.url)})),(o,t)=>({a:e.p({markdown:n.content}),b:e.p({model:n.model,rules:n.rules,labelPosition:"left",border:!0}),c:e.p({title:"诊断报告"})})}},n=e._export_sfc(o,[["__scopeId","data-v-617e58e1"]]);wx.createPage(n);
