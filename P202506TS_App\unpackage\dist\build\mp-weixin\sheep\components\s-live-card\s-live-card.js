"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),a={__name:"s-live-card",props:{goodsFields:{type:[Array,Object],default:()=>({})},tagStyle:{type:Object,default:{}},data:{type:Object,default:{}},size:{type:String,default:"sl"},background:{type:String,default:""},topRadius:{type:Number,default:0},bottomRadius:{type:Number,default:0},titleColor:{type:String,default:"#333"},subTitleColor:{type:String,default:"#999999"}},emits:["click","getHeight"],setup(a,{emit:s}){const i=a,o=t.computed((()=>({background:i.background,"border-top-left-radius":i.topRadius+"px","border-top-right-radius":i.topRadius+"px","border-bottom-left-radius":i.bottomRadius+"px","border-bottom-right-radius":i.bottomRadius+"px"}))),r=t.reactive({liveStatus:{101:{img:e.sheep.$url.static("/static/img/shop/app/mplive/living.png"),title:"直播中"},102:{img:e.sheep.$url.static("/static/img/shop/app/mplive/start.png"),title:"未开始"},103:{img:e.sheep.$url.static("/static/img/shop/app/mplive/ended.png"),title:"已结束"}}}),l=s,d=()=>{l("click")};return(s,i)=>t.e({a:"md"===a.size},"md"===a.size?{b:r.liveStatus[a.data.status].img,c:t.t(r.liveStatus[a.data.status].title),d:t.unref(e.sheep).$url.cdn(a.data.feeds_img),e:t.t(a.data.name),f:t.s({color:a.titleColor}),g:t.t(a.data.anchor_name),h:t.s({color:a.subTitleColor}),i:t.s(o.value),j:t.o(d)}:{},{k:"sl"===a.size},"sl"===a.size?{l:r.liveStatus[a.data.status].img,m:t.t(r.liveStatus[a.data.status].title),n:t.unref(e.sheep).$url.cdn(a.data.feeds_img),o:t.t(a.data.name),p:t.s({color:a.titleColor}),q:t.t(a.data.anchor_name),r:t.s({color:a.subTitleColor}),s:t.s(o.value),t:t.o(d)}:{})}},s=t._export_sfc(a,[["__scopeId","data-v-bedb20fb"]]);wx.createComponent(s);
