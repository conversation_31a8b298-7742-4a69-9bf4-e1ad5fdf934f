"use strict";const e=require("../../../common/vendor.js"),t=require("../../../sheep/index.js"),o=require("../../../sheep/api/promotion/point.js");if(!Array){(e.resolveComponent("s-point-card")+e.resolveComponent("s-empty")+e.resolveComponent("uni-load-more"))()}Math||((()=>"../../../sheep/components/s-point-card/s-point-card.js")+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+a)();const a=()=>"../../../sheep/components/s-layout/s-layout.js",n={__name:"list",setup(a){const{safeAreaInsets:n,safeArea:s}=t.sheep.$platform.device,i=2*t.sheep.$platform.device.statusBarHeight,r=2*(s.height+n.bottom)+i-t.sheep.$platform.navbar-350,p=e.ref(),l=e.reactive({pageNo:1,pageSize:5}),c=e.ref(0),u=e.ref(0),m=e.ref("");async function v(){m.value="loading";const{data:e}=await o.PointApi.getPointActivityPage(l);await p.value.concatActivity(e.list),u.value=p.value.getActivityCount(),c.value=e.total,m.value=u.value<c.value?"more":"noMore"}function d(){"noMore"!==m.value&&(l.pageNo+=1,v())}return e.onReachBottom((()=>{d()})),e.onLoad((()=>{v()})),(t,o)=>e.e({a:e.sr(p,"1cb01478-1,1cb01478-0",{k:"sPointCardRef"}),b:0===c.value},0===c.value?{c:e.p({icon:"/static/goods-empty.png",text:"暂无积分商品"})}:{},{d:c.value>0},c.value>0?{e:e.o(d),f:e.p({status:m.value,"content-text":{contentdown:"上拉加载更多"}})}:{},{g:r+"rpx",h:e.p({title:"积分商城",navbar:"normal",leftWidth:0,rightWidth:0})})}};wx.createPage(n);
