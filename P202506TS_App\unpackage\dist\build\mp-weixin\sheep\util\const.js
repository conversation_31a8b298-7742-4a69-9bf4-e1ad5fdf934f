"use strict";const e=require("../../common/vendor.js"),a=0,t=10,n=20,p=31,o={WAIT_START:"即将开始",STARTED:"进行中",END:"已结束"};exports.CouponTemplateValidityTypeEnum={DATE:{type:1,name:"固定日期可用"},TERM:{type:2,name:"领取之后可用"}},exports.DeliveryTypeEnum={EXPRESS:{type:1,name:"快递发货"},PICK_UP:{type:2,name:"用户自提"}},exports.PromotionActivityTypeEnum={NORMAL:{type:0,name:"普通"},SECKILL:{type:1,name:"秒杀"},BARGAIN:{type:2,name:"砍价"},COMBINATION:{type:3,name:"拼团"},POINT:{type:4,name:"积分商城"}},exports.PromotionDiscountTypeEnum={PRICE:{type:1,name:"满减"},PERCENT:{type:2,name:"折扣"}},exports.SharePageEnum={HOME:{name:"首页",page:"/pages/index/index",value:"1"},GOODS:{name:"普通商品页",page:"/pages/goods/index",value:"2"},GROUPON:{name:"拼团商品页",page:"/pages/goods/groupon",value:"3"},SECKILL:{name:"秒杀商品页",page:"/pages/goods/seckill",value:"4"},GROUPON_DETAIL:{name:"参与拼团页",page:"/pages/activity/groupon/detail",value:"5"},POINT:{name:"积分商品页",page:"/pages/goods/point",value:"6"}},exports.TimeStatusEnum=o,exports.WxaSubscribeTemplate={TRADE_ORDER_DELIVERY:"订单发货通知",PROMOTION_COMBINATION_SUCCESS:"拼团结果通知",PAY_WALLET_RECHARGER_SUCCESS:"充值成功通知"},exports.getTerminal=()=>{switch(e.index.getSystemInfoSync().uniPlatform){case"app":return p;case"web":return n;case"mp-weixin":return t;default:return a}},exports.getTimeStatusEnum=(a,t)=>{const n=e.dayjs();return n.isBefore(a)?o.WAIT_START:n.isAfter(t)?o.END:o.STARTED};
