"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js");if(!Array){e.resolveComponent("s-layout")()}Math;const s={__name:"contract",setup(s){e.useCssVars((t=>({"325c8c6e":e.unref(r)})));const a=0*t.sheep.$platform.device.statusBarHeight*2,r=t.sheep.$url.css("/assets/mp/index/bg_header.png"),n=e.computed((()=>{var e;return null==(e=t.sheep.$store("app").template)?void 0:e.home}));e.reactive({style:"second_one",categoryList:[],activeMenu:0,pagination:{list:[],total:[],pageNo:1,pageSize:6},loadStatus:""});const{safeArea:o}=t.sheep.$platform.device;e.computed((()=>o.height-44-50));const c=async()=>{e.index.showModal({content:"请前往电脑端进行操作",showCancel:!1})};return e.onLoad((e=>{})),(s,r)=>({a:e.s({marginTop:Number(a)+"rpx"}),b:e.unref(t.sheep).$url.static("/assets/mp/contract/img_btn1.png"),c:e.o((s=>e.unref(t.sheep).$router.go("/pages/contract/list"))),d:e.unref(t.sheep).$url.static("/assets/mp/contract/img_btn2.png"),e:e.o(c),f:e.unref(t.sheep).$url.static("/assets/mp/contract/img_btn3.png"),g:e.unref(t.sheep).$url.static("/assets/mp/contract/img_upload.png"),h:e.o((s=>e.unref(t.sheep).$router.go("/pages/contract/create"))),i:e.s(s.__cssVars()),j:e.p({title:"财税合同",tabbar:"/pages/index/contract",navbar:"custom",bgStyle:n.value.page,navbarStyle:n.value.navigationBar,onShareAppMessage:!0})})}},a=e._export_sfc(s,[["__scopeId","data-v-d4404042"]]);s.__runtimeHooks=2,wx.createPage(a);
