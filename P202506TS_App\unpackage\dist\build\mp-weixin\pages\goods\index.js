"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),t=require("../../sheep/api/promotion/coupon.js"),s=require("../../sheep/api/promotion/activity.js"),i=require("../../sheep/api/product/favorite.js"),n=require("../../sheep/api/promotion/rewardActivity.js"),r=require("../../sheep/hooks/useGoods.js"),d=require("../../sheep/api/product/spu.js"),c=require("../../sheep/api/trade/order.js"),a=require("../../sheep/util/const.js");if(!Array){(e.resolveComponent("s-empty")+e.resolveComponent("su-swiper")+e.resolveComponent("s-select-sku")+e.resolveComponent("s-activity-pop")+e.resolveComponent("s-layout"))()}Math||(p+m+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/ui/su-swiper/su-swiper.js")+k+u+(()=>"../../sheep/components/s-select-sku/s-select-sku.js")+g+f+y+l+(()=>"../../sheep/components/s-activity-pop/s-activity-pop.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const p=()=>"./components/detail/detail-navbar.js",u=()=>"./components/detail/detail-cell-sku.js",l=()=>"./components/detail/detail-tabbar.js",m=()=>"./components/detail/detail-skeleton.js",g=()=>"./components/detail/detail-comment-card.js",f=()=>"./components/detail/detail-content-card.js",y=()=>"./components/detail/detail-activity-tip.js",k=()=>"../../sheep/components/countDown/index.js",h={__name:"index",setup(p){e.onPageScroll((()=>{}));const u={bgColor:"#E93323",Color:"#fff",width:"44rpx",timeTxtwidth:"16rpx",isDay:!0},l=e.computed((()=>o.sheep.$store("user").isLogin)),m=e.reactive({goodsId:0,skeletonLoading:!0,goodsInfo:{},showSelectSku:!1,selectedSku:{},settlementSku:{},showModel:!1,couponInfo:[],showActivityModel:!1,rewardActivity:{},activityList:[]});function g(e){m.selectedSku=e,m.settlementSku=e}function f(e){e.id?o.sheep.$store("cart").add(e):o.sheep.$helper.toast("请选择商品规格")}function y(e){e.id?o.sheep.$router.go("/pages/order/confirm",{data:JSON.stringify({items:[{skuId:e.id,count:e.goods_num,categoryId:m.goodsInfo.categoryId}]})}):o.sheep.$helper.toast("请选择商品规格")}function k(){m.showActivityModel=!0}async function h(o){const{code:s}=await t.CouponApi.takeCoupon(o);0===s&&(e.index.showToast({title:"领取成功"}),setTimeout((()=>{S()}),1e3))}const I=e.computed((()=>e.isEmpty(m.goodsInfo)?{}:o.sheep.$platform.share.getShareInfo({title:m.goodsInfo.name,image:o.sheep.$url.cdn(m.goodsInfo.picUrl),desc:m.goodsInfo.introduction,params:{page:a.SharePageEnum.GOODS.value,query:m.goodsInfo.id}},{type:"goods",title:m.goodsInfo.name,image:o.sheep.$url.cdn(m.goodsInfo.picUrl),price:r.fen2yuan(m.goodsInfo.price),original_price:r.fen2yuan(m.goodsInfo.marketPrice)})));async function S(){const{code:e,data:o}=await t.CouponApi.getCouponTemplateList(m.goodsId,2,10);0===e&&(m.couponInfo=o)}async function v(e){let{data:o,code:t}=await c.OrderApi.getSettlementProduct(e);0===t&&1===o.length&&(o=o[0],m.goodsInfo.skus.forEach((e=>{o.skus.forEach((o=>{e.id===o.id&&(e.promotionType=o.promotionType,e.promotionPrice=o.promotionPrice,e.promotionId=o.promotionId,e.promotionEndTime=o.promotionEndTime)}))})),m.settlementSku=m.goodsInfo.skus.filter((e=>e.stock>0&&e.promotionPrice>0)).reduce(((e,o)=>e.promotionPrice<o.promotionPrice?e:o),[]),o.rewardActivity&&(m.rewardActivity=o.rewardActivity,async function(e){const{code:o,data:t}=await n.RewardActivityApi.getRewardActivity(e);0===o&&(m.rewardActivity.startTime=t.startTime,m.rewardActivity.endTime=t.endTime)}(m.rewardActivity.id)))}return e.onLoad((e=>{e.id?(m.goodsId=e.id,d.SpuApi.getSpuDetail(m.goodsId).then((e=>{0===e.code&&e.data?(m.skeletonLoading=!1,m.goodsInfo=e.data,l.value&&i.FavoriteApi.isFavoriteExists(m.goodsId,"goods").then((e=>{0===e.code&&(m.goodsInfo.favorite=e.data)}))):m.goodsInfo=null})),S(),s.ActivityApi.getActivityListBySpuId(m.goodsId).then((e=>{0===e.code&&(m.activityList=e.data)})),v(m.goodsId)):m.goodsInfo=null})),(t,s)=>e.e({a:m.skeletonLoading},m.skeletonLoading?{}:null===m.goodsInfo?{c:e.p({text:"商品不存在或已下架",icon:"/static/soldout-empty.png",showAction:!0,actionText:"再逛逛",actionUrl:"/pages/goods/list"})}:e.e({d:e.p({isPreview:!0,list:e.unref(r.formatGoodsSwiper)(m.goodsInfo.sliderPicUrls),otStyle:"tag",imageMode:"widthFix",dotCur:"bg-mask-40",seizeHeight:750}),e:m.settlementSku&&m.settlementSku.id&&m.settlementSku.promotionPrice},m.settlementSku&&m.settlementSku.id&&m.settlementSku.promotionPrice?e.e({f:e.unref(o.sheep).$url.static("/static/img/shop/goods/dis.png"),g:e.t(e.unref(r.fen2yuan)(m.settlementSku.promotionPrice)),h:e.t(e.unref(r.fen2yuan)(m.settlementSku.price-m.settlementSku.promotionPrice)),i:4===m.settlementSku.promotionType},(4===m.settlementSku.promotionType||m.settlementSku.promotionType,{}),{j:6===m.settlementSku.promotionType,k:e.t(e.unref(r.fen2yuan)(m.settlementSku.price)),l:e.t(m.settlementSku.stock),m:m.settlementSku.promotionEndTime>0},m.settlementSku.promotionEndTime>0?{n:e.p({tipText:" ",bgColor:u,dayText:":",hourText:":",minuteText:":",secondText:" ",datatime:m.settlementSku.promotionEndTime/1e3,isDay:!1})}:{}):{},{o:!m.settlementSku.promotionPrice},m.settlementSku.promotionPrice?{}:e.e({p:e.t(e.unref(r.fen2yuan)(m.selectedSku.price||m.goodsInfo.price)),q:m.goodsInfo.marketPrice>m.goodsInfo.price},m.goodsInfo.marketPrice>m.goodsInfo.price?{r:e.t(e.unref(r.fen2yuan)(m.selectedSku.marketPrice||m.goodsInfo.marketPrice))}:{},{s:e.t(e.unref(r.formatSales)("exact",m.goodsInfo.salesCount))}),{t:e.f(m.couponInfo.slice(0,1),((o,t,s)=>({a:e.t(e.unref(r.fen2yuanSimple)(o.usePrice)),b:e.t(1===o.discountType?"减"+e.unref(r.fen2yuanSimple)(o.discountPrice)+"元":"打"+e.unref(r.formatDiscountPercent)(o.discountPercent)+"折"),c:o.id,d:e.o(k,o.id)}))),v:e.f(e.unref(r.getRewardActivityRuleItemDescriptions)(m.rewardActivity).slice(0,3-m.couponInfo.slice(0,1).length),((o,t,s)=>({a:e.t(o),b:o,c:e.o(k,o)}))),w:m.couponInfo.length},m.couponInfo.length?{x:e.o(k)}:{},{y:e.t(m.goodsInfo.name),z:e.t(m.goodsInfo.introduction),A:e.o((e=>m.showSelectSku=!0)),B:e.o((e=>m.selectedSku.goods_sku_text=e)),C:e.p({sku:m.selectedSku,modelValue:m.selectedSku.goods_sku_text}),D:e.o(f),E:e.o(y),F:e.o(g),G:e.o((e=>m.showSelectSku=!1)),H:e.p({goodsInfo:m.goodsInfo,show:m.showSelectSku}),I:e.p({goodsId:m.goodsId}),J:e.p({content:m.goodsInfo.description}),K:m.activityList.length>0},m.activityList.length>0?{L:e.p({"activity-list":m.activityList})}:{},{M:m.goodsInfo.stock>0},m.goodsInfo.stock>0?{N:e.o((e=>m.showSelectSku=!0)),O:e.o((e=>m.showSelectSku=!0))}:{},{P:e.o((e=>m.goodsInfo=e)),Q:e.p({modelValue:m.goodsInfo}),R:e.o((e=>m.showActivityModel=!1)),S:e.o(h),T:e.o((e=>m=e)),U:e.p({show:m.showActivityModel,modelValue:m})}),{b:null===m.goodsInfo,V:e.p({onShareAppMessage:I.value,navbar:"goods"})})}},I=e._export_sfc(h,[["__scopeId","data-v-ca6ee35a"]]);h.__runtimeHooks=3,wx.createPage(I);
