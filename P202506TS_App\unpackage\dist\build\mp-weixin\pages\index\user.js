"use strict";const e=require("../../common/vendor.js"),s=require("../../sheep/index.js"),o=require("../../sheep/api/member/auth.js"),t=require("../../sheep/config/index.js");if(!Array){(e.resolveComponent("s-user-card")+e.resolveComponent("uni-list-item")+e.resolveComponent("uni-list")+e.resolveComponent("su-popup")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-user-card/s-user-card.js")+(()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../sheep/ui/su-popup/su-popup.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const r={__name:"user",setup(r){e.useCssVars((s=>({"2e22b005":e.unref(i)}))),e.index.hideTabBar({fail:()=>{}});const l=0*s.sheep.$platform.device.statusBarHeight*2,i=s.sheep.$url.css("/assets/mp/index/bg_header.png"),a=e.computed((()=>s.sheep.$store("app").template.user));e.computed((()=>s.sheep.$store("user").isLogin));const n=e.reactive({showCall:!1,phoneNumbers:["0532-89858668","0532-89858778"]}),u=[{showArrow:!0,title:"我的订单",titleColor:"#333",subtitle:"",subtitleColor:"#bbb"},{showArrow:!0,title:"税务风险检测设置",titleColor:"#333",subtitle:"",subtitleColor:"#bbb",url:""},{showArrow:!0,title:"修改密码",titleColor:"#333",subtitle:"",subtitleColor:"#bbb"},{showArrow:!0,title:"客服电话",titleColor:"#333",subtitle:"",subtitleColor:"#bbb"},{showArrow:!0,title:"当前版本",titleColor:"#333",subtitle:t.version,subtitleColor:"#bbb"},{showArrow:!1,title:"退出登录",titleColor:"#333"}],p=t=>{switch(console.log("menuTapped",t),t.title){case"我的订单":s.sheep.$router.go("/pages/order/list");break;case"我的企业":case"我的财税报告":e.index.makePhoneCall({phoneNumber:t.subtitle});break;case"税务风险检测设置":s.sheep.$router.go("/pages/tax/setting");break;case"财务自动检测开通":s.sheep.$router.go("/pages/finance/setting");break;case"修改密码":s.sheep.$router.go("/pages/user/change-password");break;case"客服电话":n.showCall=!0,console.log("state.showCall",n.showCall);break;case"退出登录":e.index.showModal({title:"提示",content:"确认退出账号？",success:async function(e){if(!e.confirm)return;const{code:t}=await o.AuthUtil.logout();0===t&&(s.sheep.$store("user").logout(),s.sheep.$router.go("/pages/index/login"))}})}};return e.onShow((()=>{s.sheep.$store("user").updateUserData()})),e.onPullDownRefresh((()=>{s.sheep.$store("user").updateUserData(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),e.onPageScroll((()=>{})),(o,t)=>({a:e.s({marginTop:Number(l)+"rpx"}),b:e.p({data:{space:20}}),c:e.f(u,((o,t,r)=>e.e({a:o.iconUrl},o.iconUrl?{b:e.unref(s.sheep).$url.cdn(o.iconUrl)}:{},{c:e.t(o.title),d:e.s({color:o.titleColor}),e:e.t(o.subtitle),f:e.s({color:o.subtitleColor}),g:t,h:e.o((e=>p(o)),t),i:"bc3ce210-3-"+r+",bc3ce210-2",j:e.p({showArrow:o.showArrow,clickable:!0})}))),d:e.p({border:!0}),e:e.f(n.phoneNumbers,((s,o,t)=>({a:e.t(s),b:o,c:e.o((o=>{return t=s,void e.index.makePhoneCall({phoneNumber:t});var t}),o)}))),f:e.o((e=>n.showCall=!1)),g:e.o((e=>n.showCall=!1)),h:e.p({show:n.showCall,type:"share"}),i:e.s(o.__cssVars()),j:e.p({title:"我的",tabbar:"/pages/index/user",navbar:"custom",bgStyle:a.value.page,navbarStyle:a.value.navigationBar,onShareAppMessage:!0})})}},l=e._export_sfc(r,[["__scopeId","data-v-bc3ce210"]]);r.__runtimeHooks=3,wx.createPage(l);
