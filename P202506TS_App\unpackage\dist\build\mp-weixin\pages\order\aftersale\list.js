"use strict";const t=require("../../../common/vendor.js"),e=require("../../../sheep/index.js"),a=require("../../../sheep/hooks/useGoods.js"),o=require("../../../sheep/api/trade/afterSale.js"),n=require("../../../sheep/util/index.js");if(!Array){(t.resolveComponent("su-tabs")+t.resolveComponent("su-sticky")+t.resolveComponent("s-empty")+t.resolveComponent("s-goods-item")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const i={__name:"list",setup(i){const s=t.reactive({currentTab:0,showApply:!1,pagination:{list:[],total:0,pageNo:1,pageSize:10},loadStatus:""}),r=[{name:"全部",value:"all"}];function l(t){n.resetPagination(s.pagination),s.currentTab=t.index,p()}async function p(){s.loadStatus="loading";let{data:e,code:n}=await o.AfterSaleApi.getAfterSalePage({pageNo:s.pagination.pageNo,pageSize:s.pagination.pageSize});0===n&&(e.list.forEach((t=>a.handleAfterSaleButtons(t))),s.pagination.list=t.lodash.concat(s.pagination.list,e.list),s.pagination.total=e.total,s.loadStatus=s.pagination.list.length<s.pagination.total?"more":"noMore")}function u(){"noMore"!==s.loadStatus&&(s.pagination.pageNo++,p())}return t.onLoad((async t=>{t.type&&(s.currentTab=t.type),await p()})),t.onReachBottom((()=>{u()})),(i,c)=>t.e({a:t.o(l),b:t.p({list:r,scrollable:!1,current:s.currentTab}),c:t.p({bgColor:"#fff"}),d:0===s.pagination.total},0===s.pagination.total?{e:t.p({icon:"/static/data-empty.png",text:"暂无数据"})}:{},{f:s.pagination.total>0},s.pagination.total>0?{g:t.f(s.pagination.list,((i,r,l)=>t.e({a:t.t(i.no),b:t.t(t.unref(a.formatAfterSaleStatus)(i)),c:"56d68fd2-4-"+l+",56d68fd2-0",d:t.p({img:i.picUrl,title:i.spuName,skuText:i.properties.map((t=>t.valueName)).join(" "),price:i.refundPrice}),e:t.t(10===i.way?"仅退款":"退款退货"),f:t.t(t.unref(a.formatAfterSaleStatusDescription)(i)),g:null==i?void 0:i.buttons.includes("cancel")},(null==i?void 0:i.buttons.includes("cancel"))?{h:t.o((e=>{return a=i.id,void t.index.showModal({title:"提示",content:"确定要取消此申请吗？",success:async function(t){if(!t.confirm)return;const{code:e}=await o.AfterSaleApi.cancelAfterSale(a);0===e&&(n.resetPagination(s.pagination),await p())}});var a}),i.id)}:{},{i:i.id,j:t.o((a=>t.unref(e.sheep).$router.go("/pages/order/aftersale/detail",{id:i.id})),i.id)})))}:{},{h:s.pagination.total>0},s.pagination.total>0?{i:t.o(u),j:t.p({status:s.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{k:t.p({title:"售后列表"})})}},s=t._export_sfc(i,[["__scopeId","data-v-56d68fd2"]]);wx.createPage(s);
