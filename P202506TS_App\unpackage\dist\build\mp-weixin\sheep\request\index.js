"use strict";const e=require("../../common/vendor.js"),r=require("../config/index.js"),o=require("../store/index.js"),t=require("../platform/index.js");require("../helper/index.js");const s=require("../api/member/auth.js"),n=require("../util/const.js");let a={target:null,count:0};function i(){a.count>0&&a.count--,0===a.count&&e.index.hideLoading()}const c=new e.Request({baseURL:r.baseUrl+r.apiPath,timeout:8e3,method:"GET",header:{Accept:"text/json","Content-Type":"application/json;charset=UTF-8",platform:t._platform.name},custom:{showSuccess:!1,successMsg:"",showError:!0,errorMsg:"",showLoading:!0,loadingMsg:"加载中",auth:!1}});c.interceptors.request.use((t=>{if(t.custom.auth&&!o.$store("user").isLogin)return e.index.reLaunch({url:"/pages/index/login"}),Promise.reject();t.custom.showLoading&&(a.count++,1===a.count&&e.index.showLoading({title:t.custom.loadingMsg,mask:!0,fail:()=>{e.index.hideLoading()}}));const s=l();return s&&(t.header.Authorization=s),t.header.terminal=n.getTerminal(),t.header.Accept="*/*",t.header["tenant-id"]=r.tenantId,t}),(e=>Promise.reject(e))),c.interceptors.response.use((r=>{var t,s;if(r.config.url.indexOf("/member/auth/")>=0&&(null==(s=null==(t=r.data)?void 0:t.data)?void 0:s.accessToken)&&o.$store("user").setToken(r.data.data.accessToken,r.data.data.refreshToken),r.config.custom.showLoading&&i(),0!==r.data.code){if(401===r.data.code)return h(r.config);(r.data.code+"").includes("1011007")?console.error(`分销用户绑定失败，原因：${r.data.msg}`):r.config.custom.showError&&e.index.showToast({title:r.data.msg||"服务器开小差啦,请稍后再试~",icon:"none",mask:!0})}return r.config.custom.showSuccess&&""!==r.config.custom.successMsg&&0===r.data.code&&e.index.showToast({title:r.config.custom.successMsg,icon:"none"}),Promise.resolve(r.data)}),(r=>{var t;const s=o.$store("user").isLogin;let n="网络请求出错";if(void 0!==r){switch(r.statusCode){case 400:n="请求错误";break;case 401:n=s?"您的登陆已过期":"请先登录";break;case 403:n="拒绝访问";break;case 404:n="请求出错";break;case 408:n="请求超时";break;case 429:n="请求频繁, 请稍后再访问";break;case 500:n="服务器开小差啦,请稍后再试~";break;case 501:n="服务未实现";break;case 502:n="网络错误";break;case 503:n="服务不可用";break;case 504:n="网络超时";break;case 505:n="HTTP 版本不受支持"}r.errMsg.includes("timeout")&&(n="请求超时")}return r&&r.config&&(!1===r.config.custom.showError&&e.index.showToast({title:(null==(t=r.data)?void 0:t.msg)||n,icon:"none",mask:!0}),r.config.custom.showLoading&&i()),!1}));let u=[],d=!1;const h=async e=>{if(e.url.indexOf("/member/auth/refresh-token")>=0)return Promise.reject("error");if(d)return new Promise((r=>{u.push((()=>{e.header.Authorization="Bearer "+l(),r(f(e))}))}));{d=!0;const o=m();if(!o)return g();try{if(0!==(await s.AuthUtil.refreshToken(o)).code)throw new Error("刷新令牌失败");return e.header.Authorization="Bearer "+l(),u.forEach((e=>{e()})),u=[],f(e)}catch(r){return u.forEach((e=>{e()})),g()}finally{u=[],d=!1}}},g=()=>{const r=o.$store("user");return r.logout(!0),e.index.reLaunch({url:"/pages/index/login"}),Promise.reject({code:401,msg:r.isLogin?"您的登陆已过期":"请先登录"})},l=()=>e.index.getStorageSync("token"),m=()=>e.index.getStorageSync("refresh-token"),f=e=>c.middleware(e),k=f;exports.getRefreshToken=m,exports.request=k;
