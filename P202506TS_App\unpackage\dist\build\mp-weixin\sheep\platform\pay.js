"use strict";const e=require("../../common/vendor.js"),a=require("../index.js");require("../helper/index.js");const t=require("../api/pay/order.js");function i(e,t,i){a.sheep.$router.redirect("/pages/pay/result",{id:e,orderType:t,payState:i})}exports.SheepPay=class{constructor(e,a,t){this.payment=e,this.id=t,this.orderType=a,this.payAction()}payAction(){return{WechatOfficialAccount:{wechat:()=>{this.wechatOfficialAccountPay()},alipay:()=>{this.redirectPay()},wallet:()=>{this.walletPay()},mock:()=>{this.mockPay()}},WechatMiniProgram:{wechat:()=>{this.wechatMiniProgramPay()},alipay:()=>{this.copyPayLink()},yeepay:()=>{this.yeepayMiniProgramPay()},wallet:()=>{this.walletPay()},mock:()=>{this.mockPay()}},App:{wechat:()=>{this.wechatAppPay()},alipay:()=>{this.alipay()},wallet:()=>{this.walletPay()},mock:()=>{this.mockPay()}},H5:{wechat:()=>{this.wechatWapPay()},alipay:()=>{this.redirectPay()},wallet:()=>{this.walletPay()},mock:()=>{this.mockPay()}}}[a.sheep.$platform.name][this.payment]()}prepay(e){return new Promise((async(i,s)=>{let n={id:this.id,channelCode:e,channelExtras:{}};if(["wx_pub","wx_lite"].includes(e)){const e=await a.sheep.$platform.useProvider("wechat").getOpenid();if(!e)return void this.bindWeixin();n.channelExtras.openid=e}if(["yeepay_lite"].includes(e)){const e=await a.sheep.$platform.useProvider("wechat").getOpenid();if(!e)return void this.bindWeixin();n.channelExtras.openId=e,n.channelExtras.appId="wx17092f8f87f31af3"}t.PayOrderApi.submitOrder(n).then((e=>{0===e.code&&i(e),0!==e.code&&e.msg.indexOf("无效的openid")>=0&&(e.msg.indexOf("无效的openid")>=0||e.msg.indexOf("下单账号与支付账号不一致")>=0)&&this.bindWeixin()}))}))}async wechatMiniProgramPay(){let{code:t,data:i}=await this.prepay("wx_lite");if(0!==t)return;const s=JSON.parse(i.displayContent);e.index.requestPayment({provider:"wxpay",timeStamp:s.timeStamp,nonceStr:s.nonceStr,package:s.packageValue,signType:s.signType,paySign:s.paySign,success:e=>{this.payResult("success")},fail:e=>{"requestPayment:fail cancel"===e.errMsg?a.sheep.$helper.toast("支付已手动取消"):this.payResult("fail")}})}async yeepayMiniProgramPay(){let{code:t,data:i}=await this.prepay("yeepay_lite");if(0!==t)return;const s=i.displayContent;console.log("易宝小程序支付prePayTn:",s);try{let t;t="string"==typeof s?JSON.parse(s):s,console.log("易宝支付参数:",t),e.index.requestPayment({provider:"wxpay",timeStamp:t.timeStamp,nonceStr:t.nonceStr,package:t.packageValue||t.package,signType:t.signType,paySign:t.paySign,success:e=>{console.log("易宝支付成功:",e),this.payResult("success")},fail:e=>{console.error("易宝支付失败:",e),"requestPayment:fail cancel"===e.errMsg?a.sheep.$helper.toast("支付已手动取消"):(a.sheep.$helper.toast("支付失败: "+e.errMsg),this.payResult("fail"))}})}catch(n){console.error("易宝支付参数解析失败:",n,"prePayTn:",s),a.sheep.$helper.toast("支付参数错误")}}async walletPay(){const{code:e}=await this.prepay("wallet");0===e&&this.payResult("success")}async mockPay(){const{code:e}=await this.prepay("mock");0===e&&this.payResult("success")}async copyPayLink(){let{code:t,data:i}=await this.prepay("alipay_wap");0===t&&e.index.showModal({title:"支付宝支付",content:"复制链接到外部浏览器",confirmText:"复制链接",success:e=>{e.confirm&&a.sheep.$helper.copyText(i.displayContent)}})}async alipay(){let t=this;const{error:i,data:s}=await this.prepay();0===i&&e.index.requestPayment({provider:"alipay",orderInfo:s.pay_data,success:e=>{t.payResult("success")},fail:e=>{"requestPayment:fail [paymentAlipay:62001]user cancel"===e.errMsg?a.sheep.$helper.toast("支付已手动取消"):t.payResult("fail")}})}async wechatAppPay(){let a=this,{error:t,data:i}=await this.prepay();0===t&&e.index.requestPayment({provider:"wxpay",orderInfo:i.pay_data,success:e=>{a.payResult("success")},fail:e=>{"requestPayment:fail cancel"!==e.errMsg&&a.payResult("fail")}})}payResult(e){i(this.id,this.orderType,e)}bindWeixin(){const t=this;e.index.showModal({title:"微信支付",content:"请先绑定微信再使用微信支付",success:async function(e){e.confirm&&(await a.sheep.$platform.useProvider("wechat").bind(),a.sheep.$helper.toast("微信绑定成功，正在发起支付..."),setTimeout((()=>{t.payAction()}),1e3))}})}},exports.getPayMethods=function(e){const t=[{icon:"/static/img/shop/pay/yeepay.png",title:"微信支付（易宝）",value:"yeepay",disabled:!0},{icon:"/static/img/shop/pay/wechat.png",title:"微信支付",value:"wechat",disabled:!0}],i=a.sheep.$platform.name,s=t[0];("H5"===i&&e.includes("yeepay_h5")||"WechatOfficialAccount"===i&&e.includes("yeepay_h5")||"WechatMiniProgram"===i&&e.includes("yeepay_lite")||"App"===i&&e.includes("yeepay_h5"))&&(s.disabled=!1);const n=t[1];return("WechatOfficialAccount"===i&&e.includes("wx_pub")||"WechatMiniProgram"===i&&e.includes("wx_lite")||"App"===i&&e.includes("wx_app"))&&(n.disabled=!1),t},exports.goPayResult=i;
