"use strict";const t=require("../../../common/vendor.js"),o={name:"countDown",props:{justifyLeft:{type:String,default:""},tipText:{type:String,default:"倒计时"},dayText:{type:String,default:"天"},hourText:{type:String,default:"时"},minuteText:{type:String,default:"分"},secondText:{type:String,default:"秒"},datatime:{type:Number,default:0},isDay:{type:Boolean,default:!0},isCol:{type:Boolean,default:!1},bgColor:{type:Object,default:null}},data:function(){return{day:"00",hour:"00",minute:"00",second:"00"}},created:function(){this.show_time()},mounted:function(){},methods:{show_time:function(){let t=this;function o(){let o=t.datatime-Date.parse(new Date)/1e3,e=0,r=0,l=0,i=0;o>0?(e=!0===t.isDay?Math.floor(o/86400):0,r=Math.floor(o/3600)-24*e,l=Math.floor(o/60)-24*e*60-60*r,i=Math.floor(o)-24*e*60*60-60*r*60-60*l,r<=9&&(r="0"+r),l<=9&&(l="0"+l),i<=9&&(i="0"+i),t.day=e,t.hour=r,t.minute=l,t.second=i):(t.day="00",t.hour="00",t.minute="00",t.second="00")}o(),setInterval(o,1e3)}}};const e=t._export_sfc(o,[["render",function(o,e,r,l,i,n){return t.e({a:r.tipText},r.tipText?{b:t.t(r.tipText)}:{},{c:!0===r.isDay},!0===r.isDay?{d:t.t(o.day),e:t.t(r.bgColor.isDay?"天":""),f:r.bgColor.bgColor,g:r.bgColor.Color}:{},{h:r.dayText},r.dayText?{i:t.t(r.dayText),j:r.bgColor.timeTxtwidth,k:r.bgColor.bgColor}:{},{l:t.t(o.hour),m:t.n(r.isCol?"timeCol":""),n:r.bgColor.bgColor,o:r.bgColor.Color,p:r.bgColor.width,q:r.hourText},r.hourText?{r:t.t(r.hourText),s:t.n(r.isCol?"whit":""),t:r.bgColor.timeTxtwidth,v:r.bgColor.bgColor}:{},{w:t.t(o.minute),x:t.n(r.isCol?"timeCol":""),y:r.bgColor.bgColor,z:r.bgColor.Color,A:r.bgColor.width,B:r.minuteText},r.minuteText?{C:t.t(r.minuteText),D:t.n(r.isCol?"whit":""),E:r.bgColor.timeTxtwidth,F:r.bgColor.bgColor}:{},{G:t.t(o.second),H:t.n(r.isCol?"timeCol":""),I:r.bgColor.bgColor,J:r.bgColor.Color,K:r.bgColor.width,L:r.secondText},r.secondText?{M:t.t(r.secondText)}:{},{N:t.s(r.justifyLeft)})}],["__scopeId","data-v-429a6cd3"]]);wx.createComponent(e);
