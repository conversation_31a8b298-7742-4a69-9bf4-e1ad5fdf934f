"use strict";const e=require("../../request/index.js"),r={bindBrokerageUser:r=>e.request({url:"/trade/brokerage-user/bind",method:"PUT",data:r}),getBrokerageUser:()=>e.request({url:"/trade/brokerage-user/get",method:"GET"}),getBrokerageUserSummary:()=>e.request({url:"/trade/brokerage-user/get-summary",method:"GET"}),getBrokerageRecordPage:r=>{void 0===r.status&&delete r.status;const t=Object.keys(r).map((e=>encodeURIComponent(e)+"="+r[e])).join("&");return e.request({url:`/trade/brokerage-record/page?${t}`,method:"GET"})},createBrokerageWithdraw:r=>e.request({url:"/trade/brokerage-withdraw/create",method:"POST",data:r}),getProductBrokeragePrice:r=>e.request({url:"/trade/brokerage-record/get-product-brokerage-price",method:"GET",params:{spuId:r}}),getRankByPrice:r=>{const t=`times=${r.times[0]}&times=${r.times[1]}`;return e.request({url:`/trade/brokerage-user/get-rank-by-price?${t}`,method:"GET"})},getBrokerageUserChildSummaryPageByPrice:r=>{const t=Object.keys(r).map((e=>encodeURIComponent(e)+"="+r[e])).join("&");return e.request({url:`/trade/brokerage-user/rank-page-by-price?${t}`,method:"GET"})},getBrokerageUserRankPageByUserCount:r=>{const t=Object.keys(r).map((e=>encodeURIComponent(e)+"="+r[e])).join("&");return e.request({url:`/trade/brokerage-user/rank-page-by-user-count?${t}`,method:"GET"})},getBrokerageUserChildSummaryPage:r=>e.request({url:"/trade/brokerage-user/child-summary-page",method:"GET",params:r})};exports.BrokerageApi=r;
