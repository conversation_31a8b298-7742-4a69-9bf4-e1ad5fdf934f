<s-layout wx:if="{{l}}" class="data-v-03f22df9" u-s="{{['d']}}" u-i="03f22df9-0" bind:__l="__l" u-p="{{l}}"><su-sticky wx:if="{{c}}" class="data-v-03f22df9" u-s="{{['d']}}" u-i="03f22df9-1,03f22df9-0" bind:__l="__l" u-p="{{c}}"><su-tabs wx:if="{{b}}" class="data-v-03f22df9" bindchange="{{a}}" u-i="03f22df9-2,03f22df9-1" bind:__l="__l" u-p="{{b}}"/></su-sticky><s-empty wx:if="{{d}}" class="data-v-03f22df9" u-i="03f22df9-3,03f22df9-0" bind:__l="__l" u-p="{{e}}"/><block wx:if="{{f}}"><view wx:for="{{g}}" wx:for-item="item" wx:key="h" class="data-v-03f22df9"><s-coupon-list wx:if="{{item.g}}" class="data-v-03f22df9" u-s="{{['d']}}" bindtap="{{item.e}}" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g}}"><button class="{{['ss-reset-button', 'card-btn', 'ss-flex', 'ss-row-center', 'ss-col-center', 'data-v-03f22df9', item.b]}}" catchtap="{{item.c}}" disabled="{{item.d}}">{{item.a}}</button></s-coupon-list></view></block><block wx:else><view wx:for="{{h}}" wx:for-item="item" wx:key="h" class="data-v-03f22df9"><s-coupon-list wx:if="{{item.g}}" class="data-v-03f22df9" u-s="{{['d']}}" bindtap="{{item.e}}" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g}}"><button class="{{['ss-reset-button', 'card-btn', 'ss-flex', 'ss-row-center', 'ss-col-center', 'data-v-03f22df9', item.b]}}" disabled="{{item.c}}" catchtap="{{item.d}}">{{item.a}}</button></s-coupon-list></view></block><uni-load-more wx:if="{{i}}" class="data-v-03f22df9" bindtap="{{j}}" u-i="03f22df9-6,03f22df9-0" bind:__l="__l" u-p="{{k}}"/></s-layout>