"use strict";const e=require("../../../common/vendor.js"),t={name:"AiProgress",components:{},props:{percentage:{type:[Number,String],required:!0},textInside:{type:Boolean,default:!1},strokeWidth:{type:[Number,String],default:6},duration:{type:[Number,String],default:2e3},isAnimate:{type:Boolean,default:!1},bgColor:{type:String,default:"linear-gradient(90deg, var(--ui-BG-Main) 0%, var(--ui-BG-Main-gradient) 100%)"},noData:{type:Boolean,default:!1},lineData:{type:Boolean,default:!1},inBgColor:{type:String,default:"#ebeef5"}},data:()=>({width:0,timer:null,containerWidth:0,contentWidth:0}),methods:{start(){if(this.isAnimate){const t=e.index.createSelectorQuery().in(this).selectAll("#container");e.index.createSelectorQuery().in(this).selectAll("#content"),t.boundingClientRect().exec((e=>{this.contentWidth=1*e[0][0].width*(1*this.percentage/100).toFixed(2)+"px"}))}}},mounted(){this.$nextTick((()=>{this.start()}))},created(){},filters:{},computed:{},watch:{},directives:{}};const n=e._export_sfc(t,[["render",function(t,n,i,a,o,r){return e.e({a:i.lineData},(i.lineData,{}),{b:i.isAnimate},i.isAnimate?e.e({c:i.textInside&&!i.noData},i.textInside&&!i.noData?{d:e.t(i.percentage)}:{},{e:i.strokeWidth+"px",f:i.bgColor,g:o.contentWidth,h:`width ${i.duration/1e3}s ease`}):{},{i:!i.isAnimate},i.isAnimate?{}:e.e({j:i.textInside&&!i.noData},i.textInside&&!i.noData?{k:e.t(i.percentage)}:{},{l:i.percentage+"%",m:i.strokeWidth+"px",n:i.bgColor}),{o:i.inBgColor,p:!(i.textInside||i.lineData||i.noData||i.isAnimate)},i.textInside||i.lineData||i.noData||i.isAnimate?{}:{q:e.t(i.percentage)})}],["__scopeId","data-v-c9e307e2"]]);wx.createComponent(n);
