"use strict";const e=require("../../../common/vendor.js"),t=e=>{const t=e.lastIndexOf("."),i=e.length;return{name:e.substring(0,t),ext:e.substring(t+1,i)}};exports.get_extname=e=>{if(Array.isArray(e))return e;return e.replace(/([\[\]])/g,"").split(",")},exports.get_file_data=async(i,a="image")=>{const s=t(i.name).ext.toLowerCase();let n={name:i.name,uuid:i.uuid,extname:s||"",cloudPath:i.cloudPath,fileType:i.fileType,url:i.url||i.path,size:i.size,image:{},path:i.path,video:{}};if("image"===a){const t=await(l=i.path,new Promise(((t,i)=>{e.index.getImageInfo({src:l,success(e){t(e)},fail(e){i(e)}})})));delete n.video,n.image.width=t.width,n.image.height=t.height,n.image.location=t.path}else delete n.image;var l;return n},exports.get_files_and_is_max=(i,a)=>{let s=[],n=[];return a&&0!==a.length?(i.tempFiles.forEach((e=>{const i=t(e.name).ext.toLowerCase();-1!==a.indexOf(i)&&(n.push(e),s.push(e.path))})),n.length!==i.tempFiles.length&&e.index.showToast({title:`当前选择了${i.tempFiles.length}个文件 ，${i.tempFiles.length-n.length} 个文件格式不正确`,icon:"none",duration:5e3}),{filePaths:s,files:n}):{filePaths:s,files:n}};
