"use strict";const e=require("../../request/index.js"),t={getPayWallet:()=>e.request({url:"/pay/wallet/get",method:"GET",custom:{showLoading:!1,auth:!0}}),getWalletTransactionPage:t=>{const a=Object.keys(t).map((e=>encodeURIComponent(e)+"="+t[e])).join("&");return e.request({url:`/pay/wallet-transaction/page?${a}`,method:"GET"})},getWalletTransactionSummary:t=>{const a=`createTime=${t.createTime[0]}&createTime=${t.createTime[1]}`;return e.request({url:`/pay/wallet-transaction/get-summary?${a}`,method:"GET"})},getWalletRechargePackageList:()=>e.request({url:"/pay/wallet-recharge-package/list",method:"GET",custom:{showError:!1,showLoading:!1}}),createWalletRecharge:t=>e.request({url:"/pay/wallet-recharge/create",method:"POST",data:t}),getWalletRechargePage:t=>e.request({url:"/pay/wallet-recharge/page",method:"GET",params:t,custom:{showError:!1,showLoading:!1}})};exports.PayWalletApi=t;
