"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),o={__name:"s-empty",props:{icon:{type:String,default:""},text:{type:String,default:""},showAction:{type:Boolean,default:!1},actionText:{type:String,default:""},actionUrl:{type:String,default:""},paddingTop:{type:String,default:"260"},buttonColor:{type:String,default:"var(--ui-BG-Main)"}},emits:["clickAction"],setup(o,{emit:n}){t.useCssVars((t=>({"353aa77b":o.buttonColor})));const i=o,a=n;function r(){""!==i.actionUrl&&e.sheep.$router.go(i.actionUrl),a("clickAction")}return(e,n)=>t.e({a:o.icon,b:""!==o.text},""!==o.text?{c:t.t(o.text)}:{},{d:o.showAction},o.showAction?{e:t.t(o.actionText),f:t.o(r)}:{},{g:t.s({paddingTop:o.paddingTop+"rpx"}),h:t.s(e.__cssVars())})}},n=t._export_sfc(o,[["__scopeId","data-v-4198cfab"]]);wx.createComponent(n);
