<s-layout wx:if="{{s}}" u-s="{{['d']}}" class="chat-wrap data-v-9ee391b2" u-i="9ee391b2-0" bind:__l="__l" u-p="{{s}}"><view class="page-bg data-v-9ee391b2" style="{{'height:' + a}}"></view><message-list class="r data-v-9ee391b2" u-s="{{['bottom']}}" u-r="messageListRef" u-i="9ee391b2-1,9ee391b2-0" bind:__l="__l"><message-input class="data-v-9ee391b2" bindonTools="{{b}}" bindsendMessage="{{c}}" u-i="9ee391b2-2,9ee391b2-1" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}" slot="bottom"></message-input></message-list><tools-popup wx:if="{{o}}" class="data-v-9ee391b2" u-s="{{['d']}}" bindclose="{{k}}" bindonEmoji="{{l}}" bindimageSelect="{{m}}" bindonShowSelect="{{n}}" u-i="9ee391b2-3,9ee391b2-0" bind:__l="__l" u-p="{{o}}"><message-input wx:if="{{j}}" class="data-v-9ee391b2" bindonTools="{{g}}" bindsendMessage="{{h}}" u-i="9ee391b2-4,9ee391b2-3" bind:__l="__l" bindupdateModelValue="{{i}}" u-p="{{j}}"></message-input></tools-popup><select-popup wx:if="{{r}}" class="data-v-9ee391b2" bindselect="{{p}}" bindclose="{{q}}" u-i="9ee391b2-5,9ee391b2-0" bind:__l="__l" u-p="{{r}}"/></s-layout>