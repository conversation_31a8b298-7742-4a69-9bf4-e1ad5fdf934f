"use strict";const e=require("../../../common/vendor.js"),i=require("../../config/index.js"),r=require("../../request/index.js"),t={uploadFile:r=>(e.index.getStorageSync("token"),e.index.showLoading({title:"上传中"}),new Promise(((t,n)=>{e.index.uploadFile({url:i.baseUrl+i.apiPath+"/infra/file/upload",filePath:r,name:"file",header:{Accept:"*/*","tenant-id":i.tenantId},success:i=>{let r=JSON.parse(i.data);if(1!==r.error)return t(r);e.index.showToast({icon:"none",title:r.msg})},fail:e=>(console.log("上传失败：",e),t(!1)),complete:()=>{e.index.hideLoading()}})}))),getFilePresignedUrl:e=>r.request({url:"/infra/file/presigned-url",method:"GET",params:{path:e}}),createFile:e=>r.request({url:"/infra/file/create",method:"POST",data:e})};exports.FileApi=t;
