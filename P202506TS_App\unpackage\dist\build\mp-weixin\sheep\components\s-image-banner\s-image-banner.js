"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js");if(!Array){e.resolveComponent("su-swiper")()}Math;const a={__name:"s-image-banner",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(a){const r=a,o=e.computed((()=>r.data.items.map((e=>{const a="img"===e.type?e.imgUrl:e.videoUrl;return{...e,type:"img"===e.type?"image":"video",src:t.sheep.$url.cdn(a),poster:t.sheep.$url.cdn(e.imgUrl)}}))));return(t,r)=>({a:e.p({list:o.value,dotStyle:"dot"===a.data.indicator?"long":"tag",imageMode:"scaleToFill",dotCur:"bg-mask-40",seizeHeight:300,autoplay:a.data.autoplay,interval:1e3*a.data.interval,mode:a.data.type})})}};wx.createComponent(a);
