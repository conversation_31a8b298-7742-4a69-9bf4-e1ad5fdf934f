"use strict";const e=require("../../../common/vendor.js"),t=require("../../helper/index.js"),a=require("../../index.js"),r={name:"su-tabbar",props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"},value:{type:[String,Number,null],default:""},safeAreaInsetBottom:{type:Boolean,default:!0},border:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:10},activeColor:{type:String,default:"#1989fa"},inactiveColor:{type:String,default:"#7d7e80"},fixed:{type:Boolean,default:!0},placeholder:{type:Boolean,default:!0},midTabBar:{type:<PERSON>olean,default:!1}},data:()=>({placeholderHeight:0,safeBottomHeight:a.sheep.$platform.device.safeAreaInsets.bottom}),computed:{tabbarStyle(){const e={zIndex:this.zIndex};return t.deepMerge(e,t.addStyle(this.customStyle))},updateChild(){return[this.value,this.activeColor,this.inactiveColor]},updatePlaceholder(){return[this.fixed,this.placeholder]}},watch:{updateChild(){this.updateChildren()},updatePlaceholder(){this.setPlaceholderHeight()}},created(){this.children=[]},mounted(){this.setPlaceholderHeight()},methods:{updateChildren(){this.children.length&&this.children.map((e=>e.updateFromParent()))},getRect(t,a){return new Promise((r=>{e.index.createSelectorQuery().in(this)[a?"selectAll":"select"](t).boundingClientRect((e=>{a&&Array.isArray(e)&&e.length&&r(e),!a&&e&&r(e)})).exec()}))},async setPlaceholderHeight(){this.fixed&&this.placeholder&&(await t.sleep(20),this.getRect(".u-tabbar__content").then((({height:e=50})=>{this.placeholderHeight=e})))}}};const l=e._export_sfc(r,[["render",function(t,a,r,l,i,d){return e.e({a:r.safeAreaInsetBottom},r.safeAreaInsetBottom?{b:e.s({height:i.safeBottomHeight+"px"})}:{},{c:e.o((()=>{})),d:e.n(r.border&&"u-border-top"),e:e.n(r.fixed&&"u-tabbar--fixed"),f:e.n({"mid-tabbar":r.midTabBar}),g:e.s(d.tabbarStyle),h:r.placeholder},r.placeholder?{i:i.placeholderHeight+"px"}:{})}],["__scopeId","data-v-cc8282eb"]]);wx.createComponent(l);
