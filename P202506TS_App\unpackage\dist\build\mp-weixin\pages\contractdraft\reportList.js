"use strict";const t=require("../../common/vendor.js"),o=require("../../sheep/index.js"),e=require("../../sheep/util/index.js"),a=require("../../sheep/api/member/contractDraftReport.js");if(!Array){(t.resolveComponent("uni-list-item")+t.resolveComponent("uni-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-empty")+t.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const n={__name:"reportList",setup(n){const i=t.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:6},contractId:"",draftId:"",keyword:"",loadStatus:""});function s(t){i.keyword=t,e.resetPagination(i.pagination),r()}async function r(){i.loadStatus="loading";const{code:o,data:e}=await a.ContractDraftReportApi.getContractDraftReportPage({pageNo:i.pagination.pageNo,pageSize:i.pagination.pageSize,contractId:i.contractId,draftId:i.draftId,keyword:i.keyword});0===o&&(i.pagination.list=t.lodash.concat(i.pagination.list,e.list),i.pagination.total=e.total,i.loadStatus=i.pagination.list.length<i.pagination.total?"more":"noMore")}function p(){"noMore"!==i.loadStatus&&(i.pagination.pageNo++,r())}return t.onLoad((t=>{i.contractId=t.contractId,i.draftId=t.draftId,i.keyword=t.keyword,r()})),t.onReachBottom((()=>{p()})),t.onPullDownRefresh((()=>{r(),setTimeout((function(){t.index.stopPullDownRefresh()}),800)})),(n,r)=>t.e({a:i.pagination.total>0},i.pagination.total>0?{b:t.f(i.pagination.list,((n,i,r)=>({a:t.t(n.draftName||"未命名稿件"),b:t.t(t.unref(e.formatDate)(n.createTime,"YYYY.MM.DD")),c:t.o((t=>(async t=>{o.sheep.$router.go("/pages/contractdraft/reportDetail",{url:t})})(n.files)),i),d:t.o((e=>(async e=>{t.index.showModal({title:"提示",content:"确认删除此稿件诊断报告吗？",success:async function(t){if(!t.confirm)return;const{code:n}=await a.ContractDraftReportApi.deleteContractDraftReport(e);0===n&&(o.sheep.$helper.toast("删除成功"),await s())}})})(n.id)),i),e:i,f:"907371c6-2-"+r+",907371c6-1"})))}:{},{c:i.pagination.total>0},i.pagination.total>0?{d:t.o(p),e:t.p({status:i.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{f:0===i.pagination.total},0===i.pagination.total?{g:t.p({icon:t.unref(o.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无稿件诊断报告"})}:{},{h:t.p({title:"稿件诊断报告"})})}},i=t._export_sfc(n,[["__scopeId","data-v-907371c6"]]);wx.createPage(i);
