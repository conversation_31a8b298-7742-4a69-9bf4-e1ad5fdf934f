"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js");require("../../sheep/store/index.js"),require("../../sheep/helper/index.js"),require("../../sheep/request/index.js");const s=require("../../sheep/api/member/reportAisino.js");if(!Array){(e.resolveComponent("uni-easyinput")+e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-forms")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"setting",setup(t){const i=e.reactive({model:{},rules:{}}),n=e.computed((()=>e.index.getStorageSync("enterprise")));async function l(){1!==i.model.cwfxStatus?(await s.ReportAisinoApi.updateStatus(i.model),r()):o.sheep.$helper.toast("已开通")}const r=async()=>{const{data:o}=await s.ReportAisinoApi.getStatus(n.value.id);i.model=e.clone(o)};return e.onBeforeMount((()=>{r()})),(o,s)=>({a:e.o((e=>i.model.taxMobile=e)),b:e.p({placeholder:"电局手机号",inputBorder:!1,disabled:1===i.model.cwfxStatus,styles:{disableColor:"#fff"},placeholderStyle:"color:#BBBBBB;font-size:28rpx;line-height:normal",clearable:!1,modelValue:i.model.taxMobile}),c:e.p({name:"taxMobile",label:"电局手机号"}),d:e.o((e=>i.model.aisinoDqbm=e)),e:e.p({placeholder:"地区代码",inputBorder:!1,disabled:1===i.model.cwfxStatus,styles:{disableColor:"#fff"},placeholderStyle:"color:#BBBBBB;font-size:28rpx;line-height:normal",clearable:!1,modelValue:i.model.aisinoDqbm}),f:e.p({name:"aisinoDqbm",label:"地区代码"}),g:e.t(["未开通","已开通"][i.model.aisinoStatus||0]),h:e.p({name:"aisinoStatus",label:"开通状态"}),i:e.p({model:i.model,rules:i.rules,labelPosition:"left",labelWidth:"160",border:!0}),j:e.o(l),k:e.p({bottom:!0,placeholder:!0,bg:"none"}),l:e.p({title:"税务风险检测设置"})})}},i=e._export_sfc(t,[["__scopeId","data-v-e2b1c48b"]]);wx.createPage(i);
