"use strict";const e=require("../../common/vendor.js"),r=require("../../sheep/index.js"),o=require("../../sheep/hooks/useGoods.js"),t=require("../../sheep/api/trade/order.js"),n=require("../../sheep/api/trade/delivery.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-goods-item/s-goods-item.js")+d+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const d=()=>"./pickUpVerify.js",s={__name:"detail",setup(d){e.useCssVars((r=>({f41d66ec:e.unref(a)})));const s=2*r.sheep.$platform.device.statusBarHeight,a=r.sheep.$url.css("/static/img/shop/order/order_bg.png"),i=e.reactive({orderInfo:{},merchantTradeNo:"",comeinType:""}),c=e.ref({}),u=()=>{r.sheep.$helper.copyText(i.orderInfo.no)};async function f(o,n=!1){"WechatMiniProgram"!==r.sheep.$platform.name||e.isEmpty(i.orderInfo.wechat_extra_data)||n?e.index.showModal({title:"提示",content:"确认收货吗？",success:async function(e){if(!e.confirm)return;const{code:r}=await t.OrderApi.receiveOrder(o);0===r&&await l(o)}}):function(o){if(!e.wx$1.openBusinessView)return void r.sheep.$helper.toast("请升级微信版本");e.wx$1.openBusinessView({businessType:"weappOrderConfirm",extraData:{merchant_trade_no:i.orderInfo.wechat_extra_data.merchant_trade_no,transaction_id:i.orderInfo.wechat_extra_data.transaction_id},success(e){console.log("success:",e),"openBusinessView:ok"===e.errMsg&&"success"===e.extraData.status&&f(o,!0)},fail(e){console.log("error:",e)},complete(e){console.log("result:",e)}})}(o)}const p=e.ref();async function l(e){let d;if(d="wechat"===i.comeinType?await t.OrderApi.getOrderDetail(e,{merchant_trade_no:i.merchantTradeNo}):await t.OrderApi.getOrderDetail(e),0===d.code){if(i.orderInfo=d.data,o.handleOrderButtons(i.orderInfo),d.data.pickUpStoreId){const{data:e}=await n.DeliveryApi.getDeliveryPickUpStore(d.data.pickUpStoreId);c.value=e||{}}2===i.orderInfo.deliveryType&&i.orderInfo.payStatus&&p.value&&p.value.markCode(d.data.pickUpVerifyCode)}else r.sheep.$router.back()}return e.onShow((async()=>{await l(i.orderInfo.id)})),e.onLoad((async e=>{let r=0;e.id&&(r=e.id),i.comeinType=e.comein_type,"wechat"===i.comeinType&&(i.merchantTradeNo=e.merchant_trade_no),i.orderInfo.id=r})),(n,d)=>{var a,I,m,h,g,y,_,v,b,x,w,S,P,$;return e.e({a:"unpaid"==i.orderInfo.status_code||10===i.orderInfo.status||"nocomment"==i.orderInfo.status_code},"unpaid"==i.orderInfo.status_code||10===i.orderInfo.status||"nocomment"==i.orderInfo.status_code?{b:e.unref(r.sheep).$url.static("/static/img/shop/order/order_loading.png")}:{},{c:"completed"==i.orderInfo.status_code||"refund_agree"==i.orderInfo.status_code},"completed"==i.orderInfo.status_code||"refund_agree"==i.orderInfo.status_code?{d:e.unref(r.sheep).$url.static("/static/img/shop/order/order_success.png")}:{},{e:"cancel"==i.orderInfo.status_code||"closed"==i.orderInfo.status_code},"cancel"==i.orderInfo.status_code||"closed"==i.orderInfo.status_code?{f:e.unref(r.sheep).$url.static("/static/img/shop/order/order_close.png")}:{},{g:"noget"==i.orderInfo.status_code},"noget"==i.orderInfo.status_code?{h:e.unref(r.sheep).$url.static("/static/img/shop/order/order_express.png")}:{},{i:e.t(e.unref(o.formatOrderStatus)(i.orderInfo)),j:e.t(e.unref(o.formatOrderStatusDescription)(i.orderInfo)),k:e.s({marginTop:"-"+Number(s+88)+"rpx",paddingTop:Number(s+88)+"rpx"}),l:i.orderInfo.receiverAreaId>0},i.orderInfo.receiverAreaId>0?{m:e.t(i.orderInfo.receiverName),n:e.t(i.orderInfo.receiverMobile),o:e.t(i.orderInfo.receiverAreaName),p:e.t(i.orderInfo.receiverDetailAddress)}:{},{q:e.f(i.orderInfo.items,((o,t,n)=>e.e({a:[10,20,30].includes(i.orderInfo.status)&&0===o.afterSaleStatus},[10,20,30].includes(i.orderInfo.status)&&0===o.afterSaleStatus?{b:e.o((t=>e.unref(r.sheep).$router.go("/pages/order/aftersale/apply",{orderId:i.orderInfo.id,itemId:o.id})),o.goods_id)}:{},{c:10===o.afterSaleStatus},10===o.afterSaleStatus?{d:e.o((t=>e.unref(r.sheep).$router.go("/pages/order/aftersale/detail",{id:o.afterSaleId})),o.goods_id)}:{},{e:20===o.afterSaleStatus},20===o.afterSaleStatus?{f:e.o((t=>e.unref(r.sheep).$router.go("/pages/order/aftersale/detail",{id:o.afterSaleId})),o.goods_id)}:{},{g:o.status_text},o.status_text?{h:e.t(o.status_text)}:{},{i:e.o((e=>{return t=o.spuId,void r.sheep.$router.go("/pages/goods/index",{id:t});var t}),o.goods_id),j:"008f07e2-1-"+n+",008f07e2-0",k:e.p({img:o.picUrl,title:o.spuName,skuText:o.properties.map((e=>e.valueName)).join(" "),price:o.price,num:o.count}),l:o.goods_id}))),r:e.s({marginTop:i.orderInfo.receiverAreaId>0?"0":"-40rpx"}),s:e.sr(p,"008f07e2-2,008f07e2-0",{k:"pickUpVerifyRef"}),t:e.p({"order-info":i.orderInfo,systemStore:c.value}),v:e.t(i.orderInfo.no),w:e.o(u),x:e.t(e.unref(r.sheep).$helper.timeFormat(i.orderInfo.createTime,"yyyy-mm-dd hh:MM:ss")),y:i.orderInfo.payTime},i.orderInfo.payTime?{z:e.t(e.unref(r.sheep).$helper.timeFormat(i.orderInfo.payTime,"yyyy-mm-dd hh:MM:ss"))}:{},{A:e.t(i.orderInfo.payChannelName||"-"),B:e.t(e.unref(o.fen2yuan)(i.orderInfo.totalPrice)),C:e.t(e.unref(o.fen2yuan)(i.orderInfo.deliveryPrice)),D:i.orderInfo.couponPrice>0},i.orderInfo.couponPrice>0?{E:e.t(e.unref(o.fen2yuan)(i.orderInfo.couponPrice))}:{},{F:i.orderInfo.pointPrice>0},i.orderInfo.pointPrice>0?{G:e.t(e.unref(o.fen2yuan)(i.orderInfo.pointPrice))}:{},{H:i.orderInfo.discountPrice>0},i.orderInfo.discountPrice>0?{I:e.t(e.unref(o.fen2yuan)(i.orderInfo.discountPrice))}:{},{J:i.orderInfo.vipPrice>0},i.orderInfo.vipPrice>0?{K:e.t(e.unref(o.fen2yuan)(i.orderInfo.vipPrice))}:{},{L:e.t(i.orderInfo.payStatus?"已付款":"需付款"),M:e.t(e.unref(o.fen2yuan)(i.orderInfo.payPrice)),N:i.orderInfo.refundPrice>0},i.orderInfo.refundPrice>0?{O:e.t(e.unref(o.fen2yuan)(i.orderInfo.refundPrice))}:{},{P:null==(a=i.orderInfo.buttons)?void 0:a.length},(null==(I=i.orderInfo.buttons)?void 0:I.length)?e.e({Q:null==(m=i.orderInfo.buttons)?void 0:m.includes("cancel")},(null==(h=i.orderInfo.buttons)?void 0:h.includes("cancel"))?{R:e.o((r=>async function(r){e.index.showModal({title:"提示",content:"确定要取消订单吗?",success:async function(e){if(!e.confirm)return;const{code:o}=await t.OrderApi.cancelOrder(r);0===o&&await l(r)}})}(i.orderInfo.id)))}:{},{S:null==(g=i.orderInfo.buttons)?void 0:g.includes("pay")},(null==(y=i.orderInfo.buttons)?void 0:y.includes("pay"))?{T:e.o((e=>{return o=i.orderInfo.payOrderId,void r.sheep.$router.go("/pages/pay/index",{id:o});var o}))}:{},{U:null==(_=i.orderInfo.buttons)?void 0:_.includes("combination")},(null==(v=i.orderInfo.buttons)?void 0:v.includes("combination"))?{V:e.o((o=>e.unref(r.sheep).$router.go("/pages/activity/groupon/detail",{id:i.orderInfo.combinationRecordId})))}:{},{W:null==(b=i.orderInfo.buttons)?void 0:b.includes("express")},(null==(x=i.orderInfo.buttons)?void 0:x.includes("express"))?{X:e.o((e=>async function(e){r.sheep.$router.go("/pages/order/express/log",{id:e})}(i.orderInfo.id)))}:{},{Y:null==(w=i.orderInfo.buttons)?void 0:w.includes("confirm")},(null==(S=i.orderInfo.buttons)?void 0:S.includes("confirm"))?{Z:e.o((e=>f(i.orderInfo.id)))}:{},{aa:null==(P=i.orderInfo.buttons)?void 0:P.includes("comment")},(null==($=i.orderInfo.buttons)?void 0:$.includes("comment"))?{ab:e.o((e=>{return o=i.orderInfo.id,void r.sheep.$router.go("/pages/goods/comment/add",{id:o});var o}))}:{},{ac:e.p({bottom:!0,placeholder:!0,bg:"bg-white"})}):{},{ad:e.s(n.__cssVars()),ae:e.p({title:"订单详情",navbar:"inner"})})}}},a=e._export_sfc(s,[["__scopeId","data-v-008f07e2"]]);wx.createPage(a);
