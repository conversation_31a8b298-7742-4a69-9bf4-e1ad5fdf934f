"use strict";const e=require("../../common/vendor.js"),r=require("../../sheep/index.js"),o=require("../../sheep/api/member/reportCwfx.js");if(!Array){(e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-data-select")+e.resolveComponent("uni-easyinput")+e.resolveComponent("s-uploader")+e.resolveComponent("uni-forms")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-data-select/components/uni-data-select/uni-data-select.js")+(()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../sheep/components/s-uploader/s-uploader.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"create",setup(t){e.useCssVars((r=>({"326e6f72":e.unref(l)}))),r.sheep.$platform.device.statusBarHeight;const l=r.sheep.$url.css("/assets/mp/index/bg_header.png");e.computed((()=>{var e;return null==(e=r.sheep.$store("app").template)?void 0:e.home}));let a=e.reactive({model:{companyName:void 0,periodTime:void 0,evaYear:2023,cit:void 0,vat:void 0,inputVat:void 0,outputVat:void 0,preCit:void 0,preVat:void 0,preInputVat:void 0,preOutputVat:void 0,file:void 0},rules:{periodTime:{rules:[{required:!0,errorMessage:"请选择评测周期"}]},cit:{rules:[{required:!0,errorMessage:"请输入本期企业所得税应纳税额合计(万元)"}]},vat:{rules:[{required:!0,errorMessage:"请输入本期增值税应纳税额合计(万元)"}]},inputVat:{rules:[{required:!0,errorMessage:"请输入本期増值税进项税额(万元)"}]},outputVat:{rules:[{required:!0,errorMessage:"请输入本期増值税销项税额(万元)"}]},preCit:{rules:[{required:!0,errorMessage:"请输入上期企业所得税应纳税额合计(万元)"}]},preVat:{rules:[{required:!0,errorMessage:"请输入上期增值税应纳税额合计(万元)"}]},preInputVat:{rules:[{required:!0,errorMessage:"请输入上期増值税进项税额(万元)"}]},preOutputVat:{rules:[{required:!0,errorMessage:"请输入上期増值税销项税额(万元)"}]}}});const u=[{value:"年度",text:" 年度"},{value:"第一季度",text:" 第一季度"},{value:"第二季度",text:" 第二季度"},{value:"第三季度",text:" 第三季度"},{value:"第四季度",text:" 第四季度"}],{safeArea:p}=r.sheep.$platform.device;e.computed((()=>p.height-44-50));const n=e=>{console.log("onSelect-t",e),a.model.file=e.tempFilePaths[0],console.log("state.model.file",a.model.file)},s=e.ref(null);async function i(){if(!(await e.unref(s).validate().catch((e=>{console.log("error: ",e)}))))return;if(!a.model.periodTime)return void r.sheep.$helper.toast("请选择评测周期");if(!a.model.file)return void r.sheep.$helper.toast("请选择财务报表");let t={...a.model};const{code:l}=await o.ReportCwfxApi.postOrder(t);0===l&&(e.index.showToast({title:"上传成功"}),r.sheep.$router.redirect("/pages/finance/list"))}return e.onLoad((r=>{const o=e.index.getStorageSync("enterprise");a.model.companyName=o.name,a.model.enterpriseId=o.id})),(r,o)=>({a:e.t(e.unref(a).model.companyName),b:e.p({name:"companyName",label:"企业名称"}),c:e.o((r=>e.unref(a).model.periodTime=r)),d:e.p({localdata:u,placeholder:"请选择评测周期",inputBorder:!1,modelValue:e.unref(a).model.periodTime}),e:e.p({name:"periodTime",label:"评测周期"}),f:e.t(e.unref(a).model.evaYear),g:e.p({name:"evaYear",label:"评测年份"}),h:e.o((r=>e.unref(a).model.cit=r)),i:e.p({placeholder:"请输入本期企业所得税应纳税额合计(万元)",inputBorder:!1,modelValue:e.unref(a).model.cit}),j:e.p({name:"cit",label:"本期企业所得税应纳税额合计(万元)"}),k:e.o((r=>e.unref(a).model.vat=r)),l:e.p({placeholder:"请输入本期增值税应纳税额合计(万元)",inputBorder:!1,modelValue:e.unref(a).model.vat}),m:e.p({name:"vat",label:"本期增值税应纳税额合计(万元)"}),n:e.o((r=>e.unref(a).model.inputVat=r)),o:e.p({placeholder:"请输入本期増值税进项税额(万元)",inputBorder:!1,modelValue:e.unref(a).model.inputVat}),p:e.p({name:"inputVat",label:"本期増值税进项税额(万元)"}),q:e.o((r=>e.unref(a).model.outputVat=r)),r:e.p({placeholder:"请输入本期増值税销项税额(万元)",inputBorder:!1,modelValue:e.unref(a).model.outputVat}),s:e.p({name:"outputVat",label:"本期増值税销项税额(万元)"}),t:e.o((r=>e.unref(a).model.preCit=r)),v:e.p({placeholder:"请输入上期企业所得税应纳税额合计(万元)",inputBorder:!1,modelValue:e.unref(a).model.preCit}),w:e.p({name:"preCit",label:"上期企业所得税应纳税额合计(万元)"}),x:e.o((r=>e.unref(a).model.preVat=r)),y:e.p({placeholder:"请输入上期增值税应纳税额合计(万元)",inputBorder:!1,modelValue:e.unref(a).model.preVat}),z:e.p({name:"preVat",label:"上期增值税应纳税额合计(万元)"}),A:e.o((r=>e.unref(a).model.preInputVat=r)),B:e.p({placeholder:"请输入上期増值税进项税额(万元)",inputBorder:!1,modelValue:e.unref(a).model.preInputVat}),C:e.p({name:"preInputVat",label:"上期増值税进项税额(万元)"}),D:e.o((r=>e.unref(a).model.preOutputVat=r)),E:e.p({placeholder:"请输入上期増值税销项税额(万元)",inputBorder:!1,modelValue:e.unref(a).model.preOutputVat}),F:e.p({name:"preOutputVat",label:"上期増值税销项税额(万元)"}),G:e.o(n),H:e.o((r=>e.unref(a).model.file=r)),I:e.p({fileMediatype:"all",fileExtname:"xlsx",limit:"1",mode:"list",autoUpload:!1,url:e.unref(a).model.file}),J:e.p({name:"file",label:"财务报表"}),K:e.o(i),L:e.sr(s,"6c97a016-1,6c97a016-0",{k:"formRef"}),M:e.p({model:e.unref(a).model,rules:e.unref(a).rules,labelPosition:"top",labelWidth:"500",border:!0}),N:e.s(r.__cssVars()),O:e.p({title:"发起财务风险检测"})})}},l=e._export_sfc(t,[["__scopeId","data-v-6c97a016"]]);wx.createPage(l);
