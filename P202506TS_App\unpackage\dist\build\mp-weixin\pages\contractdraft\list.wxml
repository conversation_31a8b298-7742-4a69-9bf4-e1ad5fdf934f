<s-layout wx:if="{{k}}" class="data-v-0cedbb53" u-s="{{['d']}}" u-i="0cedbb53-0" bind:__l="__l" u-p="{{k}}"><su-sticky wx:if="{{c}}" class="data-v-0cedbb53" u-s="{{['d']}}" u-i="0cedbb53-1,0cedbb53-0" bind:__l="__l" u-p="{{c}}"><view class="ss-flex data-v-0cedbb53"><view class="ss-flex-1 data-v-0cedbb53"><su-tabs wx:if="{{b}}" class="data-v-0cedbb53" bindchange="{{a}}" u-i="0cedbb53-2,0cedbb53-1" bind:__l="__l" u-p="{{b}}"/></view></view></su-sticky><view wx:if="{{d}}" class="contract-content ss-m-x-20 ss-m-t-20 data-v-0cedbb53"><uni-list class="data-v-0cedbb53" u-s="{{['d']}}" u-i="0cedbb53-3,0cedbb53-0" bind:__l="__l"><uni-list-item wx:for="{{e}}" wx:for-item="item" wx:key="n" u-s="{{['body']}}" class="ss-radius-20 ss-m-b-20 data-v-0cedbb53" style="background-color:#fff;overflow:hidden" u-i="{{item.o}}" bind:__l="__l"><view slot="body"><view class="ss-flex-col data-v-0cedbb53"><view class="contract-header ss-flex data-v-0cedbb53"><view class="{{['contract-no', 'data-v-0cedbb53', item.b]}}">{{item.a}}</view><view class="{{['contract-status', 'data-v-0cedbb53', item.d]}}">{{item.c}}</view></view><view class="ss-w-100 data-v-0cedbb53" style="height:58rpx"></view><view class="contract-text title data-v-0cedbb53">{{item.e}}</view><view class="contract-text data-v-0cedbb53"> 创建时间：{{item.f}}</view></view><view class="ss-m-t-20 ss-flex ss-flex-wrap ss-col-center data-v-0cedbb53" style="gap:20rpx"><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.g}}"> 详情 </button><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.h}}"> 编辑 </button><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.i}}"> 定稿 </button><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.j}}"> 诊断 </button><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.k}}"> 诊断报告 </button><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.l}}"> 废弃 </button><button class="tool-btn ss-reset-button data-v-0cedbb53" catchtap="{{item.m}}"> 删除 </button></view></view></uni-list-item></uni-list></view><uni-load-more wx:if="{{f}}" class="data-v-0cedbb53" bindtap="{{g}}" u-i="0cedbb53-5,0cedbb53-0" bind:__l="__l" u-p="{{h}}"/><s-empty wx:if="{{i}}" class="data-v-0cedbb53" u-i="0cedbb53-6,0cedbb53-0" bind:__l="__l" u-p="{{j}}"/></s-layout>