"use strict";const t=require("../../common/vendor.js"),o=require("../../common/assets.js"),i=require("../../sheep/index.js"),e=require("../../sheep/api/promotion/rewardActivity.js"),a=require("../../sheep/api/product/spu.js"),s=require("../../sheep/hooks/useGoods.js"),n=require("../../sheep/api/trade/order.js");if(!Array){(t.resolveComponent("su-sticky")+t.resolveComponent("s-goods-column")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../sheep/components/s-goods-column/s-goods-column.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const d={__name:"index",setup(d){const p=t.reactive({activityId:0,activityInfo:{},pagination:{list:[],total:1,pageNo:1,pageSize:8},loadStatus:"",leftGoodsList:[],rightGoodsList:[]});let c=0,r=0,u=0;function l(t=0,o="left"){p.pagination.list[c]&&("left"===o?r+=t:u+=t,r<=u?p.leftGoodsList.push(p.pagination.list[c]):p.rightGoodsList.push(p.pagination.list[c]),c++)}async function g(){const o={};2===p.activityInfo.productScope?o.ids=p.activityInfo.productSpuIds.join(","):3===p.activityInfo.productScope&&(o.categoryIds=p.activityInfo.productSpuIds.join(",")),p.loadStatus="loading";const{code:i,data:e}=await a.SpuApi.getSpuPage({pageNo:p.pagination.pageNo,pageSize:p.pagination.pageSize,...o});0===i&&(await n.OrderApi.getSettlementProduct(e.list.map((t=>t.id)).join(",")).then((t=>{0===t.code&&s.appendSettlementProduct(e.list,t.data)})),p.pagination.list=t.lodash.concat(p.pagination.list,e.list),p.pagination.total=e.total,p.loadStatus=p.pagination.list.length<p.pagination.total?"more":"noMore",l())}function m(){"noMore"!==p.loadStatus&&(p.pagination.pageNo++,g())}return t.onReachBottom((()=>{m()})),t.onLoad((async t=>{p.activityId=t.activityId,await async function(t){const{code:o,data:i}=await e.RewardActivityApi.getRewardActivity(t);0===o&&(p.activityInfo=i)}(p.activityId),await g(p.activityId)})),(e,a)=>t.e({a:t.f(p.activityInfo.rules,((o,i,e)=>({a:t.t(o.description),b:o}))),b:o._imports_0$1,c:o._imports_1$1,d:t.p({bgColor:"#fff"}),e:t.f(p.leftGoodsList,((o,e,a)=>({a:t.o((e=>t.unref(i.sheep).$router.go("/pages/goods/index",{id:o.id})),o.id),b:t.o((t=>l(t,"left")),o.id),c:"19929d6c-2-"+a+",19929d6c-0",d:t.p({size:"md",data:o}),e:o.id}))),f:t.f(p.rightGoodsList,((o,e,a)=>({a:t.o((e=>t.unref(i.sheep).$router.go("/pages/goods/index",{id:o.id})),o.id),b:t.o((t=>l(t,"right")),o.id),c:"19929d6c-3-"+a+",19929d6c-0",d:t.p({size:"md",data:o}),e:o.id}))),g:p.pagination.total>0},p.pagination.total>0?{h:t.o(m),i:t.p({status:p.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{j:t.p({title:p.activityInfo.title})})}},p=t._export_sfc(d,[["__scopeId","data-v-19929d6c"]]);wx.createPage(p);
