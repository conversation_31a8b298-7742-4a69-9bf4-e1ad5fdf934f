"use strict";const t=require("../../../common/vendor.js"),e=require("../../helper/index.js"),i=require("../../index.js"),s={name:"su-sticky",props:{offsetTop:{type:[String,Number],default:0},customNavHeight:{type:[String,Number],default:i.sheep.$platform.navbar},stickyToTop:{type:Boolean,default:!1},bgColor:{type:String,default:"transparent"},zIndex:{type:[String,Number],default:""},index:{type:[String,Number],default:""},customStyle:{type:[Object,String],default:()=>({})}},data:()=>({cssSticky:!1,stickyTop:0,elId:e.guid(),left:0,width:"auto",height:"auto",fixed:!1}),computed:{style(){const t={};return this.stickyToTop?t.position="static":this.cssSticky?(t.position="sticky",t.zIndex=this.uZindex,t.top=e.addUnit(this.stickyTop)):t.height=this.fixed?this.height+"px":"auto",t.backgroundColor=this.bgColor,e.deepMerge(e.addStyle(this.customStyle),t)},stickyContent(){const t={};return this.cssSticky||(t.position=this.fixed?"fixed":"static",t.top=this.stickyTop+"px",t.left=this.left+"px",t.width="auto"==this.width?"auto":this.width+"px",t.zIndex=this.uZindex),t},uZindex(){return this.zIndex?this.zIndex:970}},mounted(){this.init()},methods:{init(){this.getStickyTop(),this.checkSupportCssSticky(),this.cssSticky||!this.stickyToTop&&this.initObserveContent()},$uGetRect(e,i){return new Promise((s=>{t.index.createSelectorQuery().in(this)[i?"selectAll":"select"](e).boundingClientRect((t=>{i&&Array.isArray(t)&&t.length&&s(t),!i&&t&&s(t)})).exec()}))},initObserveContent(){this.$uGetRect("#"+this.elId).then((t=>{this.height=t.height,this.left=t.left,this.width=t.width,this.$nextTick((()=>{this.observeContent()}))}))},observeContent(){this.disconnectObserver("contentObserver");const e=t.index.createIntersectionObserver({thresholds:[.95,.98,1]});e.relativeToViewport({top:-this.stickyTop}),e.observe(`#${this.elId}`,(t=>{this.setFixed(t.boundingClientRect.top)})),this.contentObserver=e},setFixed(t){const e=t<=this.stickyTop;this.fixed=e},disconnectObserver(t){const e=this[t];e&&e.disconnect()},getStickyTop(){this.stickyTop=e.getPx(this.offsetTop)+e.getPx(this.customNavHeight)},async checkSupportCssSticky(){"android"===e.os()&&Number(e.sys().system)>8&&(this.cssSticky=!0),this.cssSticky=await this.checkComputedStyle(),"ios"===e.os()&&(this.cssSticky=!0)},checkComputedStyle(){return new Promise((e=>{t.index.createSelectorQuery().in(this).select(".u-sticky").fields({computedStyle:["position"]}).exec((t=>{e("sticky"===t[0].position)}))}))},checkCssStickyForH5(){}},beforeDestroy(){this.disconnectObserver("contentObserver")}};const o=t._export_sfc(s,[["render",function(e,i,s,o,n,c){return{a:t.s(c.stickyContent),b:n.elId,c:t.s(c.style)}}],["__scopeId","data-v-8a7972b2"]]);wx.createComponent(o);
