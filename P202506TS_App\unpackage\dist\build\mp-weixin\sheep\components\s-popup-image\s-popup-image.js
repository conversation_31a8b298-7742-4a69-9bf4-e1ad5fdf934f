"use strict";const e=require("../../../common/vendor.js"),o=require("../../index.js"),s=require("../../hooks/useModal.js");if(!Array){e.resolveComponent("su-popup")()}Math;const r={__name:"s-popup-image",props:{data:{type:Object,default(){}}},setup(r){const t=r,n=JSON.parse(e.index.getStorageSync("modal-store")||"{}");console.log(n);const a=n.advHistory||[],u=e.ref(0),c=e.computed((()=>{const e=t.data.list||[],o=[];return e.length>0&&e.forEach((e=>{"once"===e.showType&&a.includes(e.imgUrl)?e.isShow=!1:(e.isShow=!0,o.push(e)),s.saveAdvHistory(e)})),o}));return(s,r)=>({a:e.f(c.value,((s,r,t)=>e.e({a:r===u.value},r===u.value?{b:e.unref(o.sheep).$url.cdn(s.imgUrl),c:e.o((e=>{return r=s.url,void o.sheep.$router.go(r);var r}),r),d:e.o((e=>function(e){u.value=e+1,c.value[e].isShow=!1}(r)),r),e:"e1c1d1c2-0-"+t,f:e.p({show:s.isShow,type:"center",backgroundColor:"none",round:"0",showClose:!0,isMaskClick:!1})}:{},{g:r})))})}},t=e._export_sfc(r,[["__scopeId","data-v-e1c1d1c2"]]);wx.createComponent(t);
