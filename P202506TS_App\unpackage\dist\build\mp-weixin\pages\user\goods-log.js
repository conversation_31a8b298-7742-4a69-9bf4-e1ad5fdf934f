"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),o=require("../../sheep/api/product/history.js"),i=require("../../sheep/helper/utils.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("su-fixed")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-empty")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const s={__name:"goods-log",setup(s){e.useCssVars((t=>({d09a0a54:e.unref(n)})));const n=t.sheep.$platform.navbar,a={list:[],pageNo:1,total:1,pageSize:10},d=e.reactive({pagination:i.cloneDeep(a),loadStatus:"",editMode:!1,selectedSpuIdList:[],selectAll:!1});async function l(){d.loadStatus="loading";const{code:t,data:i}=await o.SpuHistoryApi.getBrowseHistoryPage({pageNo:d.pagination.pageNo,pageSize:d.pagination.pageSize});0===t&&(d.pagination.list=e.lodash.concat(d.pagination.list,i.list),d.pagination.total=i.total,d.loadStatus=d.pagination.list.length<d.pagination.total?"more":"noMore")}const p=e=>{d.selectedSpuIdList.includes(e)?d.selectedSpuIdList.splice(d.selectedSpuIdList.indexOf(e),1):d.selectedSpuIdList.push(e),d.selectAll=d.selectedSpuIdList.length===d.pagination.list.length},c=()=>{d.selectAll=!d.selectAll,d.selectAll?d.pagination.list.forEach((e=>{d.selectedSpuIdList.includes(e.spuId)&&d.selectedSpuIdList.splice(d.selectedSpuIdList.indexOf(e.spuId),1),d.selectedSpuIdList.push(e.spuId)})):d.selectedSpuIdList=[]};async function u(){if(d.selectedSpuIdList.length<=0)return;const{code:e}=await o.SpuHistoryApi.deleteBrowseHistory(d.selectedSpuIdList);0===e&&g()}async function r(){const{code:e}=await o.SpuHistoryApi.cleanBrowseHistory();0===e&&g()}function g(){d.editMode=!1,d.selectedSpuIdList=[],d.selectAll=!1,d.pagination=a,l()}function m(){"noMore"!==d.loadStatus&&(d.pagination.pageNo+=1,l())}return e.onReachBottom((()=>{m()})),e.onLoad((()=>{l()})),(o,i)=>e.e({a:e.t(d.pagination.total),b:d.editMode&&d.pagination.total},d.editMode&&d.pagination.total?{c:e.o((e=>d.editMode=!1))}:{},{d:!d.editMode&&d.pagination.total},!d.editMode&&d.pagination.total?{e:e.o((e=>d.editMode=!0))}:{},{f:e.f(d.pagination.list,((o,i,s)=>e.e(d.editMode?{a:d.selectedSpuIdList.includes(o.spuId),b:e.o((e=>p(o.spuId)),o.id),c:e.o((e=>p(o.spuId)),o.id)}:{},{d:e.o((i=>e.unref(t.sheep).$router.go("/pages/goods/index",{id:o.spuId})),o.id),e:"36fb4e6d-1-"+s+",36fb4e6d-0",f:e.p({title:o.spuName,img:o.picUrl,price:o.price,skuText:o.introduction,priceColor:"#FF3000",titleWidth:400}),g:o.id}))),g:d.editMode,h:d.selectAll,i:e.o(c),j:e.o(c),k:e.n({"ui-BG-Main-Gradient":d.selectedSpuIdList.length>0,"ui-Shadow-Main":d.selectedSpuIdList.length>0}),l:e.o(u),m:e.o(r),n:d.editMode,o:e.p({bottom:!0,val:0,placeholder:!0}),p:d.pagination.total>0},d.pagination.total>0?{q:e.o(m),r:e.p({status:d.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{s:0===d.pagination.total},0===d.pagination.total?{t:e.p({text:"暂无浏览记录",icon:"/static/collect-empty.png"})}:{},{v:e.s(o.__cssVars()),w:e.p({bgStyle:{color:"#f2f2f2"},title:"我的足迹"})})}},n=e._export_sfc(s,[["__scopeId","data-v-36fb4e6d"]]);wx.createPage(n);
