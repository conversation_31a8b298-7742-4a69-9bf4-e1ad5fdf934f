"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),a=require("../../sheep/platform/share.js"),t=require("../../sheep/hooks/useModal.js"),i=require("../../sheep/api/product/spu.js"),r=require("../../sheep/api/trade/brokerage.js"),n=require("../../sheep/hooks/useGoods.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("s-empty")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const s={__name:"goods",setup(s){const p=e.reactive({pagination:{list:[],total:0,pageNo:1,pageSize:8},loadStatus:"",shareInfo:{}});async function c(){p.loadStatus="loading";let{code:o,data:a}=await i.SpuApi.getSpuPage({pageSize:p.pagination.pageSize,pageNo:p.pagination.pageNo});0===o?(await Promise.all(a.list.map((async e=>{try{const o=await r.BrokerageApi.getProductBrokeragePrice(e.id);e.brokerageMinPrice=o.data.brokerageMinPrice,e.brokerageMaxPrice=o.data.brokerageMaxPrice}catch(o){console.error(`获取商品【${e.name}】的佣金时出错：`,o)}}))),p.pagination.list=e.lodash.concat(p.pagination.list,a.list),p.pagination.total=a.total,p.loadStatus=p.pagination.list.length<p.pagination.total?"more":"noMore"):p.loadStatus="error"}function g(){"noMore"!==p.loadStatus&&(p.pagination.pageNo++,c())}return e.onLoad((()=>{c()})),e.onReachBottom((()=>{g()})),(i,r)=>e.e({a:e.f(p.pagination.list,((i,r,s)=>e.e({a:void 0===i.brokerageMinPrice},void 0===i.brokerageMinPrice?{}:i.brokerageMinPrice===i.brokerageMaxPrice?{c:e.t(e.unref(n.fen2yuan)(i.brokerageMinPrice))}:{d:e.t(e.unref(n.fen2yuan)(i.brokerageMinPrice)),e:e.t(e.unref(n.fen2yuan)(i.brokerageMaxPrice))},{b:i.brokerageMinPrice===i.brokerageMaxPrice,f:e.o((e=>{return r=i,p.shareInfo=a.$share.getShareInfo({title:r.name,image:o.sheep.$url.cdn(r.picUrl),desc:r.introduction,params:{page:"2",query:r.id}},{type:"goods",title:r.name,image:o.sheep.$url.cdn(r.picUrl),price:n.fen2yuan(r.price),original_price:n.fen2yuan(r.marketPrice)}),void t.showShareModal();var r}),i.id),g:e.o((a=>e.unref(o.sheep).$router.go("/pages/goods/index",{id:i.id})),i.id),h:"8a08a031-1-"+s+",8a08a031-0",i:e.p({size:"lg",img:i.picUrl,title:i.name,subTitle:i.introduction,price:i.price,originPrice:i.marketPrice,priceColor:"#333"}),j:i.id}))),b:0===p.pagination.total},0===p.pagination.total?{c:e.p({icon:"/static/goods-empty.png",text:"暂无推广商品"})}:{},{d:p.pagination.total>0},p.pagination.total>0?{e:e.o(g),f:e.p({status:p.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{g:e.p({title:"推广商品",onShareAppMessage:p.shareInfo})})}},p=e._export_sfc(s,[["__scopeId","data-v-8a08a031"]]);s.__runtimeHooks=2,wx.createPage(p);
