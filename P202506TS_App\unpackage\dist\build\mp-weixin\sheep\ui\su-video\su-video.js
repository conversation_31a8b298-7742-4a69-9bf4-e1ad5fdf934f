"use strict";const e=require("../../../common/vendor.js");require("../../index.js");const t={__name:"su-video",props:{moveX:{type:[Number],default:0},uid:{type:[Number,String],default:0},height:{type:Number,default:300},width:{type:Number,default:750},initialTime:{type:Number,default:1},src:{type:String,default:""},poster:{type:String,default:"https://img1.baidu.com/it/u=1601695551,235775011&fm=26&fmt=auto"},autoplay:{type:<PERSON>olean,default:!1}},emits:["videoTimeupdate"],setup(t,{expose:o,emit:i}){const r=e.getCurrentInstance(),s=e.reactive({enableProgressGesture:!1,showModal:!1}),a=t,u=i,d=e=>{u("videoTimeupdate",e)},n=e=>{console.log("视频错误信息:",e.target.errMsg)},l=()=>{console.log("视频开始")},p=()=>{console.log("视频暂停")},m=()=>{console.log("视频结束")};return o({pausePlay:()=>{e.index.createVideoContext(`sVideo${a.index}`,r).pause()}}),(o,i)=>({a:`sVideo${t.uid}`,b:e.s({height:t.height+"rpx"}),c:t.src,d:s.enableProgressGesture,e:t.initialTime,f:e.o(n),g:e.o(d),h:e.o(l),i:e.o(p),j:e.o(m),k:t.poster,l:t.autoplay})}},o=e._export_sfc(t,[["__scopeId","data-v-f5e784aa"]]);wx.createComponent(o);
