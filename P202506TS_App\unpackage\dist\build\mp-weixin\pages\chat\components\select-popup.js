"use strict";const o=require("../../../common/vendor.js"),e=require("../../../sheep/api/trade/order.js"),a=require("../../../sheep/api/product/history.js");if(!Array){(o.resolveComponent("uni-load-more")+o.resolveComponent("su-popup"))()}Math||(t+s+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../sheep/ui/su-popup/su-popup.js"))();const t=()=>"./goods.js",s=()=>"./order.js",d={__name:"select-popup",props:{mode:{type:String,default:"goods"},show:{type:Boolean,default:!1}},emits:["select","close"],setup(t,{emit:s}){const d=s,n=t;o.watch((()=>n.mode),(()=>{r.pagination.data=[],n.mode&&p(r.pagination.page)}));const r=o.reactive({loadStatus:"",pagination:{data:[],current_page:1,total:1,last_page:1}});async function p(t,s=5){r.loadStatus="loading";const d="goods"==n.mode?await a.SpuHistoryApi.getBrowseHistoryPage({page:t,list_rows:s}):await e.OrderApi.getOrderPage({page:t,list_rows:s});let p=o.lodash.concat(r.pagination.data,d.data.list);r.pagination={...d.data,data:p},r.pagination.current_page<r.pagination.last_page?r.loadStatus="more":r.loadStatus="noMore"}function i(){"noMore"!==r.loadStatus&&p(r.pagination.current_page+1)}return(e,a)=>({a:o.t("goods"==t.mode?"我的浏览":"我的订单"),b:o.f(r.pagination.data,((e,a,s)=>o.e("goods"==t.mode?{a:"4da142ee-1-"+s+",4da142ee-0",b:o.p({goodsData:e})}:{},"order"==t.mode?{c:"4da142ee-2-"+s+",4da142ee-0",d:o.p({orderData:e})}:{},{e:e.id,f:o.o((o=>d("select",{type:t.mode,data:e})),e.id)}))),c:"goods"==t.mode,d:"order"==t.mode,e:o.p({status:r.loadStatus,"content-text":{contentdown:"上拉加载更多"}}),f:o.o(i),g:o.o((o=>d("close"))),h:o.p({show:t.show,showClose:!0,round:"10",backgroundColor:"#eee"})})}},n=o._export_sfc(d,[["__scopeId","data-v-4da142ee"]]);wx.createComponent(n);
