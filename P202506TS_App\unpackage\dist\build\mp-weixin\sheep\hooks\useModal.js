"use strict";const e=require("../store/index.js"),t=require("../helper/index.js"),o=require("../../common/vendor.js"),s=require("../helper/test.js"),r=require("../api/member/auth.js");function a(){e.$store("modal").$patch((e=>{e.auth="",e.extData=null}))}exports.closeAuthModal=a,exports.closeMenuTools=function(){e.$store("modal").$patch((e=>{e.menu=!1}))},exports.closeShareModal=function(){e.$store("modal").$patch((e=>{e.share=!1}))},exports.getSmsCode=function(a,i){const n=e.$store("modal"),u=n.lastTimer[a];if(void 0===u)return void t.$helper.toast("短信发送事件错误");if(!(o.dayjs().unix()-u>=60))return void t.$helper.toast("请稍后再试");if(i&&!s.test.mobile(i))return void t.$helper.toast("手机号码格式不正确");let l=-1;switch(a){case"resetPassword":l=4;break;case"changePassword":l=3;break;case"changeMobile":l=2;break;case"smsLogin":l=1}r.AuthUtil.sendSmsCode(i,l).then((e=>{0===e.code&&n.$patch((e=>{e.lastTimer[a]=o.dayjs().unix()}))}))},exports.getSmsTimer=function(s,r=""){const a=e.$store("modal").lastTimer[s];if(void 0===a)return void t.$helper.toast("短信发送事件错误");const i=o.ref(o.dayjs().unix()-a-60),n=i.value>=0;return n?"获取验证码":n?void 0:(setTimeout((()=>{i.value++}),1e3),-i.value.toString()+" 秒")},exports.saveAdvHistory=function(t){e.$store("modal").$patch((e=>{e.advHistory.includes(t.imgUrl)||e.advHistory.push(t.imgUrl)}))},exports.showAuthModal=function(t="smsLogin",o){const s=e.$store("modal");""!==s.auth?(setTimeout((()=>{s.$patch((e=>{e.auth=t,e.extData=o}))}),500),a()):s.$patch((e=>{e.auth=t}))},exports.showMenuTools=function(){e.$store("modal").$patch((e=>{e.menu=!0}))},exports.showShareModal=function(){e.$store("modal").$patch((e=>{e.share=!0}))};
