"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),o={__name:"su-fixed",props:{noNav:{type:Boolean,default:!1},bottom:{type:Boolean,default:!1},bg:{type:String,default:""},bgStyles:{type:Object,default(){}},val:{type:Number,default:0},width:{type:[String,Number],default:0},alway:{type:Boolean,default:!0},opacity:{type:Boolean,default:!1},index:{type:[Number,String],default:0},placeholder:{type:[Boolean],default:!1},sticky:{type:[Boolean],default:!1},noFixed:{type:Boolean,default:!1},ui:{type:String,default:""},clickTo:{type:Boolean,default:!1},isInset:{type:Boolean,default:!0}},setup(o){const{safeAreaInsets:n}=t.sheep.$platform.device,a=e.getCurrentInstance(),l=t.sheep.$helper.guid(),i=t.sheep.$platform.navbar,p=e.reactive({content:{},fixed:!0,scrollTop:0,opacityVal:0}),r=e.computed((()=>p.fixed&&d.bottom?d.isInset?d.val+"px":d.val+n.bottom+"px":"auto")),d=o;p.fixed=!e.unref(d.sticky),e.onPageScroll((e=>{let o=e.scrollTop;p.scrollTop=o,p.opacityVal=o>t.sheep.$platform.navbar?1:.01*o})),e.onMounted((()=>{e.nextTick$1((()=>{s()}))}));const s=()=>{e.index.createSelectorQuery().in(a).select(`#fixed-${l}`).boundingClientRect((t=>{null!=t&&(p.content=t,e.unref(d.sticky)&&u(p.scrollTop))})).exec()},u=o=>{e.unref(d.bottom)?p.fixed=o>=p.content.bottom-t.sheep.$platform.device.windowHeight+p.content.height+e.unref(d.val):p.fixed=o>=p.content.top-(e.unref(d.noNav)?e.unref(d.val):e.unref(d.val)+t.sheep.$platform.navbar)},c=()=>{d.hasToTop&&e.index.pageScrollTo({scrollTop:p.content.top,duration:100})};return(a,d)=>e.e({a:e.unref(n).bottom&&o.bottom&&o.isInset},e.unref(n).bottom&&o.bottom&&o.isInset?{b:e.s({height:e.unref(n).bottom+"px"})}:{},{c:e.o(c),d:e.s({zIndex:o.index+e.unref(t.sheep).$zIndex.navbar}),e:o.bottom},o.bottom?{f:e.n(o.bg)}:{},{g:e.n(o.ui),h:e.n(o.bg),i:e.s({zIndex:o.index+e.unref(t.sheep).$zIndex.navbar-1}),j:e.s(o.bgStyles),k:e.s(o.opacity?{opacity:p.opacityVal}:""),l:`fixed-${e.unref(l)}`,m:e.n({fixed:p.fixed}),n:e.s({left:o.sticky?"auto":"0px",top:p.fixed&&!o.bottom?(o.noNav?o.val:o.val+e.unref(i))+"px":"auto",bottom:r.value,zIndex:o.index+e.unref(t.sheep).$zIndex.navbar}),o:e.s(o.alway?"":{opacity:p.opacityVal}),p:(o.sticky||o.placeholder)&&p.fixed},(o.sticky||o.placeholder)&&p.fixed?{q:e.s({height:p.content.height+"px",width:o.width+"px"})}:{})}};wx.createComponent(o);
