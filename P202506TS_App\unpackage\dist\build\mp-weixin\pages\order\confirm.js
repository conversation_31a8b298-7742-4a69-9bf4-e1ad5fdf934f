"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),r=require("../../sheep/api/trade/order.js"),i=require("../../sheep/api/trade/config.js"),t=require("../../sheep/hooks/useGoods.js"),a=require("../../sheep/util/const.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("uni-easyinput")+e.resolveComponent("s-coupon-select")+e.resolveComponent("s-discount-list")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||(d+(()=>"../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../sheep/components/s-coupon-select/s-coupon-select.js")+(()=>"../../sheep/components/s-discount-list/s-discount-list.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const d=()=>"./addressSelection.js",n={__name:"confirm",setup(d){const n=e.reactive({orderPayload:{},orderInfo:{items:[],price:{}},showCoupon:!1,couponInfo:[],showDiscount:!1,pointStatus:!1}),s=e.ref({addressInfo:{},deliveryType:void 0,isPickUp:!0,pickUpInfo:{},receiverName:"",receiverMobile:""}),p=async()=>{n.pointStatus=!n.pointStatus,await u()};async function c(e){n.orderPayload.couponId=e,await u(),n.showCoupon=!1}function l(){if(1!==s.value.deliveryType||s.value.addressInfo.id){if(2===s.value.deliveryType){if(!s.value.pickUpInfo.id)return void o.sheep.$helper.toast("请选择自提门店地址");if(""===s.value.receiverName||""===s.value.receiverMobile)return void o.sheep.$helper.toast("请填写联系人或联系人电话");if(!/^[\u4e00-\u9fa5\w]{2,16}$/.test(s.value.receiverName))return void o.sheep.$helper.toast("请填写您的真实姓名");if(!/^1(3|4|5|7|8|9|6)\d{9}$/.test(s.value.receiverMobile))return void o.sheep.$helper.toast("请填写正确的手机号")}!async function(){const{code:e,data:i}=await r.OrderApi.createOrder({items:n.orderPayload.items,couponId:n.orderPayload.couponId,remark:n.orderPayload.remark,deliveryType:s.value.deliveryType,addressId:s.value.addressInfo.id,pickUpStoreId:s.value.pickUpInfo.id,receiverName:s.value.receiverName,receiverMobile:s.value.receiverMobile,pointStatus:n.pointStatus,combinationActivityId:n.orderPayload.combinationActivityId,combinationHeadId:n.orderPayload.combinationHeadId,seckillActivityId:n.orderPayload.seckillActivityId,pointActivityId:n.orderPayload.pointActivityId});if(0!==e)return;n.orderPayload.items[0].cartId>0&&o.sheep.$store("cart").getList();i.payOrderId&&i.payOrderId>0?o.sheep.$router.redirect("/pages/pay/index",{id:i.payOrderId}):o.sheep.$router.redirect("/pages/order/detail",{id:i.id})}()}else o.sheep.$helper.toast("请选择收货地址")}async function u(){const{data:e,code:o}=await r.OrderApi.settlementOrder({items:n.orderPayload.items,couponId:n.orderPayload.couponId,deliveryType:s.value.deliveryType,addressId:s.value.addressInfo.id,pickUpStoreId:s.value.pickUpInfo.id,receiverName:s.value.receiverName,receiverMobile:s.value.receiverMobile,pointStatus:n.pointStatus,combinationActivityId:n.orderPayload.combinationActivityId,combinationHeadId:n.orderPayload.combinationHeadId,seckillActivityId:n.orderPayload.seckillActivityId,pointActivityId:n.orderPayload.pointActivityId});return 0!==o||(n.orderInfo=e,n.couponInfo=e.coupons||[],n.orderInfo.address&&(s.value.addressInfo=n.orderInfo.address)),o}return e.onLoad((async e=>{if(!e.data)return void o.sheep.$helper.toast("参数不正确，请检查！");n.orderPayload=JSON.parse(e.data);const{data:r,code:t}=await i.TradeConfigApi.getTradeConfig();if(0===t&&(s.value.isPickUp=r.deliveryPickUpEnabled),s.value.deliveryType=a.DeliveryTypeEnum.EXPRESS.type,0!==await u()){if(s.value.isPickUp){if(s.value.deliveryType=a.DeliveryTypeEnum.PICK_UP.type,0===await u())return}s.value.deliveryType=void 0,await u()}})),e.watch(s,(async(e,o)=>{e.addressInfo.id===o.addressInfo.id&&e.deliveryType===o.deliveryType||await u()})),(r,i)=>e.e({a:e.o((e=>s.value=e)),b:e.p({modelValue:s.value}),c:e.f(n.orderInfo.items,((o,r,i)=>({a:o.skuId,b:"d65513fa-2-"+i+",d65513fa-0",c:e.p({img:o.picUrl,title:o.spuName,skuText:o.properties.map((e=>e.valueName)).join(" "),price:o.price,num:o.count,marginBottom:"10"})}))),d:e.o((e=>n.orderPayload.remark=e)),e:e.p({maxlength:"20",placeholder:"建议留言前先与商家沟通",inputBorder:!1,clearable:!1,modelValue:n.orderPayload.remark}),f:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.totalPrice)),g:n.orderPayload.pointActivityId},n.orderPayload.pointActivityId?{h:e.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),i:e.t(n.orderInfo.usePoint)}:{},{j:0===n.orderInfo.type||n.orderPayload.pointActivityId},0===n.orderInfo.type||n.orderPayload.pointActivityId?e.e({k:e.t(n.pointStatus||n.orderPayload.pointActivityId?"剩余积分":"当前积分"),l:e.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),m:e.t(n.pointStatus||n.orderPayload.pointActivityId?n.orderInfo.totalPoint-n.orderInfo.usePoint:n.orderInfo.totalPoint||0),n:!n.orderPayload.pointActivityId},n.orderPayload.pointActivityId?{}:{o:n.pointStatus,p:!n.orderInfo.totalPoint||n.orderInfo.totalPoint<=0,q:e.o(p)}):{},{r:1===s.value.deliveryType},1===s.value.deliveryType?e.e({s:n.orderInfo.price.deliveryPrice>0},n.orderInfo.price.deliveryPrice>0?{t:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.deliveryPrice))}:{}):{},{v:2===s.value.deliveryType},2===s.value.deliveryType?{w:e.o((e=>s.value.receiverName=e)),x:e.p({maxlength:"20",placeholder:"请填写您的联系姓名",inputBorder:!1,clearable:!1,modelValue:s.value.receiverName})}:{},{y:2===s.value.deliveryType},2===s.value.deliveryType?{z:e.o((e=>s.value.receiverMobile=e)),A:e.p({maxlength:"20",placeholder:"请填写您的联系电话",inputBorder:!1,clearable:!1,modelValue:s.value.receiverMobile})}:{},{B:0===n.orderInfo.type},0===n.orderInfo.type?e.e({C:n.orderPayload.couponId>0},n.orderPayload.couponId>0?{D:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.couponPrice))}:{E:e.t(n.couponInfo.filter((e=>e.match)).length>0?n.couponInfo.filter((e=>e.match)).length+" 张可用":"暂无可用优惠券"),F:e.n(n.couponInfo.filter((e=>e.match)).length>0?"text-red":"text-disabled")},{G:e.o((e=>n.showCoupon=!0))}):{},{H:n.orderInfo.price.discountPrice>0},n.orderInfo.price.discountPrice>0?{I:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.discountPrice)),J:e.o((e=>n.showDiscount=!0))}:{},{K:n.orderInfo.price.vipPrice>0},n.orderInfo.price.vipPrice>0?{L:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.vipPrice))}:{},{M:e.t(n.orderInfo.items.reduce(((e,o)=>e+o.count),0)),N:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.payPrice)),O:e.o(c),P:e.o((e=>n.showCoupon=!1)),Q:e.o((e=>n.couponInfo=e)),R:e.p({show:n.showCoupon,modelValue:n.couponInfo}),S:e.o((e=>n.showDiscount=!1)),T:e.o((e=>n.orderInfo=e)),U:e.p({show:n.showDiscount,modelValue:n.orderInfo}),V:e.t(e.unref(t.fen2yuan)(n.orderInfo.price.payPrice)),W:e.o(l),X:e.p({bottom:!0,opacity:!1,bg:"bg-white",placeholder:!0,noFixed:!1,index:200}),Y:e.p({title:"确认订单"})})}},s=e._export_sfc(n,[["__scopeId","data-v-d65513fa"]]);wx.createPage(s);
