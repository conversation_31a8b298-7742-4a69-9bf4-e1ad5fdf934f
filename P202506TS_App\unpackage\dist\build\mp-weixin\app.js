"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("./common/vendor.js"),t=require("./sheep/index.js"),n=require("./sheep/store/index.js");Math;const r={__name:"App",setup(n){const r=e.computed((()=>t.sheep.$store("user").isLogin));return e.onLaunch((()=>{setTimeout((()=>{e.index.hideTabBar({fail:()=>{}})}),200),t.ShoproInit(),r.value||t.sheep.$router.redirect("/pages/index/login")})),e.onShow((t=>{var n;if(null==(n=null==t?void 0:t.query)?void 0:n.scene){const n=(e=>{const t=e.startsWith("?")?e.substring(1):e;return decodeURIComponent(t).split("&").reduce(((e,t)=>{if(!t)return e;let[n,r]=t.split("=").map(decodeURIComponent);return n&&(e[n]=void 0!==r?r:""),e}),{})})(t.query.scene);e.index.setStorageSync("scene",n)}})),()=>{}}};function o(){const t=e.createSSRApp(r);return n.setupPinia(t),{app:t}}o().app.mount("#app"),exports.createApp=o;
