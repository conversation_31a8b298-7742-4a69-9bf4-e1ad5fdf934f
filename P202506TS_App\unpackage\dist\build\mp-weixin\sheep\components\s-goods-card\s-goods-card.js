"use strict";const t=require("../../../common/vendor.js"),o=require("../../index.js"),e=require("../../api/product/spu.js"),d=require("../../api/trade/order.js"),a=require("../../hooks/useGoods.js");if(!Array){t.resolveComponent("s-goods-column")()}Math;const i={__name:"s-goods-card",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(i){const s="oneColBigImg",r="twoCol",n="oneColSmallImg",u=t.reactive({goodsList:[],leftGoodsList:[],rightGoodsList:[]}),l=i,{layoutType:g,btnBuy:p,spuIds:c}=l.data||{},{marginLeft:f,marginRight:m}=l.styles||{},b=t.computed((()=>"text"===p.type?{background:`linear-gradient(to right, ${p.bgBeginColor}, ${p.bgEndColor})`}:"img"===p.type?{width:"54rpx",height:"54rpx",background:`url(${o.sheep.$url.cdn(p.imgUrl)}) no-repeat`,backgroundSize:"100% 100%"}:void 0));let h=0,x=0,L=0;function y(t=0,o="left"){u.goodsList[h]&&("left"===o&&(x+=t),"right"===o&&(L+=t),x<=L?u.leftGoodsList.push(u.goodsList[h]):u.rightGoodsList.push(u.goodsList[h]),h++)}return t.onMounted((async()=>{u.goodsList=await async function(t){const{data:o}=await e.SpuApi.getSpuListByIds(t);return o}(c.join(",")),await d.OrderApi.getSettlementProduct(u.goodsList.map((t=>t.id)).join(",")).then((t=>{0===t.code&&a.appendSettlementProduct(u.goodsList,t.data)})),g===r&&y()})),(e,d)=>t.e({a:t.unref(g)===s&&u.goodsList.length},t.unref(g)===s&&u.goodsList.length?{b:t.f(u.goodsList,((e,d,a)=>{var s;return{a:t.o((d=>t.unref(o.sheep).$router.go("/pages/goods/index",{id:e.id})),e.id),b:"eca9a26c-0-"+a,c:t.p({size:"sl",goodsFields:i.data.fields,tagStyle:i.data.badge,data:e,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom}),d:e.id}})),c:t.t("text"===t.unref(p).type?t.unref(p).text:""),d:t.s(b.value),e:t.s({marginBottom:2*i.data.space+"rpx"})}:{},{f:t.unref(g)===r&&u.goodsList.length},t.unref(g)===r&&u.goodsList.length?{g:t.f(u.leftGoodsList,((e,d,a)=>{var s;return{a:t.o((d=>t.unref(o.sheep).$router.go("/pages/goods/index",{id:e.id})),e.id),b:t.o((t=>y(t,"left")),e.id),c:"eca9a26c-1-"+a,d:t.p({size:"md",goodsFields:i.data.fields,tagStyle:i.data.badge,data:e,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom,titleWidth:330-t.unref(f)-t.unref(m)}),e:e.id}})),h:t.t("text"===t.unref(p).type?t.unref(p).text:""),i:t.s(b.value),j:t.s({paddingRight:i.data.space+"rpx",marginBottom:i.data.space+"px"}),k:t.f(u.rightGoodsList,((e,d,a)=>{var s;return{a:t.o((d=>t.unref(o.sheep).$router.go("/pages/goods/index",{id:e.id})),e.id),b:t.o((t=>y(t,"right")),e.id),c:"eca9a26c-2-"+a,d:t.p({size:"md",goodsFields:i.data.fields,tagStyle:i.data.badge,data:e,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom,titleWidth:330-t.unref(f)-t.unref(m)}),e:e.id}})),l:t.t("text"===t.unref(p).type?t.unref(p).text:""),m:t.s(b.value),n:t.s({paddingLeft:i.data.space+"rpx",marginBottom:i.data.space+"px"})}:{},{o:t.unref(g)===n&&u.goodsList.length},t.unref(g)===n&&u.goodsList.length?{p:t.f(u.goodsList,((e,d,a)=>{var s;return{a:t.o((d=>t.unref(o.sheep).$router.go("/pages/goods/index",{id:e.id})),e.id),b:"eca9a26c-3-"+a,c:t.p({size:"lg",goodsFields:i.data.fields,data:e,tagStyle:i.data.badge,titleColor:null==(s=i.data.fields.name)?void 0:s.color,subTitleColor:i.data.fields.introduction.color,topRadius:i.data.borderRadiusTop,bottomRadius:i.data.borderRadiusBottom}),d:e.id}})),q:t.t("text"===t.unref(p).type?t.unref(p).text:""),r:t.s(b.value),s:t.s({marginBottom:i.data.space+"px"})}:{})}},s=t._export_sfc(i,[["__scopeId","data-v-eca9a26c"]]);wx.createComponent(s);
