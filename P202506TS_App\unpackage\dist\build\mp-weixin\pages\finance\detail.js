"use strict";const e=require("../../common/vendor.js");require("../../sheep/index.js");const t=require("../../sheep/api/member/reportCwfx.js");if(!Array){(e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-forms")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const o={__name:"detail",setup(o){const a=e.reactive({id:"",model:{},content:""});e.computed((()=>e.index.getStorageSync("enterprise")));return e.onLoad((o=>{a.id=o.id,(async()=>{const{data:o}=await t.ReportCwfxApi.getReportCwfx(a.id);a.model=e.clone(o)})()})),(t,o)=>({a:e.t(a.model.companyName),b:e.p({name:"companyName",label:"企业名称"}),c:e.t(a.model.status),d:e.p({name:"status",label:"检测状态"}),e:e.t(a.model.orderNo),f:e.p({name:"orderNo",label:"订单号"}),g:e.t(a.model.detailData),h:e.p({name:"detailData",label:"订单信息"}),i:e.t(a.model.mobile),j:e.p({name:"mobile",label:"手机号"}),k:e.t(a.model.type),l:e.p({name:"type",label:"检测类型"}),m:e.t(a.model.periodTime),n:e.p({name:"periodTime",label:"评测周期"}),o:e.t(a.model.evaYear),p:e.p({name:"evaYear",label:"评估年份"}),q:e.t(a.model.cit),r:e.p({name:"cit",label:"本期企业所得税应纳税额合计"}),s:e.t(a.model.vat),t:e.p({name:"vat",label:"本期增值税应纳税额合计"}),v:e.t(a.model.inputVat),w:e.p({name:"inputVat",label:"本期増值税进项税额"}),x:e.t(a.model.outputVat),y:e.p({name:"outputVat",label:"本期増值税销项税额"}),z:e.t(a.model.preCit),A:e.p({name:"preCit",label:"上期企业所得税应纳税额合计"}),B:e.t(a.model.preVat),C:e.p({name:"preVat",label:"上期增值税应纳税额合计"}),D:e.t(a.model.preInputVat),E:e.p({name:"preInputVat",label:"上期増值税进项税额"}),F:e.t(a.model.preOutputVat),G:e.p({name:"preOutputVat",label:"上期増值税销项税额"}),H:e.p({model:a.model,rules:a.rules,labelPosition:"top",labelWidth:"400",border:!0}),I:e.p({title:"财务检测详情"})})}},a=e._export_sfc(o,[["__scopeId","data-v-10184bd9"]]);wx.createPage(a);
