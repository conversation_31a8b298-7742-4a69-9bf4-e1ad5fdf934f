"use strict";const e=require("../../request/index.js"),t={getFavoritePage:t=>e.request({url:"/product/favorite/page",method:"GET",params:t}),isFavoriteExists:t=>e.request({url:"/product/favorite/exits",method:"GET",params:{spuId:t}}),createFavorite:t=>e.request({url:"/product/favorite/create",method:"POST",data:{spuId:t},custom:{auth:!0,showSuccess:!0,successMsg:"收藏成功"}}),deleteFavorite:t=>e.request({url:"/product/favorite/delete",method:"DELETE",data:{spuId:t},custom:{auth:!0,showSuccess:!0,successMsg:"取消成功"}})};exports.FavoriteApi=t;
