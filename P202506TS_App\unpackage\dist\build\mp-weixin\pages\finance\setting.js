"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js");require("../../sheep/store/index.js"),require("../../sheep/helper/index.js"),require("../../sheep/request/index.js");const s=require("../../sheep/api/member/reportCwfx.js");if(!Array){(e.resolveComponent("uni-easyinput")+e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-forms")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"setting",setup(t){const n=e.reactive({model:{},rules:{}}),i=e.computed((()=>e.index.getStorageSync("enterprise")));async function r(){1!==n.model.cwfxStatus?(await s.ReportCwfxApi.updateStatus(n.model),l()):o.sheep.$helper.toast("已开通")}const l=async()=>{const{data:o}=await s.ReportCwfxApi.getStatus(i.value.id);n.model=e.clone(o)};return e.onBeforeMount((()=>{l()})),(o,s)=>({a:e.o((e=>n.model.cwfxMobile=e)),b:e.p({placeholder:"手机号",inputBorder:!1,disabled:1===n.model.cwfxStatus,styles:{disableColor:"#fff"},placeholderStyle:"color:#BBBBBB;font-size:28rpx;line-height:normal",clearable:!1,modelValue:n.model.cwfxMobile}),c:e.p({name:"cwfxMobile",label:"手机号"}),d:e.t(n.model.cwfxToken||"无"),e:e.p({name:"cwfxToken",label:"token"}),f:e.t(["未开通","已开通"][n.model.cwfxStatus||0]),g:e.p({name:"cwfxStatus",label:"开通状态"}),h:e.p({model:n.model,rules:n.rules,labelPosition:"left",border:!0}),i:e.o(r),j:e.p({bottom:!0,placeholder:!0,bg:"none"}),k:e.p({title:"财务自动检测开通"})})}},n=e._export_sfc(t,[["__scopeId","data-v-f1fa9768"]]);wx.createPage(n);
