<su-popup wx:if="{{l}}" class="data-v-1df68b1d" u-s="{{['d']}}" bindclose="{{k}}" u-i="1df68b1d-0" bind:__l="__l" u-p="{{l}}"><view class="ui-region-picker data-v-1df68b1d"><su-toolbar wx:if="{{c}}" class="data-v-1df68b1d" bindcancel="{{a}}" bindconfirm="{{b}}" u-i="1df68b1d-1,1df68b1d-0" bind:__l="__l" u-p="{{c}}"/><view class="ui-picker-body data-v-1df68b1d"><block wx:if="{{r0}}"><picker-view value="{{g}}" bindchange="{{h}}" class="ui-picker-view data-v-1df68b1d" bindpickstart="{{i}}" bindpickend="{{j}}"><picker-view-column class="data-v-1df68b1d"><view wx:for="{{d}}" wx:for-item="province" wx:key="c" class="ui-column-item data-v-1df68b1d"><view class="data-v-1df68b1d" style="{{province.b}}">{{province.a}}</view></view></picker-view-column><picker-view-column class="data-v-1df68b1d"><view wx:for="{{e}}" wx:for-item="city" wx:key="c" class="ui-column-item data-v-1df68b1d"><view class="data-v-1df68b1d" style="{{city.b}}">{{city.a}}</view></view></picker-view-column><picker-view-column class="data-v-1df68b1d"><view wx:for="{{f}}" wx:for-item="district" wx:key="c" class="ui-column-item data-v-1df68b1d"><view class="data-v-1df68b1d" style="{{district.b}}">{{district.a}}</view></view></picker-view-column></picker-view></block></view></view></su-popup>