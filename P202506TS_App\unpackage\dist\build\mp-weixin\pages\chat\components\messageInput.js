"use strict";const e=require("../../../common/vendor.js");if(!Array){e.resolveComponent("uni-easyinput")()}Math;const o={__name:"messageInput",props:{modelValue:{type:String,default:""},toolsMode:{type:String,default:""}},emits:["update:modelValue","onTools","sendMessage"],setup(o,{emit:t}){const a=o,l=t,s=e.computed({get:()=>a.modelValue,set(e){l("update:modelValue",e)}});function u(e){l("onTools",e)}function n(){l("sendMessage")}return(t,a)=>e.e({a:e.o((e=>s.value=e)),b:e.p({inputBorder:!1,clearable:!1,placeholder:"请输入你要咨询的问题",modelValue:s.value}),c:e.o((e=>u("emoji"))),d:!s.value},s.value?{}:{e:"tools"===o.toolsMode?1:"",f:e.o((e=>u("tools")))},{g:s.value},s.value?{h:e.o(n)}:{})}},t=e._export_sfc(o,[["__scopeId","data-v-82ed4e2d"]]);wx.createComponent(t);
