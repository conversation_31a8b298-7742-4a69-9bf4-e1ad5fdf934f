"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../sheep/index.js"),a=require("../../../../sheep/hooks/useModal.js"),t=require("../../../../sheep/api/member/channel.js"),s=require("../../../../sheep/api/trade/order.js"),n=require("../../../../sheep/hooks/useGoods.js"),i=require("../../../../sheep/platform/pay.js"),d=require("../../../../sheep/api/pay/channel.js"),r={__name:"enterprise-package",setup(r){const c=o.sheep.$store("modal"),p=e.computed((()=>c.extData));console.log("extData",p);const l=e.reactive({inviteCode:"",goodsInfo:[],skuId:"",loadStatus:"",orderType:"goods",payment:"",payMethods:[]}),u=e.index.getStorageSync("scene");function y(e){l.skuId=e,e&&async function(){try{console.log("开始获取支付方式...");const{data:e,code:o}=await d.PayChannelApi.getEnableChannelCodeList(9);if(console.log("支付渠道API响应:",{data:e,code:o}),0===o&&e&&e.length>0){const o=i.getPayMethods(e);console.log("获取到的支付方式:",o),l.payMethods=o.filter((e=>"yeepay"===e.value));const a=l.payMethods.find((e=>!e.disabled));a&&(l.payment=a.value,console.log("默认选择支付方式:",a.value))}else console.warn("未获取到支付渠道数据:",{data:e,code:o}),l.payMethods=[{icon:"/static/img/shop/pay/yeepay.png",title:"微信支付（易宝）",value:"yeepay",disabled:!1}],l.payment="yeepay",console.log("使用默认支付方式")}catch(e){console.error("获取支付方式失败:",e),l.payMethods=[{icon:"/static/img/shop/pay/yeepay.png",title:"微信支付（易宝）",value:"yeepay",disabled:!1}],l.payment="yeepay"}}()}function h(e){l.payment=e.detail.value}async function g(){const e=l.goodsInfo.find((e=>e.skus[0].id===l.skuId)),o={enterpriseId:p.value.id,items:[{skuId:l.skuId,count:1,picUrl:null==e?void 0:e.picUrl}],deliveryType:2,pickUpStoreId:2,receiverName:"123",receiverMobile:"13212311123",pointStatus:!1,inviteCode:l.inviteCode},{code:a,data:t}=await s.OrderApi.createOrder(o);0===a&&m(t)}u&&u.inviteCode&&(l.inviteCode=u.inviteCode);const m=e=>{o.sheep.$platform.pay(l.payment,l.orderType,e.payOrderId)},v=()=>{a.closeAuthModal(),o.sheep.$router.redirect("/pages/index/register")};return e.onMounted((async()=>{console.log("onShow","onShow"),l.inviteCode?await async function(){l.loadStatus="loading";let{code:e,data:o}=await t.ChannelApi.getProductsByInviteCode(l.inviteCode);0===e?(l.goodsInfo=o.map((e=>({...e,skus:[{id:e.skuId,price:e.price,marketPrice:e.marketPrice,stock:e.stock}]}))),console.log("state.goodsInfo",l.goodsInfo)):l.loadStatus="error"}():o.sheep.$helper.toast("请扫描邀请码进入")})),(t,s)=>e.e({a:e.f(l.goodsInfo,((a,t,s)=>{return{a:e.unref(o.sheep).$url.static(a.picUrl),b:e.t(a.name),c:e.f((i=a.name,{"青铜会员":["税务风险检测1次","AI财税专家问答智能体","财税商学院"],"白银会员":["税务风险检测1年","AI财税专家问答智能体","财税商学院"],"黄金会员":["税务风险检测2年","AI财税专家问答智能体","财税商学院"]}[i]||["专业财税服务","AI智能分析","风险预警等"]),((o,a,t)=>({a:e.t(o),b:o}))),d:e.t(e.unref(n.fen2yuan)(a.skus[0].price)),e:e.t(e.unref(n.fen2yuan)(a.skus[0].marketPrice)),f:a.id,g:l.skuId===a.skus[0].id?1:"",h:a.name.includes("黄金")?1:"",i:a.name.includes("白银")?1:"",j:a.name.includes("青铜")?1:"",k:e.o((e=>y(a.skus[0].id)),a.id)};var i})),b:l.skuId},l.skuId?{c:e.f(l.payMethods,((o,a,t)=>({a:o.icon,b:e.t(o.title),c:o.value,d:l.payment===o.value,e:o.disabled,f:o.value,g:o.disabled?1:""}))),d:e.o(h)}:{},{e:p.value&&p.value.id},p.value&&p.value.id?{f:e.o(g),g:!l.skuId||!l.payment}:{h:e.o((o=>e.unref(a.showAuthModal)("enterpriseRegister"))),i:!l.skuId},{j:e.o(v)})}},c=e._export_sfc(r,[["__scopeId","data-v-979408aa"]]);wx.createComponent(c);
