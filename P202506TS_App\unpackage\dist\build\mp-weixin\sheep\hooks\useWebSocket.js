"use strict";const e=require("../../common/vendor.js"),t=require("../config/index.js"),n=require("../util/index.js"),o=require("../request/index.js");exports.useWebSocket=function(r){const u=e.reactive({url:(t.baseUrl+t.websocketPath).replace("http","ws")+"?token="+o.getRefreshToken(),isReconnecting:!1,reconnectInterval:3e3,heartBeatInterval:5e3,pingTimeoutDuration:1e3,heartBeatTimer:null,destroy:!1,pingTimeout:null,reconnectTimeout:null,onConnected:()=>{},onClosed:()=>{},onMessage:e=>{}}),c=e.ref(null),i=()=>{u.heartBeatTimer=setInterval((()=>{var e;e="ping",c.value&&!u.destroy&&c.value.send({data:e}),u.pingTimeout=setTimeout((()=>{l()}),u.pingTimeoutDuration)}),u.heartBeatInterval)},a=()=>{clearInterval(u.heartBeatTimer),s()},l=()=>{!u.destroy&&c.value&&(u.isReconnecting=!0,u.reconnectTimeout&&clearTimeout(u.reconnectTimeout),u.reconnectTimeout=setTimeout((()=>{u.destroy||(u.isReconnecting=!1,m())}),u.reconnectInterval))},s=()=>{u.pingTimeout&&(clearTimeout(u.pingTimeout),u.pingTimeout=null)},m=()=>{u.destroy=!1,n.copyValueToTarget(u,r),c.value=e.index.connectSocket({url:u.url,complete:()=>{},success:()=>{}}),c.value.onOpen((()=>{console.log("WebSocket 连接成功"),u.onConnected(),i()})),c.value.onMessage((e=>{try{"pong"===e.data?s():u.onMessage(JSON.parse(e.data))}catch(t){console.error(t)}})),c.value.onClose((e=>{u.destroy?u.onClosed():(a(),l())}))};return m(),e.onBeforeUnmount((()=>{u.destroy=!0,a(),u.reconnectTimeout&&clearTimeout(u.reconnectTimeout),c.value&&(c.value.close(),c.value=null)})),{options:u}};
