<template>
  <el-dialog
    title="设置权益"
    v-model="dialogVisible"
    width="1200px"
    append-to-body
    destroy-on-close
  >
    <div class="product-info">
      <div class="info-item">
        <span class="label">产品名称：</span>
        <span class="value">{{ productName }}</span>
      </div>
      <div class="info-item">
        <span class="label">产品类型：</span>
        <span class="value">{{ productType }}</span>
      </div>
    </div>

    <div class="benefit-container">
      <!-- 左侧：已选权益列表 -->
      <div class="benefit-selected">
        <h3>已选权益</h3>
        <div 
          class="benefit-list"
          @dragover.prevent
          @drop="handleDrop"
        >
          <div v-for="(item, index) in selectedBenefits" :key="index" class="benefit-item">
            <div class="benefit-info">
              <div class="benefit-name">{{ item.name }}</div>
              <div class="benefit-control">
                <el-radio-group v-model="item.limitType">
                  <el-radio label="unlimited">不限次数</el-radio>
                  <el-radio label="limited">
                    限制
                    <el-input-number 
                      v-if="item.limitType === 'limited'"
                      v-model="item.total"
                      :min="1"
                      controls-position="right"
                      style="width: 120px; margin-left: 10px;"
                    />
                    <el-select
                      v-if="item.limitType === 'limited'"
                      v-model="item.unit"
                      style="width: 80px; margin-left: 10px;"
                    >
                      <el-option label="份" value="份" />
                      <el-option label="次" value="次" />
                      <el-option label="天" value="天" />
                    </el-select>
                  </el-radio>
                </el-radio-group>
              </div>
            </div>
            <el-button type="danger" link @click="removeBenefit(index, item)">删除</el-button>
          </div>
        </div>
      </div>

      <!-- 右侧：可选权益列表 -->
      <div class="benefit-available">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入名称"
            style="width: 200px"
          />
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </div>
        <el-table v-loading="loading" :data="filteredBenefits" border height="500">
          <el-table-column label="名称" prop="name">
            <template #default="scope">
              <div 
                class="draggable-benefit"
                draggable="true"
                @dragstart="handleDragStart($event, scope.row)"
              >
                {{ scope.row.name }}
                <el-button link type="primary" @click="addBenefit(scope.row)">
                  选择
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="代码" prop="code" width="150" />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip />
          <el-table-column label="状态" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? '开启' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { BenefitApi } from '@/api/product/benefit'
import { BenefitRelationApi } from '@/api/product/benefitrelation'

const props = defineProps({
  productName: {
    type: String,
    required: true
  },
  productType: {
    type: String,
    required: true
  },
  spuId: {
    type: Number,
    required: true
  }
})

const dialogVisible = ref(false)
const searchKeyword = ref('')
const loading = ref(false)

// 可选择的权益列表
const availableBenefits = ref([])

// 已选择的权益列表
const selectedBenefits = ref([])

// 加载权益列表
const loadBenefits = async () => {
  try {
    loading.value = true
    const res = await BenefitApi.getBenefitPage({
      pageNo: 1,
      pageSize: 100,
      name: searchKeyword.value
    })
    availableBenefits.value = res.list || []
    console.log('可用权益列表:', availableBenefits.value)
  } catch (error) {
    ElMessage.error('获取权益列表失败')
  } finally {
    loading.value = false
  }
}

// 修改搜索过滤逻辑，排除已选择的权益
const filteredBenefits = computed(() => {
  // 首先过滤掉已选择的权益
  return availableBenefits.value.filter(benefit => 
    !selectedBenefits.value.some(selected => selected.benefitId === benefit.id)
  )
})

/** 搜索 */
const handleSearch = () => {
  loadBenefits()
}

/** 添加权益 */
const addBenefit = (benefit: any) => {
  // 检查是否已经添加
  if (selectedBenefits.value.some(item => item.benefitId === benefit.id)) {
    ElMessage.warning('该权益已添加')
    return
  }
  
  selectedBenefits.value.push({
    benefitId: benefit.id,
    name: benefit.name,
    limitType: 'unlimited',
    total: -1,
    unit: '次'
  })
}

/** 移除权益 */
const removeBenefit = async (index: number, item: any) => {
  try {
    // 添加确认提示
    await ElMessageBox.confirm('确认要删除该权益吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 如果是已有的权益关联（有id），则从数据库中删除
    if (item.id) {
      await BenefitRelationApi.deleteBenefitRelation(item.id)
      ElMessage.success('删除成功')
    }
    
    // 从列表中移除
    selectedBenefits.value.splice(index, 1)
  } catch (error) {
    // 用户取消或删除失败
    if (error !== 'cancel') {
      console.error('删除权益失败:', error)
    }
  }
}

/**
 * 转换单位值
 * 如果单位是天，需要将值转换为秒
 * 同时兼容历史的分钟数据
 */
const convertUnitValue = (value, unit, toDatabase = true) => {
  if (unit === '天' && toDatabase) {
    // 转换为秒存入数据库：天 × 24 × 60 × 60
    return value * 24 * 60 * 60
  } else if (unit === '天' && !toDatabase) {
    // 从数据库读取时转换为天：秒 ÷ (24 × 60 × 60)
    return Math.floor(value / (24 * 60 * 60))
  } else if (unit === '分钟' && toDatabase) {
    // 兼容历史数据：分钟转换为秒存入数据库
    return value * 60
  } else if (unit === '分钟' && !toDatabase) {
    // 兼容历史数据：从数据库读取时转换为分钟，但需要进一步转换为天显示
    const minutes = Math.floor(value / 60)
    return Math.floor(minutes / (24 * 60)) // 转换为天数显示
  }
  return value
}

/** 提交表单 */
const submitForm = async () => {
  try {
    // 保存每个权益
    for (const benefit of selectedBenefits.value) {
      // 计算要保存的值
      const totalValue = benefit.limitType === 'unlimited' ? -1 : 
        convertUnitValue(benefit.total, benefit.unit, true)
      
      // 确定要保存到数据库的单位
      let saveUnit = benefit.unit || '次'
      if (benefit.unit === '天') {
        // 前端显示为天，但数据库存储单位应该是秒
        saveUnit = '秒'
      }

      const data = {
        productId: props.spuId,
        benefitId: benefit.benefitId,
        total: totalValue,
        unit: saveUnit
      }
      
      if (benefit.id) {
        // 更新已有权益
        await BenefitRelationApi.updateBenefitRelation({
          id: benefit.id,
          ...data
        })
      } else {
        // 创建新权益
        await BenefitRelationApi.createBenefitRelation(data)
      }
    }
    
    ElMessage.success('设置权益成功')
    emit('success')
    dialogVisible.value = false
  } catch (error) {
    ElMessage.error('设置权益失败')
  }
}

/** 打开弹窗 */
const open = (currentBenefits = []) => {
  dialogVisible.value = true
  console.log('当前权益列表:', currentBenefits)
  
  // 检查数据结构，确定正确的字段名
  selectedBenefits.value = [...currentBenefits].map(item => {
    // 打印每个权益项，查看字段结构
    console.log('权益项:', item)

    // 处理单位转换和历史数据兼容
    let totalValue = item.total
    let displayUnit = item.unit || '次'

    if (item.total !== -1) {
      if (item.unit === '分钟') {
        // 历史分钟数据：转换为天数显示，并更新单位为天
        totalValue = convertUnitValue(item.total, item.unit, false)
        displayUnit = '天'
      } else if (item.unit === '秒') {
        // 秒数数据（新的天数权益）：从秒转换为天数显示
        totalValue = convertUnitValue(item.total, '天', false)
        displayUnit = '天'
      } else if (item.unit === '天') {
        // 兼容可能存在的天数数据：从秒转换为天数显示
        totalValue = convertUnitValue(item.total, item.unit, false)
      }
    }

    return {
      id: item.id,
      benefitId: item.benefitId,
      name: item.benefitName || item.name || '未知权益',
      limitType: item.total === -1 ? 'unlimited' : 'limited',
      total: item.total === -1 ? 0 : totalValue,
      unit: displayUnit
    }
  })
  
  loadBenefits()
}

const emit = defineEmits(['success'])

defineExpose({
  open
})

/** 开始拖拽 */
const handleDragStart = (event: DragEvent, benefit: any) => {
  event.dataTransfer?.setData('benefit', JSON.stringify(benefit))
}

/** 处理拖拽放置 */
const handleDrop = (event: DragEvent) => {
  const benefitData = event.dataTransfer?.getData('benefit')
  if (!benefitData) return

  const benefit = JSON.parse(benefitData)
  addBenefit(benefit)
}

// 初始加载
onMounted(() => {
  // 初始时不加载，等到打开弹窗时再加载
})
</script>

<style scoped>
.product-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  color: #606266;
  margin-right: 8px;
}

.value {
  font-weight: bold;
}

.benefit-container {
  display: flex;
  gap: 20px;
  margin: 20px 0;
  height: 600px;
}

.benefit-selected {
  width: 400px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  transition: border-color 0.3s;
}

.benefit-selected.drag-over {
  border-color: var(--el-color-primary);
}

.benefit-selected h3 {
  margin: 0 0 16px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
}

.benefit-list {
  height: calc(100% - 50px);
  overflow-y: auto;
}

.benefit-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.benefit-info {
  flex: 1;
  margin-right: 20px;
}

.benefit-name {
  font-weight: bold;
  margin-bottom: 10px;
}

.benefit-control {
  margin-top: 10px;
}

.draggable-benefit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  padding: 4px 0;
}

.draggable-benefit:hover {
  background-color: #f5f7fa;
}

.benefit-available {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.search-bar {
  padding: 16px;
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #dcdfe6;
}
</style> 