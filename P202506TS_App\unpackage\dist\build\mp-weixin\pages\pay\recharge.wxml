<s-layout wx:if="{{i}}" u-s="{{['d']}}" class="withdraw-wrap data-v-9e17e370" style="{{h}}" u-i="9e17e370-0" bind:__l="__l" u-p="{{i}}"><view class="wallet-num-box ss-flex ss-col-center ss-row-between data-v-9e17e370" style="{{c}}"><view class=" data-v-9e17e370"><view class="num-title data-v-9e17e370">当前余额（元）</view><view class="wallet-num data-v-9e17e370">{{a}}</view></view><button class="ss-reset-button log-btn data-v-9e17e370" bindtap="{{b}}"> 充值记录 </button></view><view class="recharge-box data-v-9e17e370"><view class="recharge-card-box data-v-9e17e370"><view class="input-label ss-m-b-50 data-v-9e17e370">充值金额</view><view class="input-box ss-flex border-bottom ss-p-b-20 data-v-9e17e370"><view class="unit data-v-9e17e370">￥</view><uni-easyinput wx:if="{{e}}" class="data-v-9e17e370" u-i="9e17e370-1,9e17e370-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"/></view><view class="face-value-box ss-flex ss-flex-wrap ss-m-y-40 data-v-9e17e370"><button wx:for="{{f}}" wx:for-item="item" wx:key="d" class="{{['ss-reset-button', 'face-value-btn', 'data-v-9e17e370', item.e]}}" bindtap="{{item.f}}"><text class="face-value-title data-v-9e17e370">{{item.a}}</text><view wx:if="{{item.b}}" class="face-value-tag data-v-9e17e370"> 送 {{item.c}} 元 </view></button></view><button class="ss-reset-button save-btn ui-BG-Main-Gradient ss-m-t-60 ui-Shadow-Main data-v-9e17e370" bindtap="{{g}}"> 确认充值 </button></view></view></s-layout>