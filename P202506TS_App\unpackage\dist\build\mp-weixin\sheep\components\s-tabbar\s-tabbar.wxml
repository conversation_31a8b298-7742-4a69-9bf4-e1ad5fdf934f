<view wx:if="{{a}}" class="u-page__item"><su-tabbar wx:if="{{c}}" u-s="{{['d']}}" u-i="57cd8214-0" bind:__l="__l" u-p="{{c}}"><su-tabbar-item wx:for="{{b}}" wx:for-item="item" wx:key="e" u-s="{{['active-icon','inactive-icon']}}" bindtap="{{item.f}}" u-i="{{item.g}}" bind:__l="__l" u-p="{{item.h}}"><image class="u-page__item__slot-icon" style="{{'transform:' + item.a + ';' + ('transform-origin:' + 'bottom')}}" src="{{item.b}}" mode="heightFix" slot="active-icon"></image><image class="u-page__item__slot-icon" style="{{'transform:' + item.c + ';' + ('transform-origin:' + 'bottom')}}" src="{{item.d}}" mode="heightFix" slot="inactive-icon"></image></su-tabbar-item></su-tabbar></view>