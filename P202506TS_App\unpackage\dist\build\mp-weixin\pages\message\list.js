"use strict";const t=require("../../common/vendor.js"),e=require("../../sheep/index.js"),a=require("../../sheep/util/index.js"),o=require("../../sheep/api/system/notifyMessage.js");if(!Array){(t.resolveComponent("su-tabs")+t.resolveComponent("su-sticky")+t.resolveComponent("uni-list-item")+t.resolveComponent("uni-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-empty")+t.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const n={__name:"list",setup(n){const s=t.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:6},readStatus:null,tabMaps:[{name:"全部",value:""},{name:"未读",value:!1},{name:"已读",value:!0}],loadStatus:""});function i(){a.resetPagination(s.pagination)}function l(t){i(),s.currentTab=t.index,s.readStatus=s.tabMaps[t.index].value,p()}async function p(){s.loadStatus="loading";const{code:e,data:a}=await o.NotifyMessageApi.getMyNotifyMessagePage({pageNo:s.pagination.pageNo,pageSize:s.pagination.pageSize,readStatus:s.readStatus,keyword:s.keyword});0===e&&(s.pagination.list=t.lodash.concat(s.pagination.list,a.list),s.pagination.total=a.total,s.loadStatus=s.pagination.list.length<s.pagination.total?"more":"noMore")}function u(){"noMore"!==s.loadStatus&&(s.pagination.pageNo++,p())}const r=async t=>{const{code:e}=await o.NotifyMessageApi.updateNotifyMessageRead(t);var a;0===e&&await(s.keyword=a,i(),void p())};return t.onLoad((t=>{p()})),t.onReachBottom((()=>{u()})),t.onPullDownRefresh((()=>{p(),setTimeout((function(){t.index.stopPullDownRefresh()}),800)})),(o,n)=>t.e({a:t.o(l),b:t.p({list:s.tabMaps,scrollable:!1,current:s.currentTab}),c:t.p({bgColor:"#fff"}),d:s.pagination.total>0},s.pagination.total>0?{e:t.f(s.pagination.list,((o,n,s)=>t.e({a:t.t(["","通知公告","系统消息"][o.templateType]),b:t.n("通知公告"===o.templateType?"orange":"gray"),c:t.t(o.readStatus?"已读":"未读"),d:t.n(o.readStatus?"orange":"gray"),e:t.t(o.templateParams.title),f:t.t(o.templateParams.content),g:t.t(t.unref(a.formatDate)(o.createTime,"YYYY.MM.DD")),h:o.readStatus},o.readStatus?{i:t.o((t=>(async t=>{t.readStatus||await r(id),e.sheep.$router.go("/pages/message/detail",{id:t.id})})(o)),n)}:{j:t.o((t=>r(o.id)),n)},{k:n,l:"8a8497a9-4-"+s+",8a8497a9-3"})))}:{},{f:s.pagination.total>0},s.pagination.total>0?{g:t.o(u),h:t.p({status:s.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{i:0===s.pagination.total},0===s.pagination.total?{j:t.p({icon:t.unref(e.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无稿件"})}:{},{k:t.p({title:"消息列表"})})}},s=t._export_sfc(n,[["__scopeId","data-v-8a8497a9"]]);wx.createPage(s);
