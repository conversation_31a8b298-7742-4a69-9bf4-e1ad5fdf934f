"use strict";const t=require("../../request/index.js"),e={addCart:e=>t.request({url:"/trade/cart/add",method:"POST",data:e,custom:{showSuccess:!0,successMsg:"已添加到购物车~"}}),updateCartCount:e=>t.request({url:"/trade/cart/update-count",method:"PUT",data:e}),updateCartSelected:e=>t.request({url:"/trade/cart/update-selected",method:"PUT",data:e}),deleteCart:e=>t.request({url:"/trade/cart/delete",method:"DELETE",params:{ids:e}}),getCartList:()=>t.request({url:"/trade/cart/list",method:"GET",custom:{showLoading:!1,auth:!0}})};exports.CartApi=e;
