"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js");if(!Array){t.resolveComponent("uni-fab")()}Math;const n={__name:"s-float-menu",props:{data:{type:Object,default(){}}},setup(n){var o;const r=n,a=t.reactive({pattern:[],content:[],direction:""}),i=t.ref(null);function l(t){e.sheep.$router.go(t.item.url)}function u(){var e,n;(null==(e=t.unref(i))?void 0:e.isShow)&&(null==(n=t.unref(i))||n.close())}return a.direction=r.data.direction,null==(o=r.data)||o.list.forEach((t=>{var n;const o=(null==(n=r.data)?void 0:n.showText)?t.text:"";a.content.push({iconPath:e.sheep.$url.cdn(t.imgUrl),url:t.url,text:o}),a.pattern.push({color:t.textColor})})),t.onBackPress((()=>{var e,n;return!!(null==(e=t.unref(i))?void 0:e.isShow)&&(null==(n=t.unref(i))||n.close(),!0)})),(e,n)=>{var o,r;return t.e({a:null==(o=i.value)?void 0:o.isShow},(null==(r=i.value)?void 0:r.isShow)?{b:t.o(u)}:{},{c:t.sr(i,"5bf5ab3a-0",{k:"fabRef"}),d:t.o(l),e:t.p({horizontal:"right",vertical:"bottom",direction:a.direction,pattern:a.pattern,content:a.content})})}}},o=t._export_sfc(n,[["__scopeId","data-v-5bf5ab3a"]]);wx.createComponent(o);
