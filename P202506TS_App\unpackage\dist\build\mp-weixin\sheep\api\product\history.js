"use strict";const s=require("../../request/index.js"),e={deleteBrowseHistory:e=>s.request({url:"/product/browse-history/delete",method:"DELETE",data:{spuIds:e},custom:{showSuccess:!0,successMsg:"删除成功"}}),cleanBrowseHistory:()=>s.request({url:"/product/browse-history/clean",method:"DELETE",custom:{showSuccess:!0,successMsg:"清空成功"}}),getBrowseHistoryPage:e=>s.request({url:"/product/browse-history/page",method:"GET",data:e,custom:{showLoading:!1}})};exports.SpuHistoryApi=e;
