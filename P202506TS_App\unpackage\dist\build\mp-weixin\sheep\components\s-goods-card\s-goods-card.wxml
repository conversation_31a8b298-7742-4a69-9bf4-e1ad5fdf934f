<view class="data-v-eca9a26c"><view wx:if="{{a}}" class="goods-sl-box data-v-eca9a26c"><view wx:for="{{b}}" wx:for-item="item" wx:key="d" class="goods-box data-v-eca9a26c" style="{{e}}"><s-goods-column wx:if="{{item.c}}" u-s="{{['cart']}}" class=" data-v-eca9a26c" bindclick="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"><button class="ss-reset-button cart-btn data-v-eca9a26c" style="{{d}}" slot="cart">{{c}}</button></s-goods-column></view></view><view wx:if="{{f}}" class="goods-md-wrap ss-flex ss-flex-wrap ss-col-top data-v-eca9a26c"><view class="goods-list-box data-v-eca9a26c"><view wx:for="{{g}}" wx:for-item="item" wx:key="e" class="left-list data-v-eca9a26c" style="{{j}}"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-eca9a26c" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-eca9a26c" style="{{i}}" slot="cart">{{h}}</button></s-goods-column></view></view><view class="goods-list-box data-v-eca9a26c"><view wx:for="{{k}}" wx:for-item="item" wx:key="e" class="right-list data-v-eca9a26c" style="{{n}}"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-eca9a26c" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-eca9a26c" style="{{m}}" slot="cart">{{l}}</button></s-goods-column></view></view></view><view wx:if="{{o}}" class="goods-lg-box data-v-eca9a26c"><view wx:for="{{p}}" wx:for-item="item" wx:key="d" class="goods-box data-v-eca9a26c" style="{{s}}"><s-goods-column wx:if="{{item.c}}" u-s="{{['cart']}}" class="goods-card data-v-eca9a26c" bindtap="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"><button class="ss-reset-button cart-btn data-v-eca9a26c" style="{{r}}" slot="cart">{{q}}</button></s-goods-column></view></view></view>