"use strict";const e=require("../../../common/vendor.js"),t=require("../../../sheep/api/trade/delivery.js"),o=require("../../../sheep/index.js");if(!Array){e.resolveComponent("s-layout")()}Math;const r={__name:"index",setup(r){const i=e.reactive({loaded:!1,loading:!1,storeList:[],system_store:{},locationShow:!1,user_latitude:0,user_longitude:0}),d=async()=>{if(i.loading||i.loaded)return;i.loading=!0;const{data:e,code:o}=await t.DeliveryApi.getDeliveryPickUpStoreList({latitude:i.user_latitude,longitude:i.user_longitude});0===o&&(i.loading=!1,i.storeList=e)};return e.onMounted((()=>{i.user_latitude&&i.user_longitude||e.index.getLocation({type:"gcj02",success:t=>{try{i.user_latitude=t.latitude,i.user_longitude=t.longitude,e.index.setStorageSync("user_latitude",t.latitude),e.index.setStorageSync("user_longitude",t.longitude)}catch(o){console.error(o)}d()},complete:()=>{d()}}),d()})),e.onLoad((()=>{try{i.user_latitude=e.index.getStorageSync("user_latitude"),i.user_longitude=e.index.getStorageSync("user_longitude")}catch(t){console.error(t)}})),(t,r)=>({a:e.f(i.storeList,((t,r,i)=>e.e({a:t.logo,b:e.t(t.name),c:e.t(t.areaName),d:e.t(", "+t.detailAddress),e:e.o((o=>{return r=t.phone,void e.index.makePhoneCall({phoneNumber:r});var r}),r),f:t.distance},t.distance?{g:e.t(t.distance.toFixed(2))}:{},{h:e.o((o=>{return r=t,void e.index.openLocation({latitude:Number(r.latitude),longitude:Number(r.longitude),name:r.name,address:`${r.areaName}-${r.detailAddress}`,success:function(){console.log("success")}});var r}),r),i:r,j:e.o((r=>{return i=t,e.index.$emit("SELECT_PICK_UP_INFO",{addressInfo:i}),void o.sheep.$router.back();var i}),r)}))),b:e.p({bgStyle:{color:"#FFF"},title:"选择自提门店"})})}},i=e._export_sfc(r,[["__scopeId","data-v-c040fc68"]]);wx.createPage(i);
