<s-layout wx:if="{{g}}" class="data-v-8a08a031" u-s="{{['d']}}" u-i="8a08a031-0" bind:__l="__l" u-p="{{g}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="j" class="goods-item ss-m-20 data-v-8a08a031"><s-goods-item wx:if="{{item.i}}" class="data-v-8a08a031" u-s="{{['rightBottom']}}" bindtap="{{item.g}}" u-i="{{item.h}}" bind:__l="__l" u-p="{{item.i}}"><view class="ss-flex ss-row-between data-v-8a08a031" slot="rightBottom"><view wx:if="{{item.a}}" class="commission-num data-v-8a08a031">预计佣金：计算中</view><view wx:elif="{{item.b}}" class="commission-num data-v-8a08a031"> 预计佣金：{{item.c}}</view><view wx:else class="commission-num data-v-8a08a031"> 预计佣金：{{item.d}} ~ {{item.e}}</view><button class="ss-reset-button share-btn ui-BG-Main-Gradient data-v-8a08a031" catchtap="{{item.f}}"> 分享赚 </button></view></s-goods-item></view><s-empty wx:if="{{b}}" class="data-v-8a08a031" u-i="8a08a031-2,8a08a031-0" bind:__l="__l" u-p="{{c}}"/><uni-load-more wx:if="{{d}}" class="data-v-8a08a031" bindtap="{{e}}" u-i="8a08a031-3,8a08a031-0" bind:__l="__l" u-p="{{f}}"/></s-layout>