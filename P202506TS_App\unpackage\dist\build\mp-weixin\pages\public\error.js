"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js");if(!Array){e.resolveComponent("s-empty")()}Math;const o={__name:"error",setup(o){const r=e.ref(""),n=e.ref("");async function a(){e.index.reLaunch({url:"/pages/index/index"}),await t.ShoproInit()}return e.onLoad((e=>{r.value=e.errCode,n.value=e.errMsg})),(t,o)=>e.e({a:"NetworkError"===r.value},"NetworkError"===r.value?{b:e.o(a),c:e.p({icon:"/static/internet-empty.png",text:"网络连接失败",showAction:!0,actionText:"重新连接",buttonColor:"#ff3000"})}:"TemplateError"===r.value?{e:e.o(a),f:e.p({icon:"/static/internet-empty.png",text:"未找到模板",showAction:!0,actionText:"重新加载",buttonColor:"#ff3000"})}:""!==r.value?{h:e.o(a),i:e.p({icon:"/static/internet-empty.png",text:n.value,showAction:!0,actionText:"重新加载",buttonColor:"#ff3000"})}:{},{d:"TemplateError"===r.value,g:""!==r.value})}},r=e._export_sfc(o,[["__scopeId","data-v-88db9efc"]]);wx.createPage(r);
