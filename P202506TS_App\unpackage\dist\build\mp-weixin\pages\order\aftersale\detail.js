"use strict";const e=require("../../../common/vendor.js"),t=require("../../../sheep/index.js"),i=require("../../../sheep/hooks/useGoods.js"),o=require("../../../sheep/api/trade/afterSale.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("s-empty")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const n={__name:"detail",setup(n){e.useCssVars((t=>({31660596:e.unref(r)})));const s=2*t.sheep.$platform.device.statusBarHeight,r=t.sheep.$url.css("/static/img/shop/order/order_bg.png"),a=e.reactive({id:0,info:{},loading:!1,active:0,list:[{title:"提交申请"},{title:"处理中"},{title:"完成"}]});const l=()=>{t.sheep.$helper.copyText(a.info.no)};async function u(e){a.loading=!0;const{code:t,data:n}=await o.AfterSaleApi.getAfterSale(e);0===t?(a.info=n,i.handleAfterSaleButtons(a.info),[10].includes(a.info.status)?a.active=0:[20,30].includes(a.info.status)?a.active=1:([40,50].includes(a.info.status)||[61,62,63].includes(a.info.status))&&(a.active=2)):a.info=null}return e.onLoad((e=>{e.id?(a.id=e.id,u(e.id)):t.sheep.$helper.toast("缺少订单信息，请检查")})),(n,r)=>{var f,p,d,c;return e.e({a:!e.unref(e.isEmpty)(a.info)&&a.loading},!e.unref(e.isEmpty)(a.info)&&a.loading?{b:e.f(a.list,((t,i,o)=>e.e({a:a.list.length-1===i&&[61,62,63].includes(a.info.status)},a.list.length-1===i&&[61,62,63].includes(a.info.status)?{}:{b:e.n(a.active>=i?"activity-color":"info-color")},{c:a.list.length-1!==i},a.list.length-1!==i?{d:e.n(a.active>=i?"activity-bg":"info-bg")}:{},{e:e.t(t.title),f:e.n(a.active>=i?"activity-color":"info-color"),g:i}))),c:e.s({marginTop:"-"+Number(s+88)+"rpx",paddingTop:Number(s+88)+"rpx"}),d:e.t(e.unref(i.formatAfterSaleStatusDescription)(a.info)),e:e.t(e.unref(t.sheep).$helper.timeFormat(a.info.updateTime,"yyyy-mm-dd hh:MM:ss")),f:e.o((i=>e.unref(t.sheep).$router.go("/pages/order/aftersale/log",{id:a.id}))),g:e.t(e.unref(i.fen2yuan)(a.info.refundPrice)),h:e.p({img:a.info.picUrl,title:a.info.spuName,titleWidth:480,skuText:a.info.properties.map((e=>e.valueName)).join(" "),num:a.info.count}),i:e.t(a.info.no),j:e.o(l),k:e.t(e.unref(t.sheep).$helper.timeFormat(a.info.createTime,"yyyy-mm-dd hh:MM:ss")),l:e.t(10===a.info.way?"仅退款":"退款退货"),m:e.t(a.info.applyReason),n:e.t(a.info.applyDescription)}:{},{o:e.unref(e.isEmpty)(a.info)&&a.loading},e.unref(e.isEmpty)(a.info)&&a.loading?{p:e.p({icon:e.unref(t.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无该订单售后详情"})}:{},{q:!e.unref(e.isEmpty)(a.info)},e.unref(e.isEmpty)(a.info)?{}:e.e({r:null==(f=a.info.buttons)?void 0:f.includes("cancel")},(null==(p=a.info.buttons)?void 0:p.includes("cancel"))?{s:e.o((t=>{return i=a.info.id,void e.index.showModal({title:"提示",content:"确定要取消此申请吗？",success:async function(e){if(!e.confirm)return;const{code:t}=await o.AfterSaleApi.cancelAfterSale(i);0===t&&await u(i)}});var i}))}:{},{t:null==(d=a.info.buttons)?void 0:d.includes("delivery")},(null==(c=a.info.buttons)?void 0:c.includes("delivery"))?{v:e.o((i=>e.unref(t.sheep).$router.go("/pages/order/aftersale/return-delivery",{id:a.info.id})))}:{},{w:e.o((i=>e.unref(t.sheep).$router.go("/pages/chat/index"))),x:e.p({bottom:!0,placeholder:!0,bg:"bg-white"})}),{y:e.s(n.__cssVars()),z:e.p({title:"售后详情",navbar:!e.unref(e.isEmpty)(a.info)&&a.loading?"inner":"normal"})})}}},s=e._export_sfc(n,[["__scopeId","data-v-6a8ea5bc"]]);wx.createPage(s);
