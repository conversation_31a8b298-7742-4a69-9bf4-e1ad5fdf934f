<view class="uni-file-picker__container"><view wx:for="{{a}}" wx:for-item="url" wx:key="d" class="file-picker__box" style="{{d}}"><view class="file-picker__box-content" style="{{c}}"><image class="file-image" src="{{url.a}}" mode="aspectFill" catchtap="{{url.b}}"></image><view wx:if="{{b}}" class="icon-del-box" catchtap="{{url.c}}"><view class="icon-del"></view><view class="icon-del rotate"></view></view></view></view><view wx:if="{{e}}" class="file-picker__box" style="{{h}}"><view class="file-picker__box-content is-add" style="{{f}}" bindtap="{{g}}"><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else><view class="icon-add"></view><view class="icon-add rotate"></view></block></view></view></view>