"use strict";const e=require("../../request/index.js"),r={getEnterprisePartnerThirdPage:r=>e.request({url:"/member/enterprise-partner-third/page",method:"GET",params:r}),getSignatoryThirdPage:r=>e.request({url:"/member/enterprise-partner-third/signatory-page",method:"GET",params:r}),getPageByContactId:r=>e.request({url:"/member/enterprise-partner-third/page-by-contact-id",method:"GET",params:r}),getEnterprisePartnerThird:r=>e.request({url:"/member/enterprise-partner-third/get?id="+r,method:"GET"}),createEnterprisePartnerThird:r=>e.request({url:"/member/enterprise-partner-third/create",method:"POST",data:r}),updateEnterprisePartnerThird:r=>e.request({url:"/member/enterprise-partner-third/update",method:"PUT",data:r}),updateEnterprisePartnerThirdStatus:(r,t)=>e.request({url:"/member/enterprise-partner-third/update-status",method:"PUT",params:{id:r,status:t}}),deleteEnterprisePartnerThird:r=>e.request({url:"/member/enterprise-partner-third/delete?id="+r,method:"DELETE"}),exportEnterprisePartnerThird:r=>e.request({url:"/member/enterprise-partner-third/export-excel",method:"GET",params:r})};exports.EnterprisePartnerThirdApi=r;
