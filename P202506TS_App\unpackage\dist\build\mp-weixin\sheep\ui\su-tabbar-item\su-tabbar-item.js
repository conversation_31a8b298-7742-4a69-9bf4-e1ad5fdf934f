"use strict";const t=require("../../helper/index.js"),e=require("../../../common/vendor.js"),a={name:"su-tabbar-item",props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"},name:{type:[String,Number,null],default:""},icon:{icon:String,default:""},badge:{type:[String,Number,null],default:""},dot:{type:Boolean,default:!1},text:{type:String,default:""},badgeStyle:{type:[Object,String],default:""},isCenter:{type:Boolean,default:!1},centerImage:{type:String,default:""}},data:()=>({isActive:!1,addStyle:t.addStyle,parentData:{value:null,activeColor:"",inactiveColor:""},parent:{}}),created(){this.init()},methods:{getParentData(e=""){this.parent||(this.parent={}),this.parent=t.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((t=>{this.parentData[t]=this.parent[t]}))},init(){this.updateParentData(),this.parent||console.log("u-tabbar-item必须搭配u-tabbar组件使用");const t=this.parent.children.indexOf(this);this.isActive=(this.name.split("?")[0]||t)===this.parentData.value},updateParentData(){this.getParentData("su-tabbar")},updateFromParent(){this.init()},clickHandler(){this.$nextTick((()=>{const t=this.parent.children.indexOf(this),e=this.name||t;e!==this.parent.value&&this.parent.$emit("change",e),this.$emit("click",e)}))}}};const i=e._export_sfc(a,[["render",function(t,a,i,n,r,s){return e.e({a:i.isCenter},i.isCenter?{b:i.centerImage}:e.e({c:i.icon},i.icon?{d:i.icon,e:r.isActive?r.parentData.activeColor:r.parentData.inactiveColor}:e.e({f:r.isActive},(r.isActive,{})),{g:e.t(i.text),h:r.isActive?r.parentData.activeColor:r.parentData.inactiveColor}),{i:e.s(r.addStyle(i.customStyle))})}],["__scopeId","data-v-a8e52301"]]);wx.createComponent(i);
