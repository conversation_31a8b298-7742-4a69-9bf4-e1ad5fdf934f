"use strict";const t=require("../../common/vendor.js"),e=require("../url/index.js"),s=require("../util/index.js");function u(t,e,s){if(s=s||0,"exact"===e)return t+s;if(s<10)return`${t}≤10`;const u=s.toString();return`${t}${u[0]}${"0".repeat(u.length-1)}+`}const n=[".avi",".mp4"];function o(t){return(t/100).toFixed(2)}function r(t){return o(t).replace(/\.?0+$/,"")}exports.appendSettlementProduct=function(t,e){if(e&&0!==e.length)for(const s of t){const t=e.find((t=>t.spuId===s.id));if(!t)return;const u=t.skus.filter((t=>t.promotionPrice>0)).reduce(((t,e)=>t.promotionPrice<e.promotionPrice?t:e),[]);u&&(s.promotionType=u.promotionType,s.promotionPrice=u.promotionPrice),t.rewardActivity&&(s.rewardActivity=t.rewardActivity)}},exports.convertProductPropertyList=function(t){let e=[];for(const s of t)if(s.properties)for(const t of s.properties){let s=e.find((e=>e.id===t.propertyId));s||(s={id:t.propertyId,name:t.propertyName,values:[]},e.push(s)),s.values.find((e=>e.id===t.valueId))||s.values.push({id:t.valueId,name:t.valueName})}return e},exports.fen2yuan=o,exports.fen2yuanSimple=r,exports.formatAfterSaleStatus=function(t){return 10===t.status?"申请售后":20===t.status?"商品待退货":30===t.status?"商家待收货":40===t.status?"等待退款":50===t.status?"退款成功":61===t.status?"买家取消":62===t.status?"商家拒绝":63===t.status?"商家拒收货":"未知状态"},exports.formatAfterSaleStatusDescription=function(t){return 10===t.status?"退款申请待商家处理":20===t.status?"请退货并填写物流信息":30===t.status?"退货退款申请待商家处理":40===t.status?"等待退款":50===t.status?"退款成功":61===t.status?"退款关闭":62===t.status?`商家不同意退款申请，拒绝原因：${t.auditReason}`:63===t.status?`商家拒绝收货，不同意退款，拒绝原因：${t.auditReason}`:"未知状态"},exports.formatDiscountPercent=function(t){return(t/10).toFixed(1).replace(/\.?0+$/,"")},exports.formatExchange=function(t,e){return u("已兑换",t,e)},exports.formatGoodsSwiper=function(t){return(null==t?void 0:t.filter((t=>t)).map(((t,s)=>({type:n.some((e=>t.includes(e)))?"video":"image",src:e.$url.cdn(t)}))))||[]},exports.formatOrderColor=function(t){return 0===t.status?"info-color":10===t.status||20===t.status||30===t.status&&!t.commentStatus?"warning-color":30===t.status&&t.commentStatus?"success-color":"danger-color"},exports.formatOrderStatus=function(t){return 0===t.status?"待付款":10===t.status&&1===t.deliveryType?"待发货":10===t.status&&2===t.deliveryType?"":20===t.status?"待收货":30!==t.status||t.commentStatus?30===t.status&&t.commentStatus?"已完成":40===t.status?"已取消":50===t.status?"已开票":"已关闭":"待评价"},exports.formatOrderStatusDescription=function(t){return 0===t.status?`请在 ${s.formatDate(t.payExpireTime)} 前完成支付`:10===t.status?"商家未发货，请耐心等待":20===t.status?"商家已发货，请耐心等待":30!==t.status||t.commentStatus?30===t.status&&t.commentStatus?"交易完成，感谢您的支持":40===t.status?"订单已取消":50===t.status?"发票已开具，交易完成":"交易关闭":"已收货，快去评价一下吧"},exports.formatSales=function(t,e){return u("exact"!==t&&e<10?"销量":"已售",t,e)},exports.formatStock=function(t,e){return u("库存",t,e)},exports.getRewardActivityRuleGroupDescriptions=function(t){if(!t||!t.rules||0===t.rules.length)return[];const e=[{name:"满减",values:[]},{name:"赠品",values:[]},{name:"包邮",values:[]}];return t.rules.forEach((s=>{const u=10===t.conditionType?`满 ${r(s.limit)} 元`:`满 ${s.limit} 件`;if(s.limit&&e[0].values.push(`${u} 减 ${r(s.discountPrice)} 元`),s.point||s.giveCouponTemplateCounts&&s.giveCouponTemplateCounts.length>0){let t=[];s.point&&t.push(`送 ${s.point} 积分`),s.giveCouponTemplateCounts&&s.giveCouponTemplateCounts.length>0&&t.push(`送 ${s.giveCouponTemplateCounts.length} 张优惠券`),e[1].values.push(`${u} ${t.join("、")}`)}s.freeDelivery&&e[2].values.push(`${u} 包邮`)})),e.forEach((t=>{0===t.values.length&&e.splice(e.indexOf(t),1)})),e},exports.getRewardActivityRuleItemDescriptions=function(t){if(!t||!t.rules||0===t.rules.length)return[];const e=[];return t.rules.forEach((s=>{const u=10===t.conditionType?`满${r(s.limit)}元`:`满${s.limit}件`;s.limit&&e.push(`${u}减${r(s.discountPrice)}元`),s.point&&e.push(`${u}送${s.point}积分`),s.giveCouponTemplateCounts&&s.giveCouponTemplateCounts.length>0&&e.push(`${u}送${s.giveCouponTemplateCounts.length}张优惠券`),s.freeDelivery&&e.push(`${u}包邮`)})),e},exports.handleAfterSaleButtons=function(t){t.buttons=[],[10,20,30].includes(t.status)&&t.buttons.push("cancel"),20===t.status&&t.buttons.push("delivery")},exports.handleOrderButtons=function(t){t.buttons=[],3===t.type&&t.buttons.push("combination"),20===t.status&&t.buttons.push("confirm"),t.logisticsId>0&&t.buttons.push("express"),0===t.status&&(t.buttons.push("cancel"),t.buttons.push("pay")),30!==t.status||t.commentStatus||t.buttons.push("comment"),40===t.status&&t.buttons.push("delete"),(!0===t.payStatus||t.status>=10&&40!==t.status)&&(t.buttons.push("viewInvoice"),50!==t.status&&t.buttons.push("applyInvoice"))},exports.useDurationTime=function(e,s=""){e=function(e){if((e=e.toString()).indexOf("-")>0)return t.dayjs(e);if(e.length>10)return t.dayjs(parseInt(e));if(10===e.length)return t.dayjs.unix(parseInt(e))}(e),""===s&&(s=t.dayjs());let u=t.ref(e-s);u.value>0&&setTimeout((()=>{u.value>0&&(u.value-=1e3)}),1e3);let n=t.dayjs.duration(u.value);return{h:(30*n.months()*24+24*n.days()+n.hours()).toString().padStart(2,"0"),m:n.minutes().toString().padStart(2,"0"),s:n.seconds().toString().padStart(2,"0"),ms:n.$ms}};
