<view wx:if="{{a}}" class="uni-noticebar data-v-02f0aeb6" style="{{'background-color:' + E}}" bindtap="{{F}}"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><uni-icons wx:if="{{b}}" class="uni-noticebar-icon data-v-02f0aeb6" u-i="02f0aeb6-0" bind:__l="__l" u-p="{{c}}"/></block><view ref="textBox" class="{{['uni-noticebar__content-wrapper', 'data-v-02f0aeb6', s && 'uni-noticebar__content-wrapper--scrollable', t && 'uni-noticebar__content-wrapper--single']}}"><view id="{{p}}" class="{{['uni-noticebar__content', 'data-v-02f0aeb6', q && 'uni-noticebar__content--scrollable', r && 'uni-noticebar__content--single']}}"><text id="{{e}}" ref="animationEle" class="{{['uni-noticebar__content-text', 'data-v-02f0aeb6', f && 'uni-noticebar__content-text--scrollable', g && 'uni-noticebar__content-text--single']}}" style="{{'color:' + h + ';' + ('width:' + i) + ';' + ('animation-duration:' + j) + ';' + ('-webkit-animation-duration:' + k) + ';' + ('animation-play-state:' + l) + ';' + ('-webkit-animation-play-state:' + m) + ';' + ('animation-delay:' + n) + ';' + ('-webkit-animation-delay:' + o)}}">{{d}}</text></view></view><view wx:if="{{v}}" class="uni-noticebar__more uni-cursor-point data-v-02f0aeb6" bindtap="{{A}}"><text wx:if="{{w}}" style="{{'color:' + y}}" class="uni-noticebar__more-text data-v-02f0aeb6">{{x}}</text><uni-icons wx:else class="data-v-02f0aeb6" u-i="02f0aeb6-1" bind:__l="__l" u-p="{{z||''}}"/></view><view wx:if="{{B}}" class="uni-noticebar-close uni-cursor-point data-v-02f0aeb6"><view class="data-v-02f0aeb6" bindtap="{{D}}"><block wx:if="{{$slots.close}}"><slot name="close"></slot></block><block wx:else><uni-icons wx:if="{{C}}" class="data-v-02f0aeb6" u-i="02f0aeb6-2" bind:__l="__l" u-p="{{C}}"/></block></view></view></view>