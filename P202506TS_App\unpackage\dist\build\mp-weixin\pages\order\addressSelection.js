"use strict";const e=require("../../common/vendor.js"),a=require("../../sheep/index.js"),s={__name:"addressSelection",props:{modelValue:{type:Object,default(){}}},emits:["update:modelValue"],setup(s,{emit:l}){const d=s,o=l,t=e.computed({get:()=>new Proxy(d.modelValue,{set:(e,a,s)=>(o("update:modelValue",{...e,[a]:s}),!0)}),set(e){o("update:modelValue",e)}});function u(){let s="SELECT_ADDRESS",l="/pages/user/address/list?type=select";2===t.value.deliveryType&&(s="SELECT_PICK_UP_INFO",l="/pages/user/goods_details_store/index"),e.index.$once(s,(a=>{!async function(a={}){e.isEmpty(a)||(1===t.value.deliveryType&&(t.value.addressInfo=a),2===t.value.deliveryType&&(t.value.pickUpInfo=a))}(a.addressInfo)})),a.sheep.$router.go(l)}const p=e=>{t.value.deliveryType=e};return(s,l)=>e.e({a:t.value.isPickUp},t.value.isPickUp?{b:e.n(1===t.value.deliveryType?"on":"on2"),c:e.o((e=>p(1)))}:{},{d:t.value.isPickUp},t.value.isPickUp?{e:e.n(2===t.value.deliveryType?"on":"on2"),f:e.o((e=>p(2)))}:{},{g:1===t.value.deliveryType},1===t.value.deliveryType?e.e({h:t.value.addressInfo.name},t.value.addressInfo.name?e.e({i:e.t(t.value.addressInfo.name),j:e.t(t.value.addressInfo.mobile),k:t.value.addressInfo.defaultStatus},(t.value.addressInfo.defaultStatus,{}),{l:e.t(t.value.addressInfo.areaName),m:e.t(t.value.addressInfo.detailAddress)}):{},{n:e.o(u),o:e.s(t.value.isPickUp?"":"border-top-left-radius: 14rpx;border-top-right-radius: 14rpx;")}):{},{p:2===t.value.deliveryType},2===t.value.deliveryType?e.e({q:t.value.pickUpInfo.name},t.value.pickUpInfo.name?{r:e.t(t.value.pickUpInfo.name),s:e.t(t.value.pickUpInfo.phone),t:e.t(t.value.pickUpInfo.areaName),v:e.t(", "+t.value.pickUpInfo.detailAddress)}:{},{w:e.o(u)}):{},{x:e.unref(a.sheep).$url.static("/static/images/line.png","local"),y:e.s(t.value.isPickUp?"":"padding-top:10rpx;")})}},l=e._export_sfc(s,[["__scopeId","data-v-9f2ad794"]]);wx.createComponent(l);
