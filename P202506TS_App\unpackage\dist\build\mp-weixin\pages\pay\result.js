"use strict";const e=require("../../common/vendor.js"),r=require("../../sheep/index.js"),s=require("../../sheep/hooks/useModal.js"),t=require("../../sheep/api/pay/order.js"),a=require("../../sheep/hooks/useGoods.js"),o=require("../../sheep/api/trade/order.js"),u=require("../../sheep/util/const.js"),d={__name:"result",setup(d){const i=e.reactive({id:0,orderType:"goods",result:"unpaid",orderInfo:{},tradeOrder:{},counter:0}),n=e.computed((()=>"unpaid"===i.result?"waiting":"paid"===i.result?"success":"failed"===i.result?"failed":"closed"===i.result?"closed":void 0));async function c(s){i.counter++;const{data:a,code:d}=await t.PayOrderApi.getOrder(s);if(0===d){if(i.orderInfo=a,!i.orderInfo||30===i.orderInfo.status)return void(i.result="closed");if(0!==i.orderInfo.status){if(i.result="paid",e.index.showModal({title:"支付结果",showCancel:!1,content:"支付成功",success:()=>{!async function(){if(!e.index.getStorageSync("subscribe_btn_status"))return void(l.value=!0);!function(){if("goods"!==i.orderType)return;const s=[u.WxaSubscribeTemplate.TRADE_ORDER_DELIVERY];3===i.tradeOrder.type&&s.push(u.WxaSubscribeTemplate.PROMOTION_COMBINATION_SUCCESS);r.sheep.$platform.useProvider("wechat").subscribeMessage(s,(()=>{e.index.removeStorageSync("subscribe_btn_status"),e.index.setStorageSync("subscribe_btn_status","已订阅"),l.value=!1}))}()}()}}),"goods"===i.orderType){const{data:e,code:r}=await o.OrderApi.getOrderDetail(i.orderInfo.merchantOrderId,!0);0===r&&(i.tradeOrder=e)}return}}i.counter<3&&"unpaid"===i.result&&setTimeout((()=>{c(s)}),1500),i.counter>=3&&(i.result="failed")}const l=e.ref(!1);return e.onLoad((async e=>{s.closeAuthModal(),e.id&&(i.id=e.id),e.orderType&&(i.orderType=e.orderType),"fail"===e.payState?i.result="failed":await c(i.id)})),e.onShow((()=>{e.isEmpty(i.orderInfo)||c(i.id)})),e.onHide((()=>{i.result="unpaid",i.counter=0})),e.onUnload((()=>{e.index.reLaunch({url:"/pages/index/register"})})),(s,t)=>e.e({a:"waiting"===n.value},(n.value,{}),{b:"success"===n.value},"success"===n.value?{c:e.unref(r.sheep).$url.static("/static/img/shop/order/order_pay_success.gif")}:{},{d:["failed","closed"].includes(n.value)},["failed","closed"].includes(n.value)?{e:e.unref(r.sheep).$url.static("/static/img/shop/order/order_paty_fail.gif")}:{},{f:"success"===n.value},(n.value,{}),{g:"failed"===n.value},(n.value,{}),{h:"closed"===n.value},(n.value,{}),{i:"waiting"===n.value},(n.value,{}),{j:"success"===n.value},"success"===n.value?{k:e.t(e.unref(a.fen2yuan)(i.orderInfo.price))}:{},{l:e.o((s=>e.unref(r.sheep).$router.go("/pages/index/register",{payState:"success"}))),m:"failed"===n.value},"failed"===n.value?{n:e.o((s=>e.unref(r.sheep).$router.redirect("/pages/index/register")))}:{})}},i=e._export_sfc(d,[["__scopeId","data-v-10e525c5"]]);wx.createPage(i);
