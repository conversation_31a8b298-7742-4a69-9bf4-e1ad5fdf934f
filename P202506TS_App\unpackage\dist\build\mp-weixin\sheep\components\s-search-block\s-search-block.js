"use strict";const e=require("../../../common/vendor.js"),a=require("../../index.js");if(!Array){e.resolveComponent("uni-search-bar")()}Math;const t={__name:"s-search-block",props:{data:{type:Object,default:()=>({})},elBackground:{type:String,default:""},height:{type:Number,default:36},iconColor:{type:String,default:"#b0b3bf"},fontColor:{type:String,default:"#b0b3bf"},placeholder:{type:String,default:"这是一个搜索框"},radius:{type:Number,default:10},width:{type:String,default:"100%"},navbar:{type:Boolean,default:!0}},emits:["click"],setup(t,{emit:o}){const r=e.reactive({searchVal:""}),d=o,n=t,l=()=>{d("click")};function s(e){e.value&&(a.sheep.$router.go("/pages/goods/list",{keyword:e.value}),setTimeout((()=>{r.searchVal=""}),100))}return(o,d)=>e.e({a:t.navbar},t.navbar?{b:e.s({color:n.iconColor}),c:e.t(t.placeholder),d:e.s({color:t.fontColor,width:t.width})}:{},{e:!t.navbar},t.navbar?{}:{f:e.o(s),g:e.o((e=>r.searchVal=e)),h:e.p({radius:t.data.borderRadius,placeholder:t.data.placeholder,cancelButton:"none",clearButton:"none",modelValue:r.searchVal})},{i:e.f(t.data.hotKeywords,((t,o,r)=>({a:e.t(t),b:e.o((o=>e.unref(a.sheep).$router.go("/pages/goods/list",{keyword:t})),o),c:o}))),j:e.s({color:t.data.textColor}),k:t.data.hotKeywords&&t.data.hotKeywords.length&&t.navbar},t.data.hotKeywords&&t.data.hotKeywords.length&&t.navbar?{l:e.f(t.data.hotKeywords,((a,t,o)=>({a:e.t(a),b:t}))),m:e.s({color:t.data.textColor,marginRight:"10rpx"})}:{},{n:e.o(l),o:e.s({borderRadius:t.radius+"px",background:t.elBackground,height:t.height+"px",width:t.width}),p:e.n({"border-content":t.navbar})})}},o=e._export_sfc(t,[["__scopeId","data-v-59498a77"]]);wx.createComponent(o);
