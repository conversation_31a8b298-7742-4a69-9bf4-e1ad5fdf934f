<s-layout wx:if="{{W}}" class="data-v-12e40b1c" u-s="{{['d']}}" style="{{V}}" u-i="12e40b1c-0" bind:__l="__l" u-p="{{W}}"><view class="page-bg data-v-12e40b1c" style="{{a}}"/><view class="ai-content data-v-12e40b1c" style="{{'height:' + U + ';' + ('overflow:' + 'hidden')}}"><conversation-list wx:if="{{g}}" class="r data-v-12e40b1c" data-c-h="{{!false}}" u-r="conversationListRef" bindonConversationCreate="{{c}}" bindonConversationClick="{{d}}" bindonConversationClear="{{e}}" bindonConversationDelete="{{f}}" u-i="12e40b1c-1,12e40b1c-0" bind:__l="__l" u-p="{{g}}"/><view class="detail-container ss-flex ss-flex-col data-v-12e40b1c"><view wx:if="{{false}}" class="header data-v-12e40b1c"><view class="title data-v-12e40b1c">{{h}} <label wx:if="{{i}}" class="data-v-12e40b1c">({{j}})</label></view><view wx:if="{{k}}" class="btns data-v-12e40b1c"><button class="data-v-12e40b1c" type="primary" bg plain size="small" bindtap="{{n}}"><label class="data-v-12e40b1c"><rich-text class="data-v-12e40b1c" nodes="{{l}}"/></label><icon wx:if="{{m}}" class="ml-10px data-v-12e40b1c" u-i="12e40b1c-2,12e40b1c-0" bind:__l="__l" u-p="{{m}}"/></button><button size="small" class="btn data-v-12e40b1c" bindtap="{{p}}"><icon wx:if="{{o}}" class="data-v-12e40b1c" u-i="12e40b1c-3,12e40b1c-0" bind:__l="__l" u-p="{{o}}"/></button><button size="small" class="btn data-v-12e40b1c"><icon wx:if="{{q}}" class="data-v-12e40b1c" u-i="12e40b1c-4,12e40b1c-0" bind:__l="__l" u-p="{{q}}"/></button><button size="small" class="btn data-v-12e40b1c" bindtap="{{s}}"><icon wx:if="{{r}}" class="data-v-12e40b1c" u-i="12e40b1c-5,12e40b1c-0" bind:__l="__l" u-p="{{r}}"/></button></view></view><view class="main-container ss-flex-1 data-v-12e40b1c"><view class="data-v-12e40b1c"><view class="message-container data-v-12e40b1c"><message-loading wx:if="{{t}}" class="data-v-12e40b1c" u-i="12e40b1c-6,12e40b1c-0" bind:__l="__l"/><message-new-conversation wx:if="{{v}}" class="data-v-12e40b1c" bindonNewConversation="{{w}}" u-i="12e40b1c-7,12e40b1c-0" bind:__l="__l"/><message-list-empty wx:if="{{x}}" class="data-v-12e40b1c" bindonPrompt="{{y}}" u-i="12e40b1c-8,12e40b1c-0" bind:__l="__l"/><message-list wx:if="{{z}}" class="ss-flex-1 r data-v-12e40b1c" u-r="messageRef" bindonDeleteSuccess="{{B}}" bindonEdit="{{C}}" bindonRefresh="{{D}}" u-i="12e40b1c-9,12e40b1c-0" bind:__l="__l" u-p="{{E}}"></message-list></view></view></view><view class="footer-container data-v-12e40b1c" safe-area-inset-bottom="{{false}}"><view class="prompt-from data-v-12e40b1c"><block wx:if="{{r0}}"><textarea class="prompt-input data-v-12e40b1c" bindkeydown="{{F}}" bindinput="{{G}}" bindcompositionstart="{{H}}" bindcompositionend="{{I}}" placeholder="请尽可能详细的描述您的合同需求..." cursor-spacing="20" value="{{J}}"></textarea></block><view class="prompt-btns data-v-12e40b1c"><view class="data-v-12e40b1c"><su-switch wx:if="{{L}}" class="data-v-12e40b1c" data-c-h="{{!false}}" style="transform:scale(0.8)" u-i="12e40b1c-10,12e40b1c-0" bind:__l="__l" bindupdateModelValue="{{K}}" u-p="{{L}}"/><label hidden="{{!false}}" class="ml-5px text-14px text-#8f8f8f data-v-12e40b1c">上下文</label></view><button wx:if="{{M}}" class="data-v-12e40b1c" type="primary" size="default" bindtap="{{O}}" loading="{{P}}">{{N}}</button><button wx:if="{{Q}}" class="data-v-12e40b1c" type="danger" size="default" bindtap="{{R}}"> 停止 </button></view></view><view class="ss-w-100 data-v-12e40b1c" style="height:120rpx"></view></view></view><conversation-update-form class="r data-v-12e40b1c" u-r="conversationUpdateFormRef" bindsuccess="{{T}}" u-i="12e40b1c-11,12e40b1c-0" bind:__l="__l"/></view></s-layout>