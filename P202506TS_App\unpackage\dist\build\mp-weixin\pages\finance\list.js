"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),o=require("../../sheep/util/index.js"),n=require("../../sheep/api/member/reportCwfx.js");if(!Array){(e.resolveComponent("uni-list-item")+e.resolveComponent("uni-list")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-empty")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const i={__name:"list",setup(i){const a=e.reactive({pagination:{list:[],total:0,pageNo:1,pageSize:6},loadStatus:""});function s(e){a.keyword=e,o.resetPagination(a.pagination),r()}async function r(){a.loadStatus="loading";const t=e.index.getStorageSync("enterprise"),{code:o,data:i}=await n.ReportCwfxApi.getReportCwfxPage({pageNo:a.pagination.pageNo,pageSize:a.pagination.pageSize,enterpriseId:t.id,keyword:a.keyword});0===o&&(a.pagination.list=e.lodash.concat(a.pagination.list,i.list),a.pagination.total=i.total,a.loadStatus=a.pagination.list.length<a.pagination.total?"more":"noMore")}function p(){"noMore"!==a.loadStatus&&(a.pagination.pageNo++,r())}return e.onLoad((e=>{r()})),e.onReachBottom((()=>{p()})),e.onPullDownRefresh((()=>{r(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),(i,r)=>e.e({a:a.pagination.total>0},a.pagination.total>0?{b:e.f(a.pagination.list,((i,a,r)=>e.e({a:e.t(i.status),b:e.n("执行成功"===i.status?"orange":"gray"),c:e.t(i.companyName),d:e.t(i.periodTime),e:i.orderError},i.orderError?{f:e.t(i.orderError)}:{},{g:e.t(e.unref(o.formatDate)(i.createTime,"YYYY.MM.DD")),h:e.o((e=>(async e=>{t.sheep.$router.go("/pages/finance/detail",{id:e})})(i.id)),a),i:i.fileUrl},i.fileUrl?{j:e.o((t=>(async t=>{e.index.showLoading({title:"加载中",mask:!0,fail:()=>{e.index.hideLoading()}}),e.index.request({url:t,method:"GET",dataType:"arraybuffer",header:{Accept:"text/json","Content-Type":"application/json;charset=UTF-8"},responseType:"arraybuffer"}).then((t=>{const o=e.index.getFileSystemManager(),n=t.header["Content-Disposition"]?decodeURIComponent(t.header["Content-Disposition"].match(/filename="?(.+)"?/)[1]):"稿件.pdf";o.writeFile({filePath:e.wx$1.env.USER_DATA_PATH+"/"+n,data:t.data,encoding:"binary",success(t){e.index.openDocument({filePath:e.wx$1.env.USER_DATA_PATH+"/"+n,showMenu:!0,success:t=>{e.index.hideLoading(),e.index.showToast({title:"文件获取成功，请通过右上角菜单进行分享保存",icon:"none"})},fail:t=>{e.index.hideLoading(),e.index.showToast({title:"打开文件失败",icon:"none"})}})},fail(t){e.index.hideLoading(),e.index.showToast({title:"写文件失败",icon:"none"})}})})).catch((()=>{e.index.hideLoading(),e.index.showToast({title:"文件获取失败",icon:"none"})}))})(i.fileUrl)),a)}:i.reportId?{l:e.o((o=>(async o=>{const i=e.index.getStorageSync("enterprise"),{code:a}=await n.ReportCwfxApi.getFileUrl(i.id,o);0===a&&(t.sheep.$helper.toast("下载成功"),await s())})(i.id)),a)}:{},{k:i.reportId,m:a,n:"64e79162-2-"+r+",64e79162-1"})))}:{},{c:a.pagination.total>0},a.pagination.total>0?{d:e.o(p),e:e.p({status:a.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{f:0===a.pagination.total},0===a.pagination.total?{g:e.p({icon:e.unref(t.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无稿件"})}:{},{h:e.o((o=>e.unref(t.sheep).$router.go("/pages/finance/create"))),i:e.p({title:"财务自动检测"})})}},a=e._export_sfc(i,[["__scopeId","data-v-64e79162"]]);wx.createPage(a);
