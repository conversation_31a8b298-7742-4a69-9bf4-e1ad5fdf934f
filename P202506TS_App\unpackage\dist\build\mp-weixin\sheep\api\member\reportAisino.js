"use strict";const e=require("../../request/index.js"),t={getReportAisinoPage:t=>e.request({url:"/member/report-aisino/page",method:"GET",params:t}),getStatus:t=>e.request({url:"/member/report-aisino/get-status?enterpriseId="+t,method:"GET"}),updateStatus:t=>e.request({url:"/member/report-aisino/update-status",method:"PUT",data:t,custom:{loadingMsg:"开通中",showSuccess:!0,successMsg:"开通成功"}})};exports.ReportAisinoApi=t;
