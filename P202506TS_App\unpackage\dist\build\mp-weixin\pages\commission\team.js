"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),r=require("../../sheep/index.js"),t=require("../../sheep/api/trade/brokerage.js");if(!Array){e.resolveComponent("s-layout")()}Math;const a={__name:"team",setup(a){e.useCssVars((o=>({"4b9e1986":e.unref(s)})));const n=2*r.sheep.$platform.device.statusBarHeight;e.computed((()=>r.sheep.$store("user").userInfo));const s=r.sheep.$url.css("/static/img/shop/user/withdraw_bg.png");e.onPageScroll((e=>{u.scrollTop=e.scrollTop<=100}));let i=e.ref();const u=e.reactive({summary:{},pagination:{pageNo:1,pageSize:8,list:[],total:0},loadStatus:"",level:1,nickname:e.ref(""),sortKey:"",isAsc:""});function p(){u.pagination.list=[],c()}async function c(){u.loadStatus="loading";let{code:o,data:r}=await t.BrokerageApi.getBrokerageUserChildSummaryPage({pageNo:u.pagination.pageNo,pageSize:u.pagination.pageSize,level:u.level,"sortingField.order":u.isAsc,"sortingField.field":u.sortKey,nickname:u.nickname});0===o&&(u.pagination.list=e.lodash.concat(u.pagination.list,r.list),u.pagination.total=r.total,u.loadStatus=u.pagination.list.length<u.pagination.total?"more":"noMore")}function l(e){u.pagination.list=[],u.level=e+"",c()}function m(e,o){u.pagination.list=[],i=e+o.toUpperCase(),u.isAsc=o,u.sortKey=e,c()}return e.onLoad((async()=>{await c();let{data:e}=await t.BrokerageApi.getBrokerageUserSummary();u.summary=e})),e.onReachBottom((()=>{"noMore"!==u.loadStatus&&(u.pagination.pageNo++,c())})),(t,a)=>e.e({a:e.t(u.summary.firstBrokerageUserCount+u.summary.secondBrokerageUserCount||0),b:e.s({marginTop:"-"+Number(n+88)+"rpx",paddingTop:Number(n+108)+"rpx"}),c:e.t(u.summary.firstBrokerageUserCount||0),d:e.n(1==u.level?"item on":"item"),e:e.o((e=>l(1))),f:e.t(u.summary.secondBrokerageUserCount||0),g:e.n(2==u.level?"item on":"item"),h:e.o((e=>l(2))),i:e.o(p),j:u.nickname,k:e.o((e=>u.nickname=e.detail.value)),l:o._imports_0,m:e.o(p),n:"userCountDESC"===e.unref(i)},"userCountDESC"===e.unref(i)?{o:o._imports_1,p:e.o((e=>m("userCount","asc")))}:"userCountASC"===e.unref(i)?{r:o._imports_2,s:e.o((e=>m("userCount","desc")))}:{t:o._imports_3,v:e.o((e=>m("userCount","desc")))},{q:"userCountASC"===e.unref(i),w:"priceDESC"===e.unref(i)},"priceDESC"===e.unref(i)?{x:o._imports_1,y:e.o((e=>m("price","asc")))}:"priceASC"===e.unref(i)?{A:o._imports_2,B:e.o((e=>m("price","desc")))}:{C:o._imports_3,D:e.o((e=>m("price","desc")))},{z:"priceASC"===e.unref(i),E:"orderCountDESC"===e.unref(i)},"orderCountDESC"===e.unref(i)?{F:o._imports_1,G:e.o((e=>m("orderCount","asc")))}:"orderCountASC"===e.unref(i)?{I:o._imports_2,J:e.o((e=>m("orderCount","desc")))}:{K:o._imports_3,L:e.o((e=>m("orderCount","desc")))},{H:"orderCountASC"===e.unref(i),M:e.f(u.pagination.list,((o,t,a)=>({a:o.avatar,b:e.t(o.nickname),c:e.t(e.unref(r.sheep).$helper.timeFormat(o.brokerageTime,"yyyy-mm-dd hh:MM:ss")),d:e.t(o.brokerageUserCount||0),e:e.t(o.orderCount||0),f:e.t(o.brokeragePrice||0),g:t}))),N:0===u.pagination.list.length},(u.pagination.list.length,{}),{O:e.n(u.scrollTop?"team-wrap":""),P:e.s(t.__cssVars()),Q:e.p({title:"我的团队",navbar:"inner"})})}},n=e._export_sfc(a,[["__scopeId","data-v-a5b2d148"]]);a.__runtimeHooks=1,wx.createPage(n);
