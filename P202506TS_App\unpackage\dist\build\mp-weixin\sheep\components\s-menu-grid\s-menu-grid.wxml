<view class="data-v-87781ccd" style="{{c + ';' + d}}"><uni-grid wx:if="{{b}}" class="data-v-87781ccd" u-s="{{['d']}}" u-i="87781ccd-0" bind:__l="__l" u-p="{{b}}"><uni-grid-item wx:for="{{a}}" wx:for-item="item" wx:key="i" class="data-v-87781ccd" u-s="{{['d']}}" bindtap="{{item.j}}" u-i="{{item.k}}" bind:__l="__l"><view class="grid-item-box ss-flex ss-flex-col ss-row-center ss-col-center data-v-87781ccd"><view class="img-box data-v-87781ccd"><view wx:if="{{item.a}}" class="tag-box data-v-87781ccd" style="{{item.c}}">{{item.b}}</view><image class="menu-image data-v-87781ccd" src="{{item.d}}"></image></view><view class="title-box ss-flex ss-flex-col ss-row-center ss-col-center data-v-87781ccd"><view class="grid-text data-v-87781ccd" style="{{item.f}}">{{item.e}}</view><view class="grid-tip data-v-87781ccd" style="{{item.h}}">{{item.g}}</view></view></view></uni-grid-item></uni-grid></view>