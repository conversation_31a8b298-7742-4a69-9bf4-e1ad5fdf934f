package cn.iocoder.yudao.module.member.convert.contract;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.app.contract.vo.AppContractRespVO;
import cn.iocoder.yudao.module.member.dal.dataobject.contract.ContractDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:13:18+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
public class ContractConvertImpl implements ContractConvert {

    @Override
    public AppContractRespVO convert(ContractDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppContractRespVO appContractRespVO = new AppContractRespVO();

        appContractRespVO.setId( bean.getId() );
        appContractRespVO.setName( bean.getName() );
        appContractRespVO.setNo( bean.getNo() );
        appContractRespVO.setFileUrl( bean.getFileUrl() );
        appContractRespVO.setAuditStatus( bean.getAuditStatus() );
        appContractRespVO.setStartTime( bean.getStartTime() );
        appContractRespVO.setEndTime( bean.getEndTime() );
        appContractRespVO.setRemark( bean.getRemark() );
        appContractRespVO.setTags( bean.getTags() );
        appContractRespVO.setFavorite( bean.getFavorite() );
        appContractRespVO.setDocumentId( bean.getDocumentId() );
        appContractRespVO.setContractId( bean.getContractId() );
        appContractRespVO.setQysStatus( bean.getQysStatus() );
        appContractRespVO.setCreateTime( bean.getCreateTime() );
        appContractRespVO.setTop( bean.getTop() );

        return appContractRespVO;
    }

    @Override
    public PageResult<AppContractRespVO> convertPage(PageResult<ContractDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppContractRespVO> pageResult = new PageResult<AppContractRespVO>();

        pageResult.setList( contractDOListToAppContractRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    protected List<AppContractRespVO> contractDOListToAppContractRespVOList(List<ContractDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppContractRespVO> list1 = new ArrayList<AppContractRespVO>( list.size() );
        for ( ContractDO contractDO : list ) {
            list1.add( convert( contractDO ) );
        }

        return list1;
    }
}
