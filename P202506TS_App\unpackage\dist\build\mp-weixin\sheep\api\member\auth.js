"use strict";const e=require("../../request/index.js"),s={login:s=>e.request({url:"/member/auth/login",method:"POST",data:s,custom:{showSuccess:!0,loadingMsg:"登录中",successMsg:"登录成功"}}),smsLogin:s=>e.request({url:"/member/auth/sms-login",method:"POST",data:s,custom:{showSuccess:!0,loadingMsg:"登录中",successMsg:"登录成功"}}),sendSmsCode:(s,o)=>e.request({url:"/member/auth/send-sms-code",method:"POST",data:{mobile:s,scene:o},custom:{loadingMsg:"发送中",showSuccess:!0,successMsg:"发送成功"}}),logout:()=>e.request({url:"/member/auth/logout",method:"POST"}),refreshToken:s=>e.request({url:"/member/auth/refresh-token",method:"POST",params:{refreshToken:s},custom:{loading:!1,showError:!1}}),socialAuthRedirect:(s,o)=>e.request({url:"/member/auth/social-auth-redirect",method:"GET",params:{type:s,redirectUri:o},custom:{showSuccess:!0,loadingMsg:"登陆中"}}),socialLogin:(s,o,t)=>e.request({url:"/member/auth/social-login",method:"POST",data:{type:s,code:o,state:t},custom:{showSuccess:!0,loadingMsg:"登陆中"}}),weixinMiniAppLogin:(s,o,t)=>e.request({url:"/member/auth/weixin-mini-app-login",method:"POST",data:{phoneCode:s,loginCode:o,state:t},custom:{showSuccess:!0,loadingMsg:"登陆中",successMsg:"登录成功"}}),createWeixinMpJsapiSignature:s=>e.request({url:"/member/auth/create-weixin-jsapi-signature",method:"POST",params:{url:s},custom:{showError:!1,showLoading:!1}})};exports.AuthUtil=s;
