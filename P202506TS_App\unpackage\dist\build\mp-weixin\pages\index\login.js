"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),s=require("../../sheep/hooks/useModal.js");Math||(n+a+t+u+c+i)();const n=()=>"./components/login/account-login.js",a=()=>"./components/login/sms-login.js",t=()=>"./components/login/reset-password.js",u=()=>"./components/login/change-mobile.js",c=()=>"./components/login/change-password.js",i=()=>"./components/login/mp-authorization.js",l={__name:"login",setup(n){e.useCssVars((o=>({c81ae33e:e.unref(a)})));const a=o.sheep.$url.css("/assets/mp/login/bg_login.png"),t=e.computed((()=>o.sheep.$store("user").isLogin)),u=o.sheep.$store("modal"),c=e.computed((()=>u.auth||"smsLogin")),i=e.reactive({protocol:!1}),l=e.ref(!1);function r(){i.protocol=!i.protocol}function g(e){s.closeAuthModal(),o.sheep.$router.go("/pages/public/richtext",{title:e})}function p(e){l.value=e,setTimeout((()=>{l.value=!1}),1e3)}return e.onLoad((()=>{t.value&&o.sheep.$router.redirect("/pages/index/register")})),(s,n)=>e.e({a:e.unref(o.sheep).$url.static("/assets/mp/login/logo.png"),b:"accountLogin"===c.value},"accountLogin"===c.value?{c:e.o(p),d:e.p({agreeStatus:i.protocol})}:{},{e:"smsLogin"===c.value},"smsLogin"===c.value?{f:e.o(p),g:e.p({agreeStatus:i.protocol})}:{},{h:"resetPassword"===c.value},(c.value,{}),{i:"changeMobile"===c.value},(c.value,{}),{j:"changePassword"===c.value},(c.value,{}),{k:"mpAuthorization"===c.value},(c.value,{}),{l:["accountLogin","smsLogin"].includes(c.value)&&!1},(["accountLogin","smsLogin"].includes(c.value),{}),{v:["accountLogin","smsLogin"].includes(c.value)},["accountLogin","smsLogin"].includes(c.value)?{w:i.protocol,x:e.o(r),y:e.o((e=>g("服务协议"))),z:e.o((e=>g("隐私政策"))),A:e.o(r),B:l.value?1:""}:{},{C:e.s(s.__cssVars())})}},r=e._export_sfc(l,[["__scopeId","data-v-0c4ea268"]]);wx.createPage(r);
