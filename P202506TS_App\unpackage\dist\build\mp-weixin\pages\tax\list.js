"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),n=require("../../sheep/util/index.js"),o=require("../../sheep/api/member/reportAisino.js");if(!Array){(e.resolveComponent("uni-list-item")+e.resolveComponent("uni-list")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-empty")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const i={__name:"list",setup(i){const a=e.reactive({pagination:{list:[],total:0,pageNo:1,pageSize:6},loadStatus:""});function s(e){a.keyword=e,n.resetPagination(a.pagination),l()}async function l(){a.loadStatus="loading";const t=e.index.getStorageSync("enterprise"),{code:n,data:i}=await o.ReportAisinoApi.getReportAisinoPage({pageNo:a.pagination.pageNo,pageSize:a.pagination.pageSize,enterpriseId:t.id,keyword:a.keyword});0===n&&(a.pagination.list=e.lodash.concat(a.pagination.list,i.list),a.pagination.total=i.total,a.loadStatus=a.pagination.list.length<a.pagination.total?"more":"noMore")}function r(){"noMore"!==a.loadStatus&&(a.pagination.pageNo++,l())}return e.onLoad((e=>{l()})),e.onReachBottom((()=>{r()})),e.onPullDownRefresh((()=>{l(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),(n,i)=>e.e({a:a.pagination.total>0},a.pagination.total>0?{b:e.f(a.pagination.list,((n,i,a)=>{var l;return e.e({a:e.t(n.bgId),b:e.t(["","全面检测","快速检测","专项检测"][n.detectionType]),c:e.t(null==(l=JSON.parse(n.content))?void 0:l.jcsj),d:e.t(["否","是"][n.enable]),e:n.fileUrl},n.fileUrl?{f:e.o((o=>(async n=>{n.enable?n.fileUrl?(e.index.showLoading({title:"加载中",mask:!0,fail:()=>{e.index.hideLoading()}}),e.index.request({url:t.sheep.$url.static(n.fileUrl),method:"GET",responseType:"arraybuffer"}).then((t=>{const n=e.index.getFileSystemManager(),o="tax_report_"+Date.now()+".pdf";if(!e.wx$1.env||!e.wx$1.env.USER_DATA_PATH)return e.index.hideLoading(),void e.index.showToast({title:"无法访问文件存储路径",icon:"none"});const i=e.wx$1.env.USER_DATA_PATH+"/"+o;n.writeFile({filePath:i,data:t.data,encoding:"binary",success(t){e.index.openDocument({filePath:i,showMenu:!0,success:t=>{e.index.hideLoading(),e.index.showToast({title:"文件获取成功，请通过右上角菜单进行分享保存",icon:"none"})},fail:t=>{e.index.hideLoading(),e.index.showToast({title:"打开文件失败",icon:"none"})}})},fail(t){e.index.hideLoading(),e.index.showToast({title:"写文件失败",icon:"none"})}})})).catch((t=>{e.index.hideLoading(),e.index.showToast({title:"文件获取失败",icon:"none"})}))):t.sheep.$helper.toast("报告未下载"):t.sheep.$helper.toast("还未生成，请使用税务检测检查功能生成报告")})(n)),i)}:n.reportId?{h:e.o((i=>(async n=>{const i=e.index.getStorageSync("enterprise"),{code:a}=await o.ReportAisinoApi.getFileUrl(i.id,n);0===a&&(t.sheep.$helper.toast("下载成功"),await s())})(n.id)),i)}:{},{g:n.reportId,i:i,j:"56189650-2-"+a+",56189650-1"})}))}:{},{c:a.pagination.total>0},a.pagination.total>0?{d:e.o(r),e:e.p({status:a.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{f:0===a.pagination.total},0===a.pagination.total?{g:e.p({icon:e.unref(t.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无稿件"})}:{},{h:e.p({title:"税务风险检测"})})}},a=e._export_sfc(i,[["__scopeId","data-v-56189650"]]);wx.createPage(a);
