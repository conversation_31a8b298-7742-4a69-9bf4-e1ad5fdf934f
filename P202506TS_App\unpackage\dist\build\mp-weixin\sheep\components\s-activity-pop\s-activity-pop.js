"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),r=require("../../hooks/useGoods.js");if(!Array){e.resolveComponent("su-popup")()}Math;const o={__name:"s-activity-pop",props:{modelValue:{type:Object,default(){}},show:{type:Boolean,default:!1}},emits:["close"],setup(o,{emit:i}){const d=o,a=i,n=e.reactive({rewardActivity:e.computed((()=>d.modelValue.rewardActivity)),couponInfo:e.computed((()=>d.modelValue.couponInfo))});return(i,d)=>e.e({a:n.rewardActivity&&n.rewardActivity.id>0},n.rewardActivity&&n.rewardActivity.id>0?{b:e.f(e.unref(r.getRewardActivityRuleGroupDescriptions)(n.rewardActivity),((r,o,i)=>({a:e.t(r.name),b:e.t(r.values.join(";")),c:e.o((e=>{return r=n.rewardActivity,void t.sheep.$router.go("/pages/activity/index",{activityId:r.id});var r}),o),d:o}))),c:e.t(e.unref(t.sheep).$helper.timeFormat(n.rewardActivity.startTime,"yyyy.mm.dd")),d:e.t(e.unref(t.sheep).$helper.timeFormat(n.rewardActivity.endTime,"yyyy.mm.dd"))}:{},{e:n.couponInfo.length},n.couponInfo.length?{f:e.f(n.couponInfo,((o,i,d)=>e.e({a:e.t(e.unref(r.fen2yuan)(o.discountPrice)),b:e.t(e.unref(r.fen2yuan)(o.usePrice)),c:e.t(o.name),d:e.t(1==o.validityType?e.unref(t.sheep).$helper.timeFormat(o.validStartTime,"yyyy-mm-dd")+"-"+e.unref(t.sheep).$helper.timeFormat(o.validEndTime,"yyyy-mm-dd"):"领取后"+o.fixedStartTerm+"-"+o.fixedEndTerm+"天可用"),e:o.canTake},o.canTake?{f:e.o((e=>{return t=o.id,void a("get",t);var t}),o.id)}:{},{g:o.id})))}:{},{g:e.o((e=>a("close"))),h:e.p({show:o.show,type:"bottom",round:"20",showClose:!0})})}},i=e._export_sfc(o,[["__scopeId","data-v-dfb72acd"]]);wx.createComponent(i);
