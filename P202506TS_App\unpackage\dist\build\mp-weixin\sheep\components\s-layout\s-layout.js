"use strict";const e=require("../../../common/vendor.js"),a=require("../../index.js");if(!Array){(e.resolveComponent("su-navbar")+e.resolveComponent("s-custom-navbar")+e.resolveComponent("su-inner-navbar")+e.resolveComponent("s-tabbar")+e.resolveComponent("s-auth-modal")+e.resolveComponent("s-share-modal")+e.resolveComponent("s-menu-tools"))()}Math||((()=>"../../ui/su-navbar/su-navbar.js")+(()=>"../s-custom-navbar/s-custom-navbar.js")+(()=>"../../ui/su-inner-navbar/su-inner-navbar.js")+(()=>"../s-tabbar/s-tabbar.js")+(()=>"../s-auth-modal/s-auth-modal.js")+(()=>"../s-share-modal/s-share-modal.js")+(()=>"../s-menu-tools/s-menu-tools.js"))();const t={__name:"s-layout",props:{title:{type:String,default:""},navbar:{type:String,default:"normal"},opacityBgUi:{type:String,default:"bg-white"},color:{type:String,default:""},tools:{type:String,default:"title"},keyword:{type:String,default:""},navbarStyle:{type:Object,default:()=>({styleType:"",type:"",color:"",src:"",list:[],alwaysShow:0})},bgStyle:{type:Object,default:()=>({src:"",color:"var(--ui-BG-1)"})},tabbar:{type:[String,Boolean],default:""},onShareAppMessage:{type:[Boolean,Object],default:!0},leftWidth:{type:[Number,String],default:100},rightWidth:{type:[Number,String],default:100},defaultSearch:{type:String,default:""},showLeftButton:{type:Boolean,default:!1}},emits:["search"],setup(t,{emit:r}){const o=t,n=r,s=a.sheep.$store("sys");a.sheep.$store("user"),a.sheep.$store("app"),a.sheep.$store("modal");const l=e.computed((()=>s)),u=e.computed((()=>"normal"===o.navbar||"normal"===o.navbarStyle.styleType?"normal":"inner")),p=e.computed((()=>"inner"===u.value?{background:`${o.bgStyle.backgroundColor||o.bgStyle.color} ${a.sheep.$url.css(o.bgStyle.backgroundImage)} no-repeat top center / 100% auto`}:{})),i=e.computed((()=>"normal"===u.value?{background:`${o.bgStyle.backgroundColor||o.bgStyle.color} ${a.sheep.$url.css(o.bgStyle.backgroundImage)} no-repeat top center / 100% auto`}:{})),m=e.computed((()=>!0===o.onShareAppMessage?a.sheep.$platform.share.getShareInfo():e.isEmpty(o.onShareAppMessage)?{}:(a.sheep.$platform.share.updateShareInfo(o.onShareAppMessage),o.onShareAppMessage)));return e.index.showShareMenu({withShareTicket:!0,menus:["shareAppMessage","shareTimeline"]}),e.onShareAppMessage((()=>({title:m.value.title,path:m.value.forward.path,imageUrl:m.value.image}))),e.onShareTimeline((()=>({title:m.value.title,query:m.value.forward.path,imageUrl:m.value.image}))),e.onMounted((()=>{e.isEmpty(m.value)||a.sheep.$platform.share.updateShareInfo(m.value)})),(r,o)=>{var s,d,h,v,b;return e.e({a:"normal"===t.navbar},"normal"===t.navbar?{b:e.o((e=>n("search",e))),c:e.p({title:t.title,statusBar:!0,color:t.color,tools:t.tools,opacityBgUi:t.opacityBgUi,leftWidth:t.leftWidth,rightWidth:t.rightWidth,defaultSearch:t.defaultSearch})}:"custom"===t.navbar&&"normal"===u.value?{e:e.p({data:t.navbarStyle,showLeftButton:t.showLeftButton})}:{},{d:"custom"===t.navbar&&"normal"===u.value,f:"inner"===t.navbar},"inner"===t.navbar?{g:e.p({title:t.title})}:{},{h:"inner"===t.navbar},"inner"===t.navbar?{i:e.s({paddingTop:(null==(d=null==(s=e.unref(a.sheep))?void 0:s.$platform)?void 0:d.navbar)+"px"})}:{},{j:"custom"===t.navbar&&"inner"===u.value},"custom"===t.navbar&&"inner"===u.value?{k:e.p({data:t.navbarStyle,showLeftButton:t.showLeftButton})}:{},{l:""!==t.tabbar},""!==t.tabbar?{m:e.p({path:t.tabbar})}:{},{n:e.s(i.value),o:e.s(p.value),p:e.p({shareInfo:m.value}),q:e.n("theme-"+(null==(h=l.value)?void 0:h.mode)),r:e.n("main-"+(null==(v=l.value)?void 0:v.theme)),s:e.n("font-"+(null==(b=l.value)?void 0:b.fontSize))})}}},r=e._export_sfc(t,[["__scopeId","data-v-68addf1b"]]);wx.createComponent(r);
