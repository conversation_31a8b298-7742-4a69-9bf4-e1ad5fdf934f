"use strict";const e=require("../../../common/vendor.js"),r=require("../../request/index.js"),t=require("../../helper/utils.js"),d={settlementOrder:e=>{const d={...e};e.couponId>0||delete d.couponId,e.addressId>0||delete d.addressId,e.pickUpStoreId>0||delete d.pickUpStoreId,t.isEmpty(e.receiverName)&&delete d.receiverName,t.isEmpty(e.receiverMobile)&&delete d.receiverMobile,e.combinationActivityId>0||delete d.combinationActivityId,e.combinationHeadId>0||delete d.combinationHeadId,e.seckillActivityId>0||delete d.seckillActivityId,e.pointActivityId>0||delete d.pointActivityId,e.deliveryType>0||delete d.deliveryType,delete d.items;for(let r=0;r<e.items.length;r++)d[encodeURIComponent("items["+r+"].skuId")]=e.items[r].skuId+"",d[encodeURIComponent("items["+r+"].count")]=e.items[r].count+"",e.items[r].cartId&&(d[encodeURIComponent("items["+r+"].cartId")]=e.items[r].cartId+"");const o=Object.keys(d).map((e=>e+"="+d[e])).join("&");return r.request({url:`/trade/order/settlement?${o}`,method:"GET",custom:{showError:!0,showLoading:!0}})},getSettlementProduct:e=>r.request({url:"/trade/order/settlement-product",method:"GET",params:{spuIds:e},custom:{showLoading:!1,showError:!1}}),createOrder:e=>r.request({url:"/trade/order/create",method:"POST",data:e}),getOrderDetail:(e,t)=>r.request({url:"/trade/order/get-detail",method:"GET",params:{id:e,sync:t},custom:{showLoading:!1}}),getOrderPage:e=>r.request({url:"/trade/order/page",method:"GET",params:e,custom:{showLoading:!1}}),getOrderPageByEnterprise:t=>{const d=e.index.getStorageSync("enterprise"),o=d?d.id:null;return r.request({url:"/trade/order/page-by-enterprise",method:"GET",params:{...t,enterpriseId:o},custom:{showLoading:!1}})},receiveOrder:e=>r.request({url:"/trade/order/receive",method:"PUT",params:{id:e}}),cancelOrder:e=>r.request({url:"/trade/order/cancel",method:"DELETE",params:{id:e}}),deleteOrder:e=>r.request({url:"/trade/order/delete",method:"DELETE",params:{id:e}}),getOrderExpressTrackList:e=>r.request({url:"/trade/order/get-express-track-list",method:"GET",params:{id:e}}),getOrderCount:()=>r.request({url:"/trade/order/get-count",method:"GET",custom:{showLoading:!1,auth:!0}}),createOrderItemComment:e=>r.request({url:"/trade/order/item/create-comment",method:"POST",data:e}),applyOrderInvoice:e=>r.request({url:"/trade/order/apply-invoice",method:"POST",data:e,custom:{showLoading:!0,showError:!0}}),getInvoiceByOrderId:e=>r.request({url:"/trade/invoice/get-by-order",method:"GET",params:{orderId:e},custom:{showLoading:!1,showError:!1}})};exports.OrderApi=d;
