<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="企业" prop="enterpriseId">
        <el-select
          v-model="formData.enterpriseId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入企业名称进行搜索"
          :remote-method="searchEnterprise"
          :loading="enterpriseSearchLoading"
          class="w-full"
          clearable
        >
          <el-option
            v-for="item in enterpriseOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="权益" prop="benefitId">
        <el-select
          v-model="formData.benefitId"
          filterable
          placeholder="请选择权益"
          class="w-full"
          clearable
          @change="
            (val) => {
              const selectedOption = benefitOptions.find((item) => item.id === val)
              formData.benefitCode = selectedOption?.code
              formData.benefitName = selectedOption?.name
            }
          "
        >
          <el-option
            v-for="item in benefitOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- 时间类权益的专用字段 -->
      <template v-if="isTimeBenefit(formData.benefitUnit)">
        <el-form-item label="天数" prop="timeBenefitDays">
          <el-input-number
            v-model="formData.timeBenefitDays"
            :min="1"
            :max="3650"
            placeholder="请输入天数"
            @change="updateExpiryFromDays(formData.timeBenefitDays)"
            class="w-full"
          />
          <div class="text-xs text-gray-500 mt-1">
            权益有效期天数（1-3650天）
          </div>
        </el-form-item>
        <el-form-item label="到期时间" prop="timeBenefitExpiry">
          <el-date-picker
            v-model="formData.timeBenefitExpiry"
            type="datetime"
            placeholder="选择到期时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DDTHH:mm"
            @change="updateDaysFromExpiry(formData.timeBenefitExpiry)"
            class="w-full"
          />
          <div class="text-xs text-gray-500 mt-1">
            权益到期的具体时间
          </div>
        </el-form-item>
      </template>

      <!-- 次数类权益的原有字段 -->
      <template v-else>
        <el-form-item label="总量" prop="benefitTotal">
          <el-input v-model="formData.benefitTotal" :placeholder="getTotalPlaceholder()" />
        </el-form-item>
        <el-form-item label="余量" prop="benefitBalance">
          <el-input v-model="formData.benefitBalance" :placeholder="getBalancePlaceholder()" />
        </el-form-item>
      </template>
      <el-form-item label="单位" prop="benefitUnit">
        <el-input v-model="formData.benefitUnit" placeholder="请输入单位（如：次、秒、天等）" />
        <div class="text-xs text-gray-500 mt-1">
          时间类权益单位：秒、分钟、天；次数类权益单位：次、个等
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { EnterpriseBenefitApi, EnterpriseBenefitVO } from '@/api/member/enterprisebenefit'
import { EnterpriseApi } from '@/api/member/enterprise'
import { BenefitApi } from '@/api/product/benefit'

/** 企业权益 表单 */
defineOptions({ name: 'EnterpriseBenefitForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  enterpriseId: undefined,
  benefitId: undefined,
  benefitCode: undefined as string | undefined,
  benefitName: undefined as string | undefined,
  benefitTotal: undefined as number | undefined,
  benefitBalance: undefined as number | undefined,
  benefitUnit: undefined as string | undefined,
  // 时间类权益专用字段
  timeBenefitDays: undefined as number | undefined,
  timeBenefitExpiry: undefined as string | undefined
})
const formRules = reactive({
  enterpriseId: [{ required: true, message: '企业编号不能为空', trigger: 'blur' }],
  benefitId: [{ required: true, message: '权益编号不能为空', trigger: 'blur' }],
  benefitTotal: [{ required: true, message: '总量不能为空', trigger: 'blur' }],
  benefitBalance: [{ required: true, message: '余量不能为空', trigger: 'blur' }],
  timeBenefitDays: [
    { required: true, message: '天数不能为空', trigger: 'blur' },
    {
      validator: (_rule: any, value: number, callback: any) => {
        if (value < 1 || value > 3650) {
          callback(new Error('天数必须在1-3650之间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  timeBenefitExpiry: [
    { required: true, message: '到期时间不能为空', trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref
const benefitOptions = ref<{ name: string; id: number; code?: string }[]>([]) // 权益下拉列表

// 基准创建时间
const baseCreateTime = ref<Date>(new Date())

// 权益显示相关工具函数
/** 判断是否为时间类权益 */
const isTimeBenefit = (unit?: string): boolean => {
  if (!unit) return false
  return unit === '秒' || unit === '分钟' || unit === '天'
}

/** 获取总量输入框占位符 */
const getTotalPlaceholder = (): string => {
  if (isTimeBenefit(formData.value.benefitUnit)) {
    return '请输入总时长（秒数）'
  }
  return '请输入总量'
}

/** 获取余量输入框占位符 */
const getBalancePlaceholder = (): string => {
  if (isTimeBenefit(formData.value.benefitUnit)) {
    return '请输入总时长（秒数，通常与总量相同）'
  }
  return '请输入余量'
}

// 时间类权益数据转换函数
/** 获取基准创建时间 */
const getBaseCreateTime = (): Date => {
  // 编辑模式：使用数据库中的创建时间
  if (formType.value === 'update' && formData.value.id) {
    // 这里需要从后端获取创建时间，暂时使用当前时间
    return baseCreateTime.value
  }
  // 新增模式：使用当前时间
  return new Date()
}

/** 根据天数更新到期时间 */
const updateExpiryFromDays = (days?: number) => {
  if (!days) return
  const createTime = getBaseCreateTime()
  const expiryTime = new Date(createTime.getTime() + days * 24 * 60 * 60 * 1000)
  formData.value.timeBenefitExpiry = expiryTime.toISOString().slice(0, 16) // YYYY-MM-DDTHH:mm格式
}

/** 根据到期时间更新天数 */
const updateDaysFromExpiry = (expiryTimeStr?: string) => {
  if (!expiryTimeStr) return
  const createTime = getBaseCreateTime()
  const expiryTime = new Date(expiryTimeStr)
  const diffMs = expiryTime.getTime() - createTime.getTime()
  const days = Math.max(0, Math.ceil(diffMs / (24 * 60 * 60 * 1000)))
  formData.value.timeBenefitDays = days
}

/** 将天数转换为秒数（提交时使用） */
const convertToSeconds = (): void => {
  if (isTimeBenefit(formData.value.benefitUnit) && formData.value.timeBenefitDays) {
    const seconds = formData.value.timeBenefitDays * 24 * 60 * 60
    formData.value.benefitTotal = seconds
    formData.value.benefitBalance = seconds // 时间类权益余量等于总量
  }
}

/** 将秒数转换为天数和到期时间（加载时使用） */
const convertFromSeconds = (): void => {
  if (isTimeBenefit(formData.value.benefitUnit) && formData.value.benefitTotal) {
    const days = Math.floor(formData.value.benefitTotal / (24 * 60 * 60))
    formData.value.timeBenefitDays = days
    updateExpiryFromDays(days)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await EnterpriseBenefitApi.getEnterpriseBenefit(id)
      // 如果是时间类权益，设置基准创建时间并转换数据
      if (isTimeBenefit(formData.value.benefitUnit)) {
        // 这里应该从API获取创建时间，暂时使用当前时间
        baseCreateTime.value = new Date()
        convertFromSeconds()
      }
    } finally {
      formLoading.value = false
    }
  } else {
    // 新增时，设置当前时间为基准创建时间
    baseCreateTime.value = new Date()
  }

  // 获得权益下拉列表
  benefitOptions.value = await BenefitApi.getBenefitList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 如果是时间类权益，先转换数据
  if (isTimeBenefit(formData.value.benefitUnit)) {
    convertToSeconds()
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as EnterpriseBenefitVO
    if (formType.value === 'create') {
      await EnterpriseBenefitApi.createEnterpriseBenefit(data)
      message.success(t('common.createSuccess'))
    } else {
      await EnterpriseBenefitApi.updateEnterpriseBenefit(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    enterpriseId: undefined,
    benefitId: undefined,
    benefitCode: undefined as string | undefined,
    benefitName: undefined as string | undefined,
    benefitTotal: undefined as number | undefined,
    benefitBalance: undefined as number | undefined,
    benefitUnit: undefined as string | undefined,
    // 重置时间类权益的专用数据
    timeBenefitDays: undefined as number | undefined,
    timeBenefitExpiry: undefined as string | undefined
  }

  baseCreateTime.value = new Date()

  formRef.value?.resetFields()
}

/** 搜索企业 */
const enterpriseOptions = ref<{ name: string; id: number }[]>([]) // 合作方选项
const enterpriseSearchLoading = ref(false) // 合作方搜索加载状态
const searchEnterprise = async (query: string) => {
  if (query.length < 1) return

  enterpriseSearchLoading.value = true
  try {
    const res = await EnterpriseApi.getEnterpriseSimplePage({
      pageNo: 1,
      pageSize: 10,
      name: query
    })
    enterpriseOptions.value = res.list
  } catch (err) {
    console.error('搜索企业失败', err)
  } finally {
    enterpriseSearchLoading.value = false
  }
}
</script>
