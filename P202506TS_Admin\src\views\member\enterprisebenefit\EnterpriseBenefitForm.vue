<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="企业" prop="enterpriseId">
        <el-select
          v-model="formData.enterpriseId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入企业名称进行搜索"
          :remote-method="searchEnterprise"
          :loading="enterpriseSearchLoading"
          class="w-full"
          clearable
        >
          <el-option
            v-for="item in enterpriseOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="权益" prop="benefitId">
        <el-select
          v-model="formData.benefitId"
          filterable
          placeholder="请选择权益"
          class="w-full"
          clearable
          @change="
            (val) => {
              const selectedOption = benefitOptions.find((item) => item.id === val)
              formData.benefitCode = selectedOption?.code
              formData.benefitName = selectedOption?.name
            }
          "
        >
          <el-option
            v-for="item in benefitOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="总量" prop="benefitTotal">
        <el-input v-model="formData.benefitTotal" :placeholder="getTotalPlaceholder()" />
        <div class="text-xs text-gray-500 mt-1" v-if="isTimeBenefit(formData.benefitUnit)">
          时间类权益：请输入秒数（如：30天 = 2592000秒）
        </div>
      </el-form-item>
      <el-form-item label="余量" prop="benefitBalance">
        <el-input v-model="formData.benefitBalance" :placeholder="getBalancePlaceholder()" />
        <div class="text-xs text-gray-500 mt-1" v-if="isTimeBenefit(formData.benefitUnit)">
          时间类权益：余量通常与总量相同，表示权益的总时长
        </div>
      </el-form-item>
      <el-form-item label="单位" prop="benefitUnit">
        <el-input v-model="formData.benefitUnit" placeholder="请输入单位（如：次、秒、天等）" />
        <div class="text-xs text-gray-500 mt-1">
          时间类权益单位：秒、分钟、天；次数类权益单位：次、个等
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { EnterpriseBenefitApi, EnterpriseBenefitVO } from '@/api/member/enterprisebenefit'
import { EnterpriseApi } from '@/api/member/enterprise'
import { BenefitApi } from '@/api/product/benefit'

/** 企业权益 表单 */
defineOptions({ name: 'EnterpriseBenefitForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  enterpriseId: undefined,
  benefitId: undefined,
  benefitCode: undefined as string | undefined,
  benefitName: undefined as string | undefined,
  benefitTotal: undefined,
  benefitBalance: undefined,
  benefitUnit: undefined as string | undefined
})
const formRules = reactive({
  enterpriseId: [{ required: true, message: '企业编号不能为空', trigger: 'blur' }],
  benefitId: [{ required: true, message: '权益编号不能为空', trigger: 'blur' }],
  benefitTotal: [{ required: true, message: '总量不能为空', trigger: 'blur' }],
  benefitBalance: [{ required: true, message: '余量不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const benefitOptions = ref<{ name: string; id: number; code?: string }[]>([]) // 权益下拉列表

// 权益显示相关工具函数
/** 判断是否为时间类权益 */
const isTimeBenefit = (unit?: string): boolean => {
  if (!unit) return false
  return unit === '秒' || unit === '分钟' || unit === '天'
}

/** 获取总量输入框占位符 */
const getTotalPlaceholder = (): string => {
  if (isTimeBenefit(formData.value.benefitUnit)) {
    return '请输入总时长（秒数）'
  }
  return '请输入总量'
}

/** 获取余量输入框占位符 */
const getBalancePlaceholder = (): string => {
  if (isTimeBenefit(formData.value.benefitUnit)) {
    return '请输入总时长（秒数，通常与总量相同）'
  }
  return '请输入余量'
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await EnterpriseBenefitApi.getEnterpriseBenefit(id)
    } finally {
      formLoading.value = false
    }
  }

  // 获得权益下拉列表
  benefitOptions.value = await BenefitApi.getBenefitList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as EnterpriseBenefitVO
    if (formType.value === 'create') {
      await EnterpriseBenefitApi.createEnterpriseBenefit(data)
      message.success(t('common.createSuccess'))
    } else {
      await EnterpriseBenefitApi.updateEnterpriseBenefit(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    enterpriseId: undefined,
    benefitId: undefined,
    benefitCode: undefined as string | undefined,
    benefitName: undefined as string | undefined,
    benefitTotal: undefined,
    benefitBalance: undefined,
    benefitUnit: undefined as string | undefined
  }
  formRef.value?.resetFields()
}

/** 搜索企业 */
const enterpriseOptions = ref<{ name: string; id: number }[]>([]) // 合作方选项
const enterpriseSearchLoading = ref(false) // 合作方搜索加载状态
const searchEnterprise = async (query: string) => {
  if (query.length < 1) return

  enterpriseSearchLoading.value = true
  try {
    const res = await EnterpriseApi.getEnterpriseSimplePage({
      pageNo: 1,
      pageSize: 10,
      name: query
    })
    enterpriseOptions.value = res.list
  } catch (err) {
    console.error('搜索企业失败', err)
  } finally {
    enterpriseSearchLoading.value = false
  }
}
</script>
