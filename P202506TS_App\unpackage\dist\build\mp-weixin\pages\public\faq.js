"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js");if(!Array){(e.resolveComponent("uni-collapse-item")+e.resolveComponent("uni-collapse")+e.resolveComponent("s-empty")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.js")+(()=>"../../uni_modules/uni-collapse/components/uni-collapse/uni-collapse.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const o={__name:"faq",setup(o){const s=e.reactive({list:[],loading:!0});return e.onLoad((()=>{t.sheep.$router.go("/pages/public/richtext",{title:"常见问题"})})),(t,o)=>e.e({a:e.f(s.list,((t,o,s)=>({a:e.t(o+1<10?"0"+(o+1):o+1),b:e.t(t.title),c:e.t(t.content),d:t,e:"34120e8f-2-"+s+",34120e8f-1"}))),b:0===s.list.length&&!s.loading},0!==s.list.length||s.loading?{}:{c:e.p({text:"暂无常见问题",icon:"/static/collect-empty.png"})},{d:e.p({bgStyle:{color:"#FFF"},title:"常见问题"})})}},s=e._export_sfc(o,[["__scopeId","data-v-34120e8f"]]);wx.createPage(s);
