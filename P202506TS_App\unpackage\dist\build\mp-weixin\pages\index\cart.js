"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),s=require("../../sheep/api/product/spu.js"),o=require("../../sheep/hooks/useGoods.js"),i=require("../../sheep/helper/utils.js");if(!Array){(e.resolveComponent("s-empty")+e.resolveComponent("su-number-box")+e.resolveComponent("s-goods-item")+e.resolveComponent("su-fixed")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/ui/su-number-box/su-number-box.js")+(()=>"../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const d={__name:"cart",setup(d){e.useCssVars((t=>({"547dcd06":e.unref(l)})));const l=t.sheep.$platform.navbar,r=t.sheep.$store("cart"),n=e.reactive({editMode:!1,list:e.computed((()=>r.list)),selectedList:[],selectedIds:e.computed((()=>r.selectedIds)),isAllSelected:e.computed((()=>r.isAllSelected)),totalPriceSelected:e.computed((()=>r.totalPriceSelected))});function c(e){r.selectSingle(e)}function p(){r.selectAll(!n.isAllSelected)}async function u(){const e=[];n.selectedList=n.list.filter((e=>n.selectedIds.includes(e.id))),n.selectedList.map((t=>{e.push({skuId:t.sku.id,count:t.count,cartId:t.id,categoryId:t.spu.categoryId})})),i.isEmpty(e)?t.sheep.$helper.toast("请先选择商品"):(await async function(e){const{data:o}=await s.SpuApi.getSpuListByIds(e.join(","));if(i.isEmpty(o))throw t.sheep.$helper.toast("未找到商品信息"),new Error("未找到商品信息");const d=o.map((e=>e.deliveryTypes));if(function(e){for(let t=0;t<e.length-1;t++){const s=e[t];for(let o=t+1;o<e.length;o++){const t=e[o];if(!s.some((e=>t.includes(e))))return!0}}return!1}(d))throw t.sheep.$helper.toast("选中商品支持的配送方式冲突，不允许提交"),new Error("选中商品支持的配送方式冲突，不允许提交")}(n.selectedList.map((e=>e.spu)).map((e=>e.id))),t.sheep.$router.go("/pages/order/confirm",{data:JSON.stringify({items:e})}))}async function a(){r.delete(n.selectedIds)}return(t,s)=>{var i;return e.e({a:0===n.list.length},0===n.list.length?{b:e.p({icon:"/static/cart-empty.png",text:"购物车空空如也,快去逛逛吧~"})}:{},{c:n.list.length},n.list.length?e.e({d:e.t(n.list.length),e:n.editMode},n.editMode?{f:e.o((e=>n.editMode=!1))}:{g:e.o((e=>n.editMode=!0))},{h:e.f(n.list,((t,s,o)=>e.e({a:n.selectedIds.includes(t.id),b:e.o((e=>c(t.id)),t.id),c:e.o((e=>c(t.id)),t.id)},n.editMode?{}:{d:e.o((e=>{return o=t,void(0!==(s=e)?o.goods_num!==s&&(o.goods_num=s,r.update({goods_id:o.id,goods_num:s,goods_sku_price_id:o.goods_sku_price_id})):r.delete(o.id));var s,o}),t.id),e:"ce430b72-3-"+o+",ce430b72-2-"+o,f:e.o((e=>t.count=e),t.id),g:e.p({max:t.sku.stock,min:0,step:1,modelValue:t.count})},{h:"ce430b72-2-"+o+",ce430b72-0",i:e.p({img:t.spu.picUrl||t.goods.image,price:t.sku.price,skuText:t.sku.properties.length>1?t.sku.properties.reduce(((e,t)=>e.valueName+" "+t.valueName)):t.sku.properties[0].valueName,title:t.spu.name,titleWidth:400,priceColor:"#FF3000"}),j:t.id}))),i:!n.editMode,j:n.list.length>0},n.list.length>0?e.e({k:n.isAllSelected,l:e.o(p),m:e.o(p),n:e.t(e.unref(o.fen2yuan)(n.totalPriceSelected)),o:n.editMode},n.editMode?{p:e.o(a)}:{q:e.t((null==(i=n.selectedIds)?void 0:i.length)?`(${n.selectedIds.length})`:""),r:e.o(u)},{s:e.p({isInset:!1,val:48,bottom:!0,placeholder:!0})}):{}):{},{t:e.s(t.__cssVars()),v:e.p({bgStyle:{color:"#fff"},tabbar:"/pages/index/cart",title:"购物车"})})}}},l=e._export_sfc(d,[["__scopeId","data-v-ce430b72"]]);wx.createPage(l);
