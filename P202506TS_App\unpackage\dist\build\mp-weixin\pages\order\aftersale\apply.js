"use strict";const e=require("../../../common/vendor.js"),o=require("../../../sheep/index.js"),t=require("../../../sheep/api/trade/order.js"),r=require("../../../sheep/api/trade/config.js"),s=require("../../../sheep/hooks/useGoods.js"),a=require("../../../sheep/api/trade/afterSale.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("uni-easyinput")+e.resolveComponent("s-uploader")+e.resolveComponent("uni-forms")+e.resolveComponent("su-fixed")+e.resolveComponent("su-popup")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../../sheep/components/s-uploader/s-uploader.js")+(()=>"../../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../../sheep/ui/su-popup/su-popup.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const i={__name:"apply",setup(i){const n=e.ref(null),p=e.reactive({orderId:0,itemId:0,order:{},item:{},config:{},wayList:[{text:"仅退款",value:"10"},{text:"退款退货",value:"20"}],reasonList:[],showModal:!1,currentValue:""});let u=e.reactive({way:"",applyReason:"",applyDescription:"",applyPicUrls:[]});const l=e.reactive({});async function d(){let t={orderItemId:p.itemId,refundPrice:p.item.payPrice,...u};const{code:r}=await a.AfterSaleApi.createAfterSale(t);0===r&&(e.index.showToast({title:"申请成功"}),o.sheep.$router.redirect("/pages/order/aftersale/list"))}function c(e){u.way=e.detail.value,p.reasonList="10"===u.way?p.config.afterSaleRefundReasons||[]:p.config.afterSaleReturnReasons||[],u.applyReason="",p.currentValue=""}function m(e){p.currentValue=e.detail.value}function f(){u.applyReason=p.currentValue,p.showModal=!1}return e.onLoad((async e=>{if(!e.orderId||!e.itemId)return void o.sheep.$helper.toast("缺少订单信息，请检查");p.orderId=e.orderId,p.itemId=parseInt(e.itemId);const{code:s,data:a}=await t.OrderApi.getOrderDetail(p.orderId);0===s&&(p.order=a,p.item=a.items.find((e=>e.id===p.itemId))||{},10===p.order.status&&p.wayList.splice(1,1),p.config=(await r.TradeConfigApi.getTradeConfig()).data)})),(t,r)=>{var a;return e.e({a:e.p({img:p.item.picUrl,title:p.item.spuName,skuText:null==(a=p.item.properties)?void 0:a.map((e=>e.valueName)).join(" "),price:p.item.price,num:p.item.count}),b:e.f(p.wayList,((o,t,r)=>({a:e.unref(u).type===o.value,b:o.value,c:e.t(o.text),d:t}))),c:e.o(c),d:e.t(e.unref(s.fen2yuan)(p.item.payPrice)),e:e.o((e=>p.showModal=!0)),f:e.unref(u).applyReason},e.unref(u).applyReason?{g:e.t(e.unref(u).applyReason)}:{},{h:e.o((e=>p.showModal=!0)),i:e.o((o=>e.unref(u).applyDescription=o)),j:e.p({inputBorder:!1,type:"textarea",maxlength:"120",autoHeight:!0,placeholder:"客官~请描述您遇到的问题，建议上传照片",modelValue:e.unref(u).applyDescription}),k:e.o((o=>e.unref(u).applyPicUrls=o)),l:e.p({fileMediatype:"image",limit:"9",mode:"grid",imageStyles:{width:"168rpx",height:"168rpx"},url:e.unref(u).applyPicUrls}),m:e.sr(n,"3d941d61-2,3d941d61-0",{k:"form"}),n:e.o((o=>e.isRef(u)?u.value=o:u=o)),o:e.p({rules:l,"label-position":"top",modelValue:e.unref(u)}),p:e.o((t=>e.unref(o.sheep).$router.go("/pages/chat/index"))),q:e.o(d),r:e.p({bottom:!0,placeholder:!0}),s:e.f(p.reasonList,((o,t,r)=>({a:e.t(o),b:o,c:o===p.currentValue,d:o}))),t:e.o(m),v:e.o(f),w:e.o((e=>p.showModal=!1)),x:e.p({show:p.showModal,round:"10",showClose:!0}),y:e.p({title:"申请售后"})})}}},n=e._export_sfc(i,[["__scopeId","data-v-3d941d61"]]);wx.createPage(n);
