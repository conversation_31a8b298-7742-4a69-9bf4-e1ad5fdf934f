"use strict";const e=require("../../request/index.js"),r={getSignInConfigList:()=>e.request({url:"/member/sign-in/config/list",method:"GET"}),getSignInRecordSummary:()=>e.request({url:"/member/sign-in/record/get-summary",method:"GET"}),createSignInRecord:()=>e.request({url:"/member/sign-in/record/create",method:"POST"}),getSignRecordPage:r=>{const n=Object.keys(r).map((e=>encodeURIComponent(e)+"="+r[e])).join("&");return e.request({url:`/member/sign-in/record/page?${n}`,method:"GET"})}};exports.SignInApi=r;
