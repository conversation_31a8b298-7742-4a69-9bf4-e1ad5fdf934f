<s-layout wx:if="{{G}}" u-s="{{['d']}}" class="set-userinfo-wrap data-v-2c634ac3" u-i="2c634ac3-0" bind:__l="__l" u-p="{{G}}"><uni-forms wx:if="{{C}}" u-s="{{['d']}}" class="form-box data-v-2c634ac3" u-i="2c634ac3-1,2c634ac3-0" bind:__l="__l" u-p="{{C}}"><view class="bg-white ss-p-x-30 data-v-2c634ac3"><uni-forms-item wx:if="{{c}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-2,2c634ac3-1" bind:__l="__l" u-p="{{c}}"><uni-number-box wx:if="{{b}}" class="data-v-2c634ac3" u-i="2c634ac3-3,2c634ac3-2" bind:__l="__l" bindupdateModelValue="{{a}}" u-p="{{b}}"/></uni-forms-item><uni-forms-item wx:if="{{f}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-4,2c634ac3-1" bind:__l="__l" u-p="{{f}}"><uni-data-select wx:if="{{e}}" class="data-v-2c634ac3" u-i="2c634ac3-5,2c634ac3-4" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"/></uni-forms-item><uni-forms-item wx:if="{{i}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-6,2c634ac3-1" bind:__l="__l" u-p="{{i}}"><uni-data-select wx:if="{{h}}" class="data-v-2c634ac3" u-i="2c634ac3-7,2c634ac3-6" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"/></uni-forms-item><uni-forms-item wx:if="{{l}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-8,2c634ac3-1" bind:__l="__l" u-p="{{l}}"><uni-easyinput wx:if="{{k}}" class="data-v-2c634ac3" u-i="2c634ac3-9,2c634ac3-8" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"/></uni-forms-item><uni-forms-item wx:if="{{o}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-10,2c634ac3-1" bind:__l="__l" u-p="{{o}}"><uni-easyinput wx:if="{{n}}" class="data-v-2c634ac3" u-i="2c634ac3-11,2c634ac3-10" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"/></uni-forms-item><uni-forms-item wx:if="{{r}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-12,2c634ac3-1" bind:__l="__l" u-p="{{r}}"><uni-easyinput wx:if="{{q}}" class="data-v-2c634ac3" u-i="2c634ac3-13,2c634ac3-12" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"/></uni-forms-item><uni-forms-item wx:if="{{v}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-14,2c634ac3-1" bind:__l="__l" u-p="{{v}}"><uni-data-select wx:if="{{t}}" class="data-v-2c634ac3" u-i="2c634ac3-15,2c634ac3-14" bind:__l="__l" bindupdateModelValue="{{s}}" u-p="{{t}}"/></uni-forms-item><uni-forms-item wx:if="{{y}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-16,2c634ac3-1" bind:__l="__l" u-p="{{y}}"><uni-easyinput wx:if="{{x}}" class="data-v-2c634ac3" u-i="2c634ac3-17,2c634ac3-16" bind:__l="__l" bindupdateModelValue="{{w}}" u-p="{{x}}"/></uni-forms-item><uni-forms-item wx:if="{{B}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-18,2c634ac3-1" bind:__l="__l" u-p="{{B}}"><uni-data-select wx:if="{{A}}" class="data-v-2c634ac3" u-i="2c634ac3-19,2c634ac3-18" bind:__l="__l" bindupdateModelValue="{{z}}" u-p="{{A}}"/></uni-forms-item></view></uni-forms><su-fixed wx:if="{{D}}" class="data-v-2c634ac3" u-s="{{['d']}}" u-i="2c634ac3-20,2c634ac3-0" bind:__l="__l" u-p="{{F}}"><view class="footer-box ss-p-20 data-v-2c634ac3"><button class="ss-rest-button logout-btn ui-Shadow-Main data-v-2c634ac3" bindtap="{{E}}">保 存</button></view></su-fixed></s-layout>