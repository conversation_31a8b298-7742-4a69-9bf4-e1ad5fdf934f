"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/hooks/useGoods.js"),n=require("../../sheep/index.js"),o=require("../../sheep/api/trade/order.js"),i=require("../../sheep/util/index.js"),s=require("../../sheep/util/invoice.js"),a=require("../../sheep/config/index.js");if(!Array){(e.resolveComponent("su-tabs")+e.resolveComponent("su-sticky")+e.resolveComponent("s-empty")+e.resolveComponent("s-goods-item")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+r+(()=>"../../sheep/components/s-layout/s-layout.js"))();const r=()=>"./components/InvoiceApplicationDialog.js",c={__name:"list",setup(r){const c=e.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:5},loadStatus:""}),d=e.reactive({show:!1,orderId:0}),u=[{name:"全部"},{name:"待付款",value:0},{name:"待发货",value:10},{name:"待收货",value:20},{name:"待评价",value:30}];async function l(t,s=!1){"WechatMiniProgram"!==n.sheep.$platform.name||e.isEmpty(t.wechat_extra_data)||s?e.index.showModal({title:"提示",content:"确认收货吗？",success:async function(e){if(!e.confirm)return;const{code:n}=await o.OrderApi.receiveOrder(t.id);0===n&&(i.resetPagination(c.pagination),await f())}}):function(t){if(!e.wx$1.openBusinessView)return void n.sheep.$helper.toast("请升级微信版本");e.wx$1.openBusinessView({businessType:"weappOrderConfirm",extraData:{merchant_id:"1481069012",merchant_trade_no:t.wechat_extra_data.merchant_trade_no,transaction_id:t.wechat_extra_data.transaction_id},success(e){console.log("success:",e),"openBusinessView:ok"===e.errMsg&&"success"===e.extraData.status&&l(t,!0)},fail(e){console.log("error:",e)},complete(e){console.log("result:",e)}})}(t)}async function p(t){try{const{code:i,data:s}=await o.OrderApi.getInvoiceByOrderId(t);0===i&&s&&s.id?!0===s.reqStatus&&s.invFile?function(t){const o=t.invType?"增值税专用发票":"增值税普通发票";e.index.showActionSheet({title:`发票已开具 - ${o}`,itemList:["查看PDF发票","浏览器下载","查看发票详情"],success:function(o){switch(o.tapIndex){case 0:!async function(t){try{e.index.showLoading({title:"正在打开PDF..."});const o=h(t.invFile),i=await e.index.downloadFile({url:o,header:{}});e.index.hideLoading(),200===i.statusCode?e.index.openDocument({filePath:i.tempFilePath,showMenu:!0,success:function(e){console.log("打开PDF成功")},fail:function(e){console.error("打开PDF失败:",e),n.sheep.$helper.toast("无法打开PDF文件")}}):n.sheep.$helper.toast("PDF文件下载失败")}catch(o){e.index.hideLoading(),console.error("查看PDF失败:",o),n.sheep.$helper.toast("PDF文件加载失败")}}(t);break;case 1:!function(t){const n=h(t.invFile);e.index.setClipboardData({data:n,success:function(){e.index.showModal({title:"下载PDF发票",content:`下载链接已复制！\n发票号：${t.invNo}\n\n请按以下步骤下载：\n1. 打开手机浏览器(如Safari、Chrome等)\n2. 在地址栏粘贴链接\n3. 回车即可下载PDF发票`,confirmText:"我知道了",showCancel:!1})},fail:function(){e.index.showModal({title:"下载PDF发票",content:`请手动复制以下链接：\n${n}\n\n然后：\n1. 打开手机浏览器\n2. 在地址栏粘贴链接\n3. 回车即可下载PDF发票`,confirmText:"我知道了",showCancel:!1})}})}(t);break;case 2:m(t)}}})}(s):m(s):n.sheep.$helper.toast("该订单暂未申请发票")}catch(i){console.error("查看发票失败:",i),n.sheep.$helper.toast("该订单暂未申请发票")}}function h(e){if(!e)throw new Error("PDF文件路径为空");if(e.startsWith("http://")||e.startsWith("https://"))return e;let t=a.staticUrl;t=t.replace(/\/$/,"");return`${t}${e.startsWith("/")?e:"/"+e}`}function m(t){const n=s.formatInvoiceContent(t);e.index.showModal({title:"发票详情",content:n,showCancel:!1,confirmText:"确定"})}function g(){i.resetPagination(c.pagination),f()}async function f(){c.loadStatus="loading";let{code:n,data:i}=await o.OrderApi.getOrderPageByEnterprise({pageNo:c.pagination.pageNo,pageSize:c.pagination.pageSize,status:u[c.currentTab].value,commentStatus:30!==u[c.currentTab].value&&null});0===n&&(i.list.forEach((e=>{t.handleOrderButtons(e),console.log(`订单ID: ${e.id}, 状态: ${e.status}, 支付状态: ${e.payStatus}, 按钮: ${e.buttons.join(",")}`)})),c.pagination.list=e.lodash.concat(c.pagination.list,i.list),c.pagination.total=i.total,c.loadStatus=c.pagination.list.length<c.pagination.total?"more":"noMore")}function w(){"noMore"!==c.loadStatus&&(c.pagination.pageNo++,f())}return e.onLoad((async e=>{e.type&&(c.currentTab=e.type),await f()})),e.onReachBottom((()=>{w()})),e.onPullDownRefresh((()=>{i.resetPagination(c.pagination),f(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),(s,a)=>e.e({},{c:e.p({bgColor:"#fff"}),d:0===c.pagination.total},0===c.pagination.total?{e:e.p({icon:e.unref(n.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无订单"})}:{},{f:c.pagination.total>0},c.pagination.total>0?{g:e.f(c.pagination.list,((s,a,r)=>e.e({a:e.t(s.no),b:e.t(e.unref(t.formatOrderStatus)(s)),c:e.n(e.unref(t.formatOrderColor)(s)),d:e.f(s.items,((t,n,o)=>e.e({},{c:e.t(t.spuName),d:t.id}))),e:e.t(s.enterpriseName),f:e.t(s.legalPersonName),g:e.t(e.unref(i.formatDate)(s.payTime,"YYYY-MM-DD")),h:e.t(s.payTime&&e.unref(i.formatDate)(s.payTime+31536e6,"YYYY-MM-DD"))},{},{k:0!==s.buttons.length},0!==s.buttons.length?e.e({l:s.buttons.includes("combination")},s.buttons.includes("combination")?{m:e.o((e=>function(e){n.sheep.$router.go("/pages/activity/groupon/detail",{id:e.combinationRecordId})}(s)),s.id)}:{},{n:0===s.buttons.length},0===s.buttons.length?{o:e.o((e=>(s.id,!1)),s.id)}:{},{p:s.buttons.includes("confirm")},s.buttons.includes("confirm")?{q:e.o((e=>l(s)),s.id)}:{},{r:s.buttons.includes("express")},s.buttons.includes("express")?{s:e.o((e=>async function(e){n.sheep.$router.go("/pages/order/express/log",{id:e})}(s.id)),s.id)}:{},{t:s.buttons.includes("cancel")},s.buttons.includes("cancel")?{v:e.o((n=>async function(n){e.index.showModal({title:"提示",content:"确定要取消订单吗?",success:async function(e){if(!e.confirm)return;const{code:i}=await o.OrderApi.cancelOrder(n);if(0===i){let e=c.pagination.list.findIndex((e=>e.id===n));const o=c.pagination.list[e];o.status=40,t.handleOrderButtons(o)}}})}(s.id)),s.id)}:{},{w:s.buttons.includes("comment")},s.buttons.includes("comment")?{x:e.o((e=>{return t=s.id,void n.sheep.$router.go("/pages/goods/comment/add",{id:t});var t}),s.id)}:{},{y:s.buttons.includes("delete")},s.buttons.includes("delete")?{z:e.o((t=>{return n=s.id,void e.index.showModal({title:"提示",content:"确定要删除订单吗?",success:async function(e){if(e.confirm){const{code:e}=await o.OrderApi.deleteOrder(n);if(0===e){let e=c.pagination.list.findIndex((e=>e.id===n));c.pagination.list.splice(e,1)}}}});var n}),s.id)}:{},{A:s.buttons.includes("pay")},s.buttons.includes("pay")?{B:e.o((e=>{return t=s.payOrderId,void n.sheep.$router.go("/pages/pay/index",{id:t});var t}),s.id)}:{},{C:s.buttons.includes("applyInvoice")},s.buttons.includes("applyInvoice")?{D:e.o((e=>{return t=s.id,d.orderId=t,void(d.show=!0);var t}),s.id)}:{},{E:s.buttons.includes("viewInvoice")},s.buttons.includes("viewInvoice")?{F:e.o((e=>p(s.id)),s.id)}:{},{G:e.n(s.buttons.length>3?"ss-row-between":"ss-row-right")}):{},{H:s.id,I:e.o((e=>(s.id,!1)),s.id)})))}:{},{h:c.pagination.total>0},c.pagination.total>0?{i:e.o(w),j:e.p({status:c.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{k:e.o((e=>d.show=e)),l:e.o(g),m:e.p({show:d.show,"order-id":d.orderId}),n:e.p({title:"我的订单"})})}},d=e._export_sfc(c,[["__scopeId","data-v-eab5d5a2"]]);wx.createPage(d);
