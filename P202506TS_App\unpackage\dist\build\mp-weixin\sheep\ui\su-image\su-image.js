"use strict";const e=require("../../../common/vendor.js"),r=require("../../index.js"),t={__name:"su-image",props:{src:{type:String,default:""},errorSrc:{type:String,default:"/static/img/shop/empty_network.png"},mode:{type:String,default:"widthFix"},isPreview:{type:Boolean,default:!1},previewList:{type:Array,default:()=>[]},current:{type:Number,default:-1},height:{type:Number,default:0},width:{type:Number,default:0},radius:{type:Number,default:0}},emits:["load","error"],setup(t,{emit:i}){const s=e.reactive({isError:!1,imgHeight:600}),o=t,n=i,u=e.computed((()=>({height:(o.height||s.imgHeight)+"rpx",width:o.width?o.width+"rpx":"100%",borderRadius:o.radius?o.radius+"rpx":""})));function d(e){0===o.height&&(s.imgHeight=e.detail.height/e.detail.width*750)}function a(e){s.isError=!0,n("error",e)}function c(){o.isPreview&&e.index.previewImage({urls:o.previewList.length<1?[o.src]:o.previewList,current:o.current,longPressActions:{itemList:["发送给朋友","保存图片","收藏"],success:function(e){console.log("选中了第"+(e.tapIndex+1)+"个按钮,第"+(e.index+1)+"张图片")},fail:function(e){console.log(e.errMsg)}}})}return(i,o)=>e.e({a:!s.isError},s.isError?{}:{b:e.s(u.value),c:t.mode,d:e.unref(r.sheep).$url.cdn(t.src),e:e.o(c),f:e.o(d),g:e.o(a)})}},i=e._export_sfc(t,[["__scopeId","data-v-74e3b72e"]]);wx.createComponent(i);
