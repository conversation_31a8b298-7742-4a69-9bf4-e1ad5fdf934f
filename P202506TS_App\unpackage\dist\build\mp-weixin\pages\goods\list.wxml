<s-layout wx:if="{{t}}" class="data-v-bfa99b09" u-s="{{['d']}}" bindsearch="{{s}}" u-i="bfa99b09-0" bind:__l="__l" u-p="{{t}}"><su-sticky wx:if="{{e}}" class="data-v-bfa99b09" u-s="{{['d']}}" u-i="bfa99b09-1,bfa99b09-0" bind:__l="__l" u-p="{{e}}"><view class="ss-flex data-v-bfa99b09"><view class="ss-flex-1 data-v-bfa99b09"><su-tabs wx:if="{{b}}" class="data-v-bfa99b09" bindchange="{{a}}" u-i="bfa99b09-2,bfa99b09-1" bind:__l="__l" u-p="{{b}}"/></view><view class="list-icon data-v-bfa99b09" bindtap="{{d}}"><text wx:if="{{c}}" class="sicon-goods-list data-v-bfa99b09"/><text wx:else class="sicon-goods-card data-v-bfa99b09"/></view></view></su-sticky><su-popup wx:if="{{h}}" class="data-v-bfa99b09" u-s="{{['d']}}" bindclose="{{g}}" u-i="bfa99b09-3,bfa99b09-0" bind:__l="__l" u-p="{{h}}"><view class="filter-list-box data-v-bfa99b09"><view wx:for="{{f}}" wx:for-item="item" wx:key="b" class="{{['filter-item', 'data-v-bfa99b09', item.c]}}" bindtap="{{item.d}}">{{item.a}}</view></view></su-popup><view wx:if="{{i}}" class="goods-list ss-m-t-20 data-v-bfa99b09"><view wx:for="{{j}}" wx:for-item="item" wx:key="d" class="ss-p-l-20 ss-p-r-20 ss-m-b-20 data-v-bfa99b09"><s-goods-column wx:if="{{item.c}}" class=" data-v-bfa99b09" bindclick="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"/></view></view><view wx:if="{{k}}" class="ss-flex ss-flex-wrap ss-p-x-20 ss-m-t-20 ss-col-top data-v-bfa99b09"><view class="goods-list-box data-v-bfa99b09"><view wx:for="{{l}}" wx:for-item="item" wx:key="e" class="left-list data-v-bfa99b09"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-bfa99b09" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-bfa99b09" slot="cart"/></s-goods-column></view></view><view class="goods-list-box data-v-bfa99b09"><view wx:for="{{m}}" wx:for-item="item" wx:key="e" class="right-list data-v-bfa99b09"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-bfa99b09" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-bfa99b09" slot="cart"/></s-goods-column></view></view></view><uni-load-more wx:if="{{n}}" class="data-v-bfa99b09" bindtap="{{o}}" u-i="bfa99b09-7,bfa99b09-0" bind:__l="__l" u-p="{{p}}"/><s-empty wx:if="{{q}}" class="data-v-bfa99b09" u-i="bfa99b09-8,bfa99b09-0" bind:__l="__l" u-p="{{r}}"/></s-layout>