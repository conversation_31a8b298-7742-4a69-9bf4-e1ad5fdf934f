"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),s=require("../../sheep/hooks/useGoods.js"),t=require("../../sheep/api/product/spu.js"),n=require("../../sheep/util/const.js"),i=require("../../sheep/api/promotion/point.js");if(!Array){(e.resolveComponent("s-empty")+e.resolveComponent("su-swiper")+e.resolveComponent("s-select-seckill-sku")+e.resolveComponent("s-layout"))()}Math||(c+p+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/ui/su-swiper/su-swiper.js")+a+(()=>"../../sheep/components/s-select-seckill-sku/s-select-seckill-sku.js")+u+d+r+(()=>"../../sheep/components/s-layout/s-layout.js"))();const c=()=>"./components/detail/detail-navbar.js",a=()=>"./components/detail/detail-cell-sku.js",r=()=>"./components/detail/detail-tabbar.js",p=()=>"./components/detail/detail-skeleton.js",u=()=>"./components/detail/detail-comment-card.js",d=()=>"./components/detail/detail-content-card.js",l={__name:"point",setup(c){e.useCssVars((o=>({"54fc01e9":e.unref(a),"24db5cea":e.unref(r),"8defb264":e.unref(p),"1f69a07d":e.unref(u),"023a0ba8":e.unref(d)})));const a=o.sheep.$url.css("/static/img/shop/goods/score-bg.png"),r=o.sheep.$url.css("/static/img/shop/goods/seckill-btn.png"),p=o.sheep.$url.css("/static/img/shop/goods/activity-btn-disabled.png"),u=o.sheep.$url.css("/static/img/shop/goods/seckill-tip-bg.png"),d=o.sheep.$url.css("/static/img/shop/goods/groupon-tip-bg.png");e.onPageScroll((()=>{}));const l=e.reactive({skeletonLoading:!0,goodsInfo:{},showSelectSku:!1,goodsSwiper:[],selectedSku:{},showModel:!1,total:0,price:""});function g(e){l.selectedSku=e}function f(e){o.sheep.$router.go("/pages/order/confirm",{data:JSON.stringify({order_type:"goods",buy_type:"point",pointActivityId:k.value.id,items:[{skuId:e.id,count:e.count}]})})}const m=e.computed((()=>e.isEmpty(e.unref(k))?{}:o.sheep.$platform.share.getShareInfo({title:k.value.name,image:o.sheep.$url.cdn(l.goodsInfo.picUrl),params:{page:n.SharePageEnum.POINT.value,query:k.value.id}},{type:"goods",title:k.value.name,image:o.sheep.$url.cdn(l.goodsInfo.picUrl),price:(h.value.price||0)+` + ${h.value.point} 积分`,marketPrice:s.fen2yuan(l.goodsInfo.marketPrice)}))),k=e.ref(),h=e.computed((()=>{if(!e.isEmpty(l.selectedSku)){const e=l.selectedSku;return{point:e.point,price:e.pointPrice?s.fen2yuan(e.pointPrice):""}}return{point:k.value.point,price:k.value.price?s.fen2yuan(k.value.price):""}}));e.computed((()=>{let o=`￥${s.fen2yuan(l.goodsInfo.price)}`;if(!e.isEmpty(l.selectedSku)){const e=l.selectedSku;o=`${e.point}${e.pointPrice?`+￥${s.fen2yuan(e.pointPrice)}`:""}`}return o}));const y=async e=>{const{data:o}=await t.SpuApi.getSpuDetail(e);o.activity_type=n.PromotionActivityTypeEnum.POINT.type,l.goodsInfo=o,l.goodsInfo.stock=Math.min(o.stock,k.value.stock),l.goodsSwiper=s.formatGoodsSwiper(l.goodsInfo.sliderPicUrls),o.skus.forEach((e=>{const o=k.value.products.find((o=>o.skuId===e.id));o?(e.point=o.point,e.pointPrice=o.price,e.stock=Math.min(e.stock,o.stock),e.limitCount=o.count):e.stock=0})),l.skeletonLoading=!1};return e.onLoad((e=>{e.id?(async e=>{const{data:o}=await i.PointApi.getPointActivity(e);k.value=o,await y(o.spuId)})(e.id):l.goodsInfo=null})),(t,i)=>e.e({a:l.skeletonLoading},l.skeletonLoading?{}:null===l.goodsInfo||l.goodsInfo.activity_type!==e.unref(n.PromotionActivityTypeEnum).POINT.type?{c:e.p({text:"活动不存在或已结束",icon:"/static/soldout-empty.png",showAction:!0,actionText:"再逛逛",actionUrl:"/pages/goods/list"})}:e.e({d:e.p({isPreview:!0,list:l.goodsSwiper,dotStyle:"tag",imageMode:"widthFix",dotCur:"bg-mask-40",seizeHeight:750}),e:e.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),f:e.t(h.value.point),g:e.t(h.value.price&&0!==h.value.price?`+￥${h.value.price}`:""),h:e.t(e.unref(s.formatExchange)(l.goodsInfo.sales_show_type,l.goodsInfo.sales)),i:l.goodsInfo.marketPrice},l.goodsInfo.marketPrice?{j:e.t(e.unref(s.fen2yuan)(l.selectedSku.marketPrice||l.goodsInfo.marketPrice))}:{},{k:e.t(l.goodsInfo.name||""),l:e.t(l.goodsInfo.introduction),m:e.o((e=>l.showSelectSku=!0)),n:e.p({sku:l.selectedSku}),o:e.o(f),p:e.o(g),q:e.o((e=>l.showSelectSku=!1)),r:e.o((e=>l.goodsInfo=e)),s:e.p({show:l.showSelectSku,"single-limit-count":k.value.singleLimitCount,modelValue:l.goodsInfo}),t:e.p({goodsId:l.goodsInfo.id}),v:e.p({content:l.goodsInfo.description}),w:l.goodsInfo.marketPrice},l.goodsInfo.marketPrice?{x:e.t(e.unref(s.fen2yuan)(l.goodsInfo.marketPrice)),y:e.o((s=>e.unref(o.sheep).$router.go("/pages/goods/index",{id:l.goodsInfo.id})))}:{},{z:e.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),A:e.t(h.value.point),B:e.t(h.value.price&&0!==h.value.price?`+￥${h.value.price}`:""),C:0===l.goodsInfo.stock},(l.goodsInfo.stock,{}),{D:e.o((e=>l.showSelectSku=!0)),E:e.n(0!=l.goodsInfo.stock?"check-btn-box":"disabled-btn-box"),F:0===l.goodsInfo.stock,G:e.o((e=>l.goodsInfo=e)),H:e.p({modelValue:l.goodsInfo})}),{b:null===l.goodsInfo||l.goodsInfo.activity_type!==e.unref(n.PromotionActivityTypeEnum).POINT.type,I:e.s(t.__cssVars()),J:e.p({onShareAppMessage:m.value,navbar:"goods"})})}},g=e._export_sfc(l,[["__scopeId","data-v-2133cf21"]]);l.__runtimeHooks=3,wx.createPage(g);
