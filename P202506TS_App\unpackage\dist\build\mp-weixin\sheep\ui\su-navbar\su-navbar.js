"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js");if(require("../../store/index.js"),require("../../helper/index.js"),require("../../request/index.js"),!Array){(e.resolveComponent("su-status-bar")+e.resolveComponent("uni-search-bar"))()}Math||((()=>"../su-status-bar/su-status-bar.js")+(()=>"../../../uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.js"))();const r={__name:"su-navbar",props:{dark:{type:Boolean,default:!1},modelValue:{type:String,default:""},title:{type:String,default:""},titleAlign:{type:String,default:"center"},rightText:{type:String,default:""},leftIcon:{type:String,default:"left"},rightIcon:{type:String,default:""},fixed:{type:[Boolean,String],default:!0},placeholder:{type:[Boolean,String],default:!0},color:{type:String,default:""},backgroundColor:{type:String,default:""},opacity:{type:[Boolean,String],default:!1},opacityBgUi:{type:String,default:"bg-white"},statusBar:{type:[Boolean,String],default:!1},shadow:{type:[Boolean,String],default:!1},border:{type:[Boolean,String],default:!1},height:{type:[Number,String],default:44},leftWidth:{type:[Number,String],default:80},rightWidth:{type:[Number,String],default:0},tools:{type:String,default:"title"},defaultSearch:{type:String,default:""}},emits:["clickLeft","clickRight","clickTitle","search"],setup(r,{emit:l}){const o=e=>"number"==typeof e?e+"px":e,a=l,i=r;e.computed((()=>({width:t.sheep.$platform.capsule.width+"px",height:t.sheep.$platform.capsule.height+"px",margin:"0 "+(t.sheep.$platform.device.windowWidth-t.sheep.$platform.capsule.right)+"px"})));const n=e.computed((()=>i.defaultSearch));e.computed((()=>i.dark?i.backgroundColor?i.backgroundColor:i.dark?"#333":"#FFF":i.backgroundColor||"#FFF"));const u=e.computed((()=>i.dark?i.color?i.color:i.dark?"#fff":"#333":i.color||"#333")),c=e.computed((()=>o(i.height))),s=e.computed((()=>o(i.leftWidth))),d=e.computed((()=>o(i.rightWidth)));function p(e){a("search",e.value)}e.onLoad((()=>{e.index.report&&""!==i.title&&e.index.report("title",i.title)}));const f=t.sheep.$router.hasHistory();function h(){f?t.sheep.$router.back():t.sheep.$router.go("/pages/index/index"),a("clickLeft")}function g(){a("clickTitle")}return(l,o)=>e.e({a:e.n(r.opacity?"":r.opacityBgUi),b:r.statusBar},(r.statusBar,{}),{c:r.leftIcon.length>0},r.leftIcon.length>0?e.e({d:e.unref(f)},(e.unref(f),{}),{e:e.o(h)},{},{}):{},{g:"left"===r.titleAlign&&r.title.length&&"WechatOfficialAccount"!==e.unref(t.sheep).$platform.name},"left"===r.titleAlign&&r.title.length&&"WechatOfficialAccount"!==e.unref(t.sheep).$platform.name?{h:e.t(r.title),i:u.value,j:!r.leftIcon.length>0?1:""}:{},{k:s.value,l:"search"===r.tools},"search"===r.tools?{m:e.o(p),n:e.o((e=>n.value=e)),o:e.p({radius:20,placeholder:"请输入关键词",cancelButton:"none",modelValue:n.value})}:e.e({p:"title"===r.tools&&"center"===r.titleAlign&&r.title.length},"title"===r.tools&&"center"===r.titleAlign&&r.title.length?{q:e.t(r.title),r:u.value}:{},{s:e.o(g)}),{t:d.value,v:u.value,w:c.value,x:r.backgroundColor,y:r.fixed?1:"",z:r.shadow?1:"",A:r.border?1:"",B:r.placeholder},r.placeholder?e.e({C:r.statusBar},(r.statusBar,{}),{D:c.value}):{},{E:r.dark?1:""})}},l=e._export_sfc(r,[["__scopeId","data-v-3f835923"]]);wx.createComponent(l);
