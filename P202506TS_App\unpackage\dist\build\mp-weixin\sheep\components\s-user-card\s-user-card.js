"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),r=require("../../hooks/useModal.js"),o={__name:"s-user-card",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},avatar:{type:String,default:""},nickname:{type:String,default:"请先登录"},vip:{type:[String,Number],default:"1"},collectNum:{type:[String,Number],default:"1"},likeNum:{type:[String,Number],default:"1"}},setup(o){const a=e.reactive({enterpriseInfo:{}}),s=e.computed((()=>t.sheep.$store("user").userInfo));console.log("用户信息",s);const n=e.computed((()=>t.sheep.$url.static(s.value.avatar||"/assets/mp/ai/icon_user.png"))),u=e.computed((()=>t.sheep.$store("user").isLogin)),p=o,i=e.computed((()=>{const{bgType:e,bgImg:t,bgColor:r}=p.styles;return{background:"img"===e?`url(${t}) no-repeat top center / 100% 100%`:r}}));function c(){r.showAuthModal("changeMobile")}return e.onShow((async()=>{a.enterpriseInfo=await e.index.getStorageSync("enterprise")})),(r,p)=>e.e({a:n.value,b:e.o((r=>e.unref(t.sheep).$router.go("/pages/user/info"))),c:e.t(a.enterpriseInfo.name),d:e.t(a.enterpriseInfo.legalPersonName)},{},{f:u.value&&!s.value.mobile},u.value&&!s.value.mobile?{g:e.o(c)}:{},{h:e.s(i.value),i:e.s({marginLeft:`${o.data.space}px`})})}},a=e._export_sfc(o,[["__scopeId","data-v-5534254c"]]);wx.createComponent(a);
