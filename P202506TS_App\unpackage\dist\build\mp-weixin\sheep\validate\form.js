"use strict";const e=require("../helper/test.js"),r={rules:[{required:!0,errorMessage:"请输入手机号"},{validateFunction:function(r,s,t,o){return e.test.mobile(s)||o("手机号码格式不正确"),!0}}]};exports.code={rules:[{required:!0,errorMessage:"请输入验证码"}]},exports.mobile=r,exports.password={rules:[{required:!0,errorMessage:"请输入密码"},{validateFunction:function(e,r,s,t){return/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]+\S{5,12}$/.test(r)||t("需包含字母和数字,长度在6-12之间"),!0}}]};
