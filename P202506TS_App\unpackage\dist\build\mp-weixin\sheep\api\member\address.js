"use strict";const e=require("../../request/index.js"),s={getAddressList:()=>e.request({url:"/member/address/list",method:"GET"}),createAddress:s=>e.request({url:"/member/address/create",method:"POST",data:s,custom:{showSuccess:!0,successMsg:"保存成功"}}),updateAddress:s=>e.request({url:"/member/address/update",method:"PUT",data:s,custom:{showSuccess:!0,successMsg:"更新成功"}}),getAddress:s=>e.request({url:"/member/address/get",method:"GET",params:{id:s}}),deleteAddress:s=>e.request({url:"/member/address/delete",method:"DELETE",params:{id:s}})};exports.AddressApi=s;
