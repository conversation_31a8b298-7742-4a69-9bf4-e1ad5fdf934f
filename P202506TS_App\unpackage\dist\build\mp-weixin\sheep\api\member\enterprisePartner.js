"use strict";const e=require("../../request/index.js"),r={getEnterprisePartnerPage:r=>e.request({url:"/member/enterprise-partner/enterprise-partners",method:"GET",params:r}),getEnterprisePartner:r=>e.request({url:"/member/enterprise-partner/get?id="+r,method:"GET"}),createEnterprisePartner:r=>e.request({url:"/member/enterprise-partner/create",method:"POST",data:r}),updateEnterprisePartner:r=>e.request({url:"/member/enterprise-partner/update",method:"PUT",data:r}),updateEnterprisePartnerStatus:(r,t)=>e.request({url:"/member/enterprise-partner/update-status",method:"PUT",params:{id:r,status:t}}),deleteEnterprisePartner:r=>e.request({url:"/member/enterprise-partner/delete?id="+r,method:"DELETE"}),exportEnterprisePartner:r=>e.request({url:"/member/enterprise-partner/export-excel",method:"GET",params:r})};exports.EnterprisePartnerApi=r;
