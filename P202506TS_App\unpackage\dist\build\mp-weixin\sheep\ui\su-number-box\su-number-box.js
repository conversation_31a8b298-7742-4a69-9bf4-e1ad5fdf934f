"use strict";const t=require("../../../common/vendor.js"),e={name:"UniNumberBox",emits:["change","input","update:modelValue","blur","focus"],props:{value:{type:[Number,String],default:1},modelValue:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},background:{type:String,default:"#f5f5f5"},color:{type:String,default:"#333"},disabled:{type:Boolean,default:!1},activity:{type:String,default:"none"}},data:()=>({inputValue:0}),watch:{value(t){this.inputValue=+t},modelValue(t){this.inputValue=+t}},created(){1===this.value&&(this.inputValue=+this.modelValue),1===this.modelValue&&(this.inputValue=+this.value)},methods:{_calcValue(t){if(this.disabled)return;const e=this._getDecimalScale();let i=this.inputValue*e,u=this.step*e;if("minus"===t){if(i-=u,i<this.min*e)return;i>this.max*e&&(i=this.max*e)}if("plus"===t){if(i+=u,i>this.max*e)return;i<this.min*e&&(i=this.min*e)}this.inputValue=(i/e).toFixed(String(e).length-1),this.$emit("change",+this.inputValue),this.$emit("input",+this.inputValue),this.$emit("update:modelValue",+this.inputValue)},_getDecimalScale(){let t=1;return~~this.step!==this.step&&(t=Math.pow(10,String(this.step).split(".")[1].length)),t},_onBlur(t){this.$emit("blur",t);let e=t.detail.value;if(!e)return;e=+e,e>this.max?e=this.max:e<this.min&&(e=this.min);const i=this._getDecimalScale();this.inputValue=e.toFixed(String(i).length-1),this.$emit("change",+this.inputValue),this.$emit("input",+this.inputValue)},_onFocus(t){this.$emit("focus",t)}}};const i=t._export_sfc(e,[["render",function(e,i,u,a,l,n){return{a:l.inputValue<=u.min||u.disabled?1:"",b:"groupon"===u.activity?1:"",c:"seckill"===u.activity?1:"",d:t.o((t=>n._calcValue("minus"))),e:u.disabled,f:t.o(((...t)=>n._onFocus&&n._onFocus(...t))),g:t.o(((...t)=>n._onBlur&&n._onBlur(...t))),h:u.color,i:l.inputValue,j:t.o((t=>l.inputValue=t.detail.value)),k:l.inputValue>=u.max||u.disabled?1:"",l:"groupon"===u.activity?1:"",m:"seckill"===u.activity?1:"",n:t.o((t=>n._calcValue("plus")))}}],["__scopeId","data-v-2ab05cbe"]]);wx.createComponent(i);
