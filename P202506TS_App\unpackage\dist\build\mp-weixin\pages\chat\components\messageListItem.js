"use strict";const e=require("../../../common/vendor.js"),s=require("../util/constants.js"),n=require("../util/emoji.js"),t=require("../../../sheep/index.js"),r=require("../../../sheep/util/index.js");if(!Array){(e.resolveComponent("mp-html")+e.resolveComponent("su-image"))()}Math||((()=>"../../../uni_modules/mp-html/components/mp-html/mp-html.js")+(()=>"../../../sheep/ui/su-image/su-image.js")+u+a)();const u=()=>"./goods.js",a=()=>"./order.js",m={__name:"messageListItem",props:{message:{type:Object,default:()=>({})},messageIndex:{type:Number,default:0},messageList:{type:Array,default:()=>[]}},setup(u){const a=u,m=e.computed((()=>e=>r.jsonParse(e.content))),o=e.computed((()=>t.sheep.$store("user").userInfo)),p=e.computed((()=>(s,n)=>{if(e.unref(a.messageList)[n+1]){return e.dayjs(e.unref(a.messageList)[n+1].createTime).fromNow()!==e.dayjs(e.unref(s).createTime).fromNow()}return!1}));function g(e){let s=e;if("object"!=typeof s){let e=/\[(.+?)]/g,r=s.match(e);r&&r.forEach((e=>{let r=function(e){for(let s in n.emojiList)if(n.emojiList[s].name===e)return n.emojiList[s].file;return!1}(e);s=s.replace(e,`<img class="chat-img" style="width: 24px;height: 24px;margin: 0 3px;vertical-align: middle;" src="${t.sheep.$url.cdn("/static/img/chat/emoji/"+r)}"/>`)}))}return s}return(n,a)=>e.e({a:u.message.contentType!==e.unref(s.KeFuMessageContentTypeEnum).SYSTEM&&p.value(u.message,u.messageIndex)},u.message.contentType!==e.unref(s.KeFuMessageContentTypeEnum).SYSTEM&&p.value(u.message,u.messageIndex)?{b:e.t(e.unref(r.formatDate)(u.message.createTime))}:{},{c:u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).SYSTEM},u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).SYSTEM?{d:e.t(u.message.content)}:{},{e:u.message.contentType!==e.unref(s.KeFuMessageContentTypeEnum).SYSTEM},u.message.contentType!==e.unref(s.KeFuMessageContentTypeEnum).SYSTEM?e.e({f:u.message.senderType===e.unref(s.UserTypeEnum).ADMIN,g:e.unref(t.sheep).$url.cdn(u.message.senderAvatar)||e.unref(t.sheep).$url.static("/static/img/shop/chat/default.png"),h:u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).TEXT},u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).TEXT?{i:e.p({content:g(m.value(u.message).text||u.message.content)}),j:u.message.senderType===e.unref(s.UserTypeEnum).ADMIN?1:""}:{},{k:u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).IMAGE},u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).IMAGE?{l:e.p({isPreview:!0,previewList:[e.unref(t.sheep).$url.cdn(m.value(u.message).picUrl||u.message.content)],current:0,src:e.unref(t.sheep).$url.cdn(m.value(u.message).picUrl||u.message.content),height:200,width:200,mode:"aspectFill"}),m:u.message.senderType===e.unref(s.UserTypeEnum).ADMIN?1:""}:{},{n:u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).PRODUCT},u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).PRODUCT?{o:e.o((s=>e.unref(t.sheep).$router.go("/pages/goods/index",{id:m.value(u.message).spuId}))),p:e.p({goodsData:m.value(u.message)})}:{},{q:u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).ORDER},u.message.contentType===e.unref(s.KeFuMessageContentTypeEnum).ORDER?{r:e.o((s=>e.unref(t.sheep).$router.go("/pages/order/detail",{id:m.value(u.message).id}))),s:e.p({orderData:m.value(u.message)})}:{},{t:u.message.senderType===e.unref(s.UserTypeEnum).MEMBER},u.message.senderType===e.unref(s.UserTypeEnum).MEMBER?{v:e.unref(t.sheep).$url.cdn(o.value.avatar)||e.unref(t.sheep).$url.static("/static/img/shop/chat/default.png")}:{},{w:e.n(u.message.senderType===e.unref(s.UserTypeEnum).ADMIN?"ss-row-left":u.message.senderType===e.unref(s.UserTypeEnum).MEMBER?"ss-row-right":"")}):{})}},o=e._export_sfc(m,[["__scopeId","data-v-b7392727"]]);wx.createComponent(o);
