<s-layout wx:if="{{N}}" u-s="{{['d']}}" class="set-userinfo-wrap data-v-190a72a4" u-i="190a72a4-0" bind:__l="__l" u-p="{{N}}"><uni-forms wx:if="{{x}}" u-s="{{['d']}}" class="form-box data-v-190a72a4" u-i="190a72a4-1,190a72a4-0" bind:__l="__l" u-p="{{x}}"><view class="ss-flex ss-row-center ss-col-center ss-p-t-60 ss-p-b-0 bg-white data-v-190a72a4"><view class="header-box-content data-v-190a72a4"><su-image wx:if="{{a}}" class="content-img data-v-190a72a4" u-i="190a72a4-2,190a72a4-1" bind:__l="__l" u-p="{{a}}"/><view class="avatar-action data-v-190a72a4"><button class="ss-reset-button avatar-action-btn data-v-190a72a4" open-type="chooseAvatar" bindchooseavatar="{{b}}"> 修改 </button></view></view></view><view class="bg-white ss-p-x-30 data-v-190a72a4"><uni-forms-item wx:if="{{e}}" class="data-v-190a72a4" u-s="{{['d']}}" u-i="190a72a4-3,190a72a4-1" bind:__l="__l" u-p="{{e}}"><uni-easyinput wx:if="{{d}}" class="data-v-190a72a4" u-i="190a72a4-4,190a72a4-3" bind:__l="__l" bindupdateModelValue="{{c}}" u-p="{{d}}"/></uni-forms-item><uni-forms-item wx:if="{{h}}" class="data-v-190a72a4" u-s="{{['d']}}" u-i="190a72a4-5,190a72a4-1" bind:__l="__l" u-p="{{h}}"><view class="ss-flex ss-col-center ss-h-100 data-v-190a72a4"><radio-group bindchange="{{g}}" class="ss-flex ss-col-center data-v-190a72a4"><label wx:for="{{f}}" wx:for-item="item" wx:key="d" class="radio data-v-190a72a4"><view class="ss-flex ss-col-center ss-m-r-32 data-v-190a72a4"><radio class="data-v-190a72a4" value="{{item.a}}" color="var(--ui-BG-Main)" style="transform:scale(0.8)" checked="{{item.b}}"/><view class="gender-name data-v-190a72a4">{{item.c}}</view></view></label></radio-group></view></uni-forms-item><uni-forms-item wx:if="{{n}}" class="data-v-190a72a4" u-s="{{['d']}}" bindtap="{{m}}" u-i="190a72a4-6,190a72a4-1" bind:__l="__l" u-p="{{n}}"><uni-easyinput wx:if="{{l}}" class="data-v-190a72a4" u-s="{{['right']}}" u-i="190a72a4-7,190a72a4-6" bind:__l="__l" bindupdateModelValue="{{k}}" u-p="{{l}}"><view class="ss-flex ss-col-center data-v-190a72a4" slot="right"><su-radio wx:if="{{i}}" class="data-v-190a72a4" u-i="190a72a4-8,190a72a4-7" bind:__l="__l" u-p="{{j}}"/><button wx:else class="ss-reset-button ss-flex ss-col-center ss-row-center data-v-190a72a4"><text class="_icon-forward data-v-190a72a4" style="color:#bbbbbb;font-size:26rpx"></text></button></view></uni-easyinput></uni-forms-item><uni-forms-item wx:if="{{false}}" class="data-v-190a72a4" u-s="{{['d']}}" bindtap="{{s}}" u-i="190a72a4-9,190a72a4-1" bind:__l="__l" u-p="{{t}}"><uni-easyinput wx:if="{{r}}" class="data-v-190a72a4" u-s="{{['right']}}" u-i="190a72a4-10,190a72a4-9" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"><view class="ss-flex ss-col-center data-v-190a72a4" slot="right"><su-radio wx:if="{{o}}" class="ss-flex data-v-190a72a4" u-i="190a72a4-11,190a72a4-10" bind:__l="__l" u-p="{{p}}"/><button wx:else class="ss-reset-button ss-flex ss-col-center ss-row-center data-v-190a72a4"><text class="_icon-forward data-v-190a72a4" style="color:#bbbbbb;font-size:26rpx"/></button></view></uni-easyinput></uni-forms-item></view><view wx:if="{{false}}" class="bg-white ss-m-t-14 data-v-190a72a4"><uni-list class="data-v-190a72a4" u-s="{{['d']}}" u-i="190a72a4-12,190a72a4-1" bind:__l="__l"><uni-list-item wx:if="{{w}}" bindtap="{{v}}" class="list-border data-v-190a72a4" u-i="190a72a4-13,190a72a4-12" bind:__l="__l" u-p="{{w}}"/></uni-list></view></uni-forms><view wx:if="{{y}}" class="data-v-190a72a4"><view class="title-box ss-p-l-30 data-v-190a72a4">第三方账号绑定</view><view class="account-list ss-flex ss-row-between data-v-190a72a4"><view wx:if="{{z}}" class="ss-flex ss-col-center data-v-190a72a4"><image class="list-img data-v-190a72a4" src="{{A}}"/><text class="list-name data-v-190a72a4">微信公众号</text></view><view wx:if="{{B}}" class="ss-flex ss-col-center data-v-190a72a4"><image class="list-img data-v-190a72a4" src="{{C}}"/><text class="list-name data-v-190a72a4">微信小程序</text></view><view wx:if="{{D}}" class="ss-flex ss-col-center data-v-190a72a4"><image class="list-img data-v-190a72a4" src="{{E}}"/><text class="list-name data-v-190a72a4">微信开放平台</text></view><view class="ss-flex ss-col-center data-v-190a72a4"><view wx:if="{{F}}" class="info ss-flex ss-col-center data-v-190a72a4"><image class="avatar ss-m-r-20 data-v-190a72a4" src="{{G}}"/><text class="name data-v-190a72a4">{{H}}</text></view><view class="bind-box ss-m-l-20 data-v-190a72a4"><button wx:if="{{I}}" class="ss-reset-button relieve-btn data-v-190a72a4" bindtap="{{J}}"> 解绑 </button><button wx:else class="ss-reset-button bind-btn data-v-190a72a4" bindtap="{{K}}">绑定</button></view></view></view></view><su-fixed wx:if="{{M}}" class="data-v-190a72a4" u-s="{{['d']}}" u-i="190a72a4-14,190a72a4-0" bind:__l="__l" u-p="{{M}}"><view class="footer-box ss-p-20 data-v-190a72a4"><button class="ss-rest-button logout-btn ui-Shadow-Main data-v-190a72a4" bindtap="{{L}}">保存</button></view></su-fixed></s-layout>