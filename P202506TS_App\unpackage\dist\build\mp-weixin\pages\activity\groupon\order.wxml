<s-layout wx:if="{{k}}" class="data-v-0284b1d7" u-s="{{['d']}}" u-i="0284b1d7-0" bind:__l="__l" u-p="{{k}}"><su-sticky wx:if="{{c}}" class="data-v-0284b1d7" u-s="{{['d']}}" u-i="0284b1d7-1,0284b1d7-0" bind:__l="__l" u-p="{{c}}"><su-tabs wx:if="{{b}}" class="data-v-0284b1d7" bindchange="{{a}}" u-i="0284b1d7-2,0284b1d7-1" bind:__l="__l" u-p="{{b}}"></su-tabs></su-sticky><s-empty wx:if="{{d}}" class="data-v-0284b1d7" u-i="0284b1d7-3,0284b1d7-0" bind:__l="__l" u-p="{{e}}"/><view wx:if="{{f}}" class="data-v-0284b1d7"><view wx:for="{{g}}" wx:for-item="record" wx:key="k" class="order-list-card-box bg-white ss-r-10 ss-m-t-14 ss-m-20 data-v-0284b1d7"><view class="order-card-header ss-flex ss-col-center ss-row-between ss-p-x-20 data-v-0284b1d7"><view class="order-no data-v-0284b1d7">拼团编号：{{record.a}}</view><view class="{{['ss-font-26', 'data-v-0284b1d7', record.c]}}">{{record.b}}</view></view><view class="border-bottom data-v-0284b1d7"><s-goods-item wx:if="{{record.f}}" class="data-v-0284b1d7" u-s="{{['groupon']}}" u-i="{{record.e}}" bind:__l="__l" u-p="{{record.f}}"><view class="ss-flex data-v-0284b1d7" slot="groupon"><view class="sales-title data-v-0284b1d7">{{record.d}} 人团 </view></view></s-goods-item></view><view class="order-card-footer ss-flex ss-row-right ss-p-x-20 data-v-0284b1d7"><button class="detail-btn ss-reset-button data-v-0284b1d7" bindtap="{{record.g}}"> 订单详情 </button><button class="{{['tool-btn', 'ss-reset-button', 'data-v-0284b1d7', record.i && 'ui-BG-Main-Gradient']}}" bindtap="{{record.j}}">{{record.h}}</button></view></view></view><uni-load-more wx:if="{{h}}" class="data-v-0284b1d7" bindtap="{{i}}" u-i="0284b1d7-5,0284b1d7-0" bind:__l="__l" u-p="{{j}}"/></s-layout>