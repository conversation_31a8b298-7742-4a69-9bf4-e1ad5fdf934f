<s-layout wx:if="{{h}}" class="data-v-56189650" u-s="{{['d']}}" u-i="56189650-0" bind:__l="__l" u-p="{{h}}"><view wx:if="{{a}}" class="contract-content ss-m-x-20 ss-m-t-20 data-v-56189650"><uni-list class="data-v-56189650" u-s="{{['d']}}" u-i="56189650-1,56189650-0" bind:__l="__l"><uni-list-item wx:for="{{b}}" wx:for-item="item" wx:key="i" u-s="{{['body']}}" class="ss-radius-20 ss-m-b-20 data-v-56189650" style="background-color:#fff;overflow:hidden" u-i="{{item.j}}" bind:__l="__l"><view slot="body"><view class="ss-flex-col data-v-56189650"><view class="contract-text data-v-56189650">报告ID：{{item.a}}</view><view class="contract-text data-v-56189650"> 检测类型：{{item.b}}</view><view class="contract-text data-v-56189650"> 检测完成时间：{{item.c}}</view><view class="contract-text data-v-56189650"> 报告是否已生成：{{item.d}}</view></view><view class="ss-m-t-20 ss-flex ss-flex-wrap ss-col-center data-v-56189650" style="gap:20rpx"><button wx:if="{{item.e}}" class="tool-btn ss-reset-button data-v-56189650" catchtap="{{item.f}}"> 查看报告 </button><button wx:elif="{{item.g}}" class="tool-btn ss-reset-button data-v-56189650" catchtap="{{item.h}}"> 下载 </button></view></view></uni-list-item></uni-list></view><uni-load-more wx:if="{{c}}" class="data-v-56189650" bindtap="{{d}}" u-i="56189650-3,56189650-0" bind:__l="__l" u-p="{{e}}"/><s-empty wx:if="{{f}}" class="data-v-56189650" u-i="56189650-4,56189650-0" bind:__l="__l" u-p="{{g}}"/></s-layout>