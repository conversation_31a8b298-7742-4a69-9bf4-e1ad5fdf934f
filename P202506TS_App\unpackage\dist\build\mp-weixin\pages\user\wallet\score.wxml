<s-layout wx:if="{{p}}" u-s="{{['d']}}" class="wallet-wrap data-v-6494e4ec" u-i="6494e4ec-0" bind:__l="__l" u-p="{{p}}"><view class="header-box ss-flex ss-flex-col ss-row-center ss-col-center data-v-6494e4ec" style="{{b}}"><view class="header-bg data-v-6494e4ec"><view class="bg data-v-6494e4ec"/></view><view class="score-box ss-flex-col ss-row-center ss-col-center data-v-6494e4ec"><view class="ss-m-b-30 data-v-6494e4ec"><text class="all-title ss-m-r-8 data-v-6494e4ec">当前积分</text></view><text class="all-num data-v-6494e4ec">{{a}}</text></view></view><su-sticky wx:if="{{i}}" class="data-v-6494e4ec" u-s="{{['d']}}" u-i="6494e4ec-1,6494e4ec-0" bind:__l="__l" u-p="{{i}}"><view class="filter-box ss-p-x-30 ss-flex ss-col-center ss-row-between data-v-6494e4ec"><uni-datetime-picker wx:if="{{f}}" class="data-v-6494e4ec" u-s="{{['d']}}" bindchange="{{d}}" u-i="6494e4ec-2,6494e4ec-1" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"><button class="ss-reset-button date-btn data-v-6494e4ec"><text class="data-v-6494e4ec">{{c}}</text><text class="cicon-drop-down ss-seldate-icon data-v-6494e4ec"></text></button></uni-datetime-picker></view><su-tabs wx:if="{{h}}" class="data-v-6494e4ec" bindchange="{{g}}" u-i="6494e4ec-3,6494e4ec-1" bind:__l="__l" u-p="{{h}}"></su-tabs></su-sticky><view class="list-box data-v-6494e4ec"><view wx:if="{{j}}" class="data-v-6494e4ec"><view wx:for="{{k}}" wx:for-item="item" wx:key="g" class="list-item ss-flex ss-col-center ss-row-between data-v-6494e4ec"><view class="ss-flex-col data-v-6494e4ec"><view class="name data-v-6494e4ec">{{item.a}}{{item.b}}</view><view class="time data-v-6494e4ec">{{item.c}}</view></view><view wx:if="{{item.d}}" class="add data-v-6494e4ec">+{{item.e}}</view><view wx:else class="minus data-v-6494e4ec">{{item.f}}</view></view></view><s-empty wx:else class="data-v-6494e4ec" u-i="6494e4ec-4,6494e4ec-0" bind:__l="__l" u-p="{{l||''}}"/></view><uni-load-more wx:if="{{m}}" class="data-v-6494e4ec" bindtap="{{n}}" u-i="6494e4ec-5,6494e4ec-0" bind:__l="__l" u-p="{{o}}"/></s-layout>