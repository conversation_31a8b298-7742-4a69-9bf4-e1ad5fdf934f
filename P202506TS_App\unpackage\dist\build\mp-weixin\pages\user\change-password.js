"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),s=require("../../sheep/api/member/auth.js"),r=require("../../sheep/api/member/user.js");if(!Array){(e.resolveComponent("uni-easyinput")+e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-forms")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const n={__name:"change-password",setup(n){const a=e.ref(null),d=e.reactive({model:{code:"",password:"",confirmPassword:""},rules:{code:{rules:[{required:!0,errorMessage:"请输入验证码"},{pattern:/^\d{4,6}$/,errorMessage:"验证码格式不正确"}]},password:{rules:[{required:!0,errorMessage:"请输入新密码"},{minLength:6,maxLength:20,errorMessage:"密码长度为6-20位"}]},confirmPassword:{rules:[{required:!0,errorMessage:"请确认密码"},{validateFunction:function(e,o,s,r){return o!==s.password&&r("两次输入的密码不一致"),!0}}]}},isSending:!1,countdown:0}),t=async()=>{if(!(d.isSending||d.countdown>0))try{d.isSending=!0;const{code:e}=await s.AuthUtil.sendSmsCode("",3);if(0===e){d.countdown=60;const e=setInterval((()=>{d.countdown--,d.countdown<=0&&clearInterval(e)}),1e3)}}catch(e){console.error("发送验证码失败:",e),o.sheep.$helper.toast("发送验证码失败，请重试")}finally{d.isSending=!1}},i=async()=>{if(await e.unref(a).validate().catch((e=>{console.log("表单验证失败: ",e)})))try{const{code:s}=await r.UserApi.updateUserPassword({code:d.model.code,password:d.model.password});0===s&&(o.sheep.$helper.toast("密码修改成功"),setTimeout((()=>{e.index.navigateBack()}),1500))}catch(s){console.error("修改密码失败:",s),o.sheep.$helper.toast("修改密码失败，请重试")}};return(o,s)=>({a:e.t(d.isSending?"发送中...":d.countdown>0?`${d.countdown}s后重发`:"获取验证码"),b:d.isSending||d.countdown>0?1:"",c:d.isSending||d.countdown>0,d:e.o(t),e:e.o((e=>d.model.code=e)),f:e.p({placeholder:"请输入手机验证码",type:"number",maxlength:"6",modelValue:d.model.code}),g:e.p({name:"code",label:"手机验证码",required:!0}),h:e.o((e=>d.model.password=e)),i:e.p({type:"password",placeholder:"请输入新密码（6-20位）",maxlength:"20",modelValue:d.model.password}),j:e.p({name:"password",label:"新密码",required:!0}),k:e.o((e=>d.model.confirmPassword=e)),l:e.p({type:"password",placeholder:"请再次输入新密码",maxlength:"20",modelValue:d.model.confirmPassword}),m:e.p({name:"confirmPassword",label:"确认密码",required:!0}),n:e.o(i),o:e.sr(a,"fdf46d5d-1,fdf46d5d-0",{k:"changePasswordRef"}),p:e.o((e=>d.model=e)),q:e.p({rules:d.rules,validateTrigger:"bind",labelPosition:"top",labelWidth:"260",modelValue:d.model}),r:e.p({title:"修改密码",navbar:"normal",bgStyle:{color:"#fff"}})})}},a=e._export_sfc(n,[["__scopeId","data-v-fdf46d5d"]]);wx.createPage(a);
