"use strict";const e=require("./app.js"),s=require("./cart.js"),_=require("./modal.js"),r=require("./sys.js"),i=require("./user.js"),t=require("../../common/vendor.js"),o=Object.assign({"./app.js":e.__vite_glob_0_0,"./cart.js":s.__vite_glob_0_1,"./modal.js":_.__vite_glob_0_2,"./sys.js":r.__vite_glob_0_3,"./user.js":i.__vite_glob_0_4}),a={};Object.keys(o).forEach((e=>{a[e.replace(/(.*\/)*([^.]+).*/gi,"$2")]=o[e].default}));exports.$store=e=>a[e](),exports.setupPinia=e=>{const s=t.createPinia();s.use(t.index$1),e.use(s)};
