"use strict";
/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
function e(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const t={},n=[],r=()=>{},o=()=>!1,i=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),s=e=>e.startsWith("onUpdate:"),a=Object.assign,c=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},u=Object.prototype.hasOwnProperty,l=(e,t)=>u.call(e,t),f=Array.isArray,h=e=>"[object Map]"===b(e),p=e=>"[object Set]"===b(e),d=e=>"function"==typeof e,g=e=>"string"==typeof e,v=e=>"symbol"==typeof e,m=e=>null!==e&&"object"==typeof e,y=e=>(m(e)||d(e))&&d(e.then)&&d(e.catch),_=Object.prototype.toString,b=e=>_.call(e),w=e=>"[object Object]"===b(e),x=e=>g(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,S=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),O=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},k=/-(\w)/g,T=O((e=>e.replace(k,((e,t)=>t?t.toUpperCase():"")))),A=/\B([A-Z])/g,I=O((e=>e.replace(A,"-$1").toLowerCase())),P=O((e=>e.charAt(0).toUpperCase()+e.slice(1))),j=O((e=>e?`on${P(e)}`:"")),$=(e,t)=>!Object.is(e,t),E=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},L=e=>{const t=parseFloat(e);return isNaN(t)?e:t};function C(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=g(r)?N(r):C(r);if(o)for(const e in o)t[e]=o[e]}return t}if(g(e)||m(e))return e}const M=/;(?![^(]*\))/g,R=/:([^]+)/,D=/\/\*[^]*?\*\//g;function N(e){const t={};return e.replace(D,"").split(M).forEach((e=>{if(e){const n=e.split(R);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function B(e){let t="";if(g(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const r=B(e[n]);r&&(t+=r+" ")}else if(m(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const U=(e,t)=>t&&t.__v_isRef?U(e,t.value):h(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],r)=>(e[F(t,r)+" =>"]=n,e)),{})}:p(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>F(e)))}:v(t)?F(t):!m(t)||f(t)||w(t)?t:String(t),F=(e,t="")=>{var n;return v(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};function q(e,t=null){let n;return(...r)=>(e&&(n=e.apply(t,r),e=null),n)}function W(e,t){if(!g(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let r=n[0];return e||(e={}),1===n.length?e[r]:W(e[r],n.slice(1).join("."))}function H(e){let t={};return w(e)&&Object.keys(e).sort().forEach((n=>{const r=n;t[r]=e[r]})),Object.keys(t)?t:e}const z=/:/g;const V=encodeURIComponent;function K(e,t=V){const n=e?Object.keys(e).map((n=>{let r=e[n];return void 0===typeof r||null===r?r="":w(r)&&(r=JSON.stringify(r)),t(n)+"="+t(r)})).filter((e=>e.length>0)).join("&"):null;return n?`?${n}`:""}const J=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const Y=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],G=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function Q(e,t,n=!0){return!(n&&!d(t))&&(Y.indexOf(e)>-1||0===e.indexOf("on"))}let Z;const X=[];const ee=q(((e,t)=>t(e))),te=function(){};te.prototype={_id:1,on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var r=this;function o(){r.off(e,o),t.apply(n,arguments)}return o._=t,this.on(e,o,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,o=n.length;r<o;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],o=[];if(r&&t){for(var i=r.length-1;i>=0;i--)if(r[i].fn===t||r[i].fn._===t||r[i]._id===t){r.splice(i,1);break}o=r}return o.length?n[e]=o:delete n[e],this}};var ne=te;const re=["{","}"];const oe=/^(?:\d)+/,ie=/^(?:\w)+/;const se=Object.prototype.hasOwnProperty,ae=(e,t)=>se.call(e,t),ce=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=re){if(!t)return[e];let r=this._caches[e];return r||(r=function(e,[t,n]){const r=[];let o=0,i="";for(;o<e.length;){let s=e[o++];if(s===t){i&&r.push({type:"text",value:i}),i="";let t="";for(s=e[o++];void 0!==s&&s!==n;)t+=s,s=e[o++];const a=s===n,c=oe.test(t)?"list":a&&ie.test(t)?"named":"unknown";r.push({value:t,type:c})}else i+=s}return i&&r.push({type:"text",value:i}),r}(e,n),this._caches[e]=r),function(e,t){const n=[];let r=0;const o=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===o)return n;for(;r<e.length;){const i=e[r];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===o&&n.push(t[i.value])}r++}return n}(r,t)}};function ue(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let r=["en","fr","es"];t&&Object.keys(t).length>0&&(r=Object.keys(t));const o=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,r);return o||void 0}class le{constructor({locale:e,fallbackLocale:t,messages:n,watcher:r,formater:o}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=o||ce,this.messages=n||{},this.setLocale(e||"en"),r&&this.watchLocale(r)}setLocale(e){const t=this.locale;this.locale=ue(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach((e=>{ae(r,e)||(r[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let r=this.message;return"string"==typeof t?(t=ue(t,this.messages))&&(r=this.messages[t]):n=t,ae(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function fe(e){return function(){try{return e.apply(e,arguments)}catch(G_){console.error(G_)}}}let he=1;const pe={};function de(e,t,n){if("number"==typeof e){const r=pe[e];if(r)return r.keepAlive||delete pe[e],r.callback(t,n)}return t}const ge="success",ve="fail",me="complete";function ye(e,t={},{beforeAll:n,beforeSuccess:r}={}){w(t)||(t={});const{success:o,fail:i,complete:s}=function(e){const t={};for(const n in e){const r=e[n];d(r)&&(t[n]=fe(r),delete e[n])}return t}(t),a=d(o),c=d(i),u=d(s),l=he++;return function(e,t,n,r=!1){pe[e]={name:t,keepAlive:r,callback:n}}(l,e,(l=>{(l=l||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(l.errMsg,e),d(n)&&n(l),l.errMsg===e+":ok"?(d(r)&&r(l,t),a&&o(l)):c&&i(l),u&&s(l)})),l}const _e="success",be="fail",we="complete",xe={},Se={};function Oe(e,t){return function(n){return e(n,t)||n}}function ke(e,t,n){let r=!1;for(let o=0;o<e.length;o++){const i=e[o];if(r)r=Promise.resolve(Oe(i,n));else{const e=i(t,n);if(y(e)&&(r=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return r||{then:e=>e(t),catch(){}}}function Te(e,t={}){return[_e,be,we].forEach((n=>{const r=e[n];if(!f(r))return;const o=t[n];t[n]=function(e){ke(r,e,t).then((e=>d(o)&&o(e)||e))}})),t}function Ae(e,t){const n=[];f(xe.returnValue)&&n.push(...xe.returnValue);const r=Se[e];return r&&f(r.returnValue)&&n.push(...r.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Ie(e){const t=Object.create(null);Object.keys(xe).forEach((e=>{"returnValue"!==e&&(t[e]=xe[e].slice())}));const n=Se[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Pe(e,t,n,r){const o=Ie(e);if(o&&Object.keys(o).length){if(f(o.invoke)){return ke(o.invoke,n).then((n=>t(Te(Ie(e),n),...r)))}return t(Te(o,n),...r)}return t(n,...r)}function je(e,t){return(n={},...r)=>function(e){return!(!w(e)||![ge,ve,me].find((t=>d(e[t]))))}(n)?Ae(e,Pe(e,t,n,r)):Ae(e,new Promise(((o,i)=>{Pe(e,t,a(n,{success:o,fail:i}),r)})))}function $e(e,t,n,r={}){const o=t+":fail";let i="";return i=n?0===n.indexOf(o)?n:o+" "+n:o,delete r.errCode,de(e,a({errMsg:i},r))}function Ee(e,t,n,r){const o=function(e,t){e[0]}(t);if(o)return o}function Le(e,t,n,r){return n=>{const o=ye(e,n,r),i=Ee(0,[n]);return i?$e(o,e,i):t(n,{resolve:t=>function(e,t,n){return de(e,a(n||{},{errMsg:t+":ok"}))}(o,e,t),reject:(t,n)=>$e(o,e,function(e){return!e||g(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Ce(e,t,n,r){return function(e,t,n,r){return(...e)=>{const n=Ee(0,e);if(n)throw new Error(n);return t.apply(null,e)}}(0,t)}let Me=!1,Re=0,De=0;const Ne=Ce(0,((e,t)=>{if(0===Re&&function(){var e,t;let n,r,o;{const i=(null===(e=wx.getWindowInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync(),s=(null===(t=wx.getDeviceInfo)||void 0===t?void 0:t.call(wx))||wx.getSystemInfoSync();n=i.windowWidth,r=i.pixelRatio,o=s.platform}Re=n,De=r,Me="ios"===o}(),0===(e=Number(e)))return 0;let n=e/750*(t||Re);return n<0&&(n=-n),n=Math.floor(n+1e-4),0===n&&(n=1!==De&&Me?.5:1),e<0?-n:n}));function Be(e,t){Object.keys(t).forEach((n=>{d(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):f(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function Ue(e,t){e&&t&&Object.keys(t).forEach((n=>{const r=e[n],o=t[n];f(r)&&d(o)&&c(r,o)}))}const Fe=Ce(0,((e,t)=>{g(e)&&w(t)?Be(Se[e]||(Se[e]={}),t):w(e)&&Be(xe,e)})),qe=Ce(0,((e,t)=>{g(e)?w(t)?Ue(Se[e],t):delete Se[e]:w(e)&&Ue(xe,e)}));const We=new class{constructor(){this.$emitter=new ne}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},He=Ce(0,((e,t)=>(We.on(e,t),()=>We.off(e,t)))),ze=Ce(0,((e,t)=>(We.once(e,t),()=>We.off(e,t)))),Ve=Ce(0,((e,t)=>{f(e)||(e=e?[e]:[]),e.forEach((e=>{We.off(e,t)}))})),Ke=Ce(0,((e,...t)=>{We.emit(e,...t)}));let Je,Ye,Ge;function Qe(e){try{return JSON.parse(e)}catch(G_){}return e}const Ze=[];function Xe(e,t){Ze.forEach((n=>{n(e,t)})),Ze.length=0}const et=je(tt="getPushClientId",function(e,t,n,r){return Le(e,t,0,r)}(tt,((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===Ge&&(Ge=!1,Je="",Ye="uniPush is not enabled"),Ze.push(((e,r)=>{e?t({cid:e}):n(r)})),void 0!==Je&&Xe(Je,Ye)}))}),0,nt));var tt,nt;const rt=[],ot=/^\$|__f__|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|rpx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,it=/^create|Manager$/,st=["createBLEConnection"],at=["request","downloadFile","uploadFile","connectSocket"],ct=["createBLEConnection"],ut=/^on|^off/;function lt(e){return it.test(e)&&-1===st.indexOf(e)}function ft(e){return ot.test(e)&&-1===ct.indexOf(e)}function ht(e){return-1!==at.indexOf(e)}function pt(e){return!(lt(e)||ft(e)||function(e){return ut.test(e)&&"onPush"!==e}(e))}function dt(e,t){return pt(e)&&d(t)?function(n={},...r){return d(n.success)||d(n.fail)||d(n.complete)?Ae(e,Pe(e,t,n,r)):Ae(e,new Promise(((o,i)=>{Pe(e,t,a({},n,{success:o,fail:i}),r)})))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e&&e()).then((()=>n))),(n=>t.resolve(e&&e()).then((()=>{throw n}))))});const gt=["success","fail","cancel","complete"];const vt=()=>{const e=d(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=ue(n&&n.language?n.language:"en")||"en"}return t}()},mt=[];"undefined"!=typeof global&&(global.getLocale=vt);let yt;function _t(e=wx){return function(t,n){yt=yt||e.getStorageSync("__DC_STAT_UUID"),yt||(yt=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:"__DC_STAT_UUID",data:yt})),n.deviceId=yt}}function bt(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function wt(e,t){let n="",r="";switch(n=e.split(" ")[0]||t,r=e.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows"}return{osName:n,osVersion:r}}function xt(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},r=Object.keys(e),o=t.toLocaleLowerCase();for(let t=0;t<r.length;t++){const i=r[t];if(-1!==o.indexOf(i)){n=e[i];break}}}return n}function St(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function Ot(e){return vt?vt():e}function kt(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const Tt={returnValue:(e,t)=>{bt(e,t),_t()(e,t),function(e,t){const{brand:n="",model:r="",system:o="",language:i="",theme:s,version:c,platform:u,fontSizeSetting:l,SDKVersion:f,pixelRatio:h,deviceOrientation:p}=e,{osName:d,osVersion:g}=wt(o,u);let v=c,m=xt(e,r),y=St(n),_=kt(e),b=p,w=h,x=f;const S=(i||"").replace(/_/g,"-"),O={appId:"__UNI__4C2B047",appName:"天算",appVersion:"2.1.0",appVersionCode:"183",appLanguage:Ot(S),uniCompileVersion:"4.64",uniCompilerVersion:"4.64",uniRuntimeVersion:"4.64",uniPlatform:"mp-weixin",deviceBrand:y,deviceModel:r,deviceType:m,devicePixelRatio:w,deviceOrientation:b,osName:d,osVersion:g,hostTheme:s,hostVersion:v,hostLanguage:S,hostName:_,hostSDKVersion:x,hostFontSizeSetting:l,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};a(t,O)}(e,t)}},At=Tt,It={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const r=e.urls;if(!f(r))return;const o=r.length;return o?(n<0?n=0:n>=o&&(n=o-1),n>0?(t.current=r[n],t.urls=r.filter(((e,t)=>!(t<n)||e!==r[n]))):t.current=r[0],{indicator:!1,loop:!1}):void 0}},Pt={args(e,t){t.alertText=e.title}},jt={returnValue:(e,t)=>{const{brand:n,model:r,system:o="",platform:i=""}=e;let s=xt(e,r),c=St(n);_t()(e,t);const{osName:u,osVersion:l}=wt(o,i);t=H(a(t,{deviceType:s,deviceBrand:c,deviceModel:r,osName:u,osVersion:l}))}},$t={returnValue:(e,t)=>{const{version:n,language:r,SDKVersion:o,theme:i}=e;let s=kt(e),c=(r||"").replace(/_/g,"-");const u={hostVersion:n,hostLanguage:c,hostName:s,hostSDKVersion:o,hostTheme:i,appId:"__UNI__4C2B047",appName:"天算",appVersion:"2.1.0",appVersionCode:"183",appLanguage:Ot(c),isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.64",uniCompilerVersion:"4.64",uniRuntimeVersion:"4.64"};a(t,u)}},Et={returnValue:(e,t)=>{bt(e,t),t=H(a(t,{windowTop:0,windowBottom:0}))}},Lt={args(e){const t=getApp({allowDefault:!0})||{};t.$vm?bo("onError",e,t.$vm.$):(wx.$onErrorHandlers||(wx.$onErrorHandlers=[]),wx.$onErrorHandlers.push(e))}},Ct={args(e){const t=getApp({allowDefault:!0})||{};if(t.$vm){if(e.__weh){const n=t.$vm.$.onError;if(n){const t=n.indexOf(e.__weh);t>-1&&n.splice(t,1)}}}else{if(!wx.$onErrorHandlers)return;const t=wx.$onErrorHandlers.findIndex((t=>t===e));-1!==t&&wx.$onErrorHandlers.splice(t,1)}}},Mt={args(){if(wx.__uni_console__){if(wx.__uni_console_warned__)return;wx.__uni_console_warned__=!0,console.warn("开发模式下小程序日志回显会使用 socket 连接，为了避免冲突，建议使用 SocketTask 的方式去管理 WebSocket 或手动关闭日志回显功能。[详情](https://uniapp.dcloud.net.cn/tutorial/run/mp-log.html)")}}},Rt=Mt,Dt={$on:He,$off:Ve,$once:ze,$emit:Ke,upx2px:Ne,rpx2px:Ne,interceptors:{},addInterceptor:Fe,removeInterceptor:qe,onCreateVueApp:function(e){if(Z)return e(Z);X.push(e)},invokeCreateVueAppHook:function(e){Z=e,X.forEach((t=>t(e)))},getLocale:vt,setLocale:e=>{const t=d(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,mt.forEach((t=>t({locale:e}))),!0)},onLocaleChange:e=>{-1===mt.indexOf(e)&&mt.push(e)},getPushClientId:et,onPushMessage:e=>{-1===rt.indexOf(e)&&rt.push(e)},offPushMessage:e=>{if(e){const t=rt.indexOf(e);t>-1&&rt.splice(t,1)}else rt.length=0},invokePushCallback:function(e){if("enabled"===e.type)Ge=!0;else if("clientId"===e.type)Je=e.cid,Ye=e.errMsg,Xe(Je,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:Qe(e.message)};for(let e=0;e<rt.length;e++){if((0,rt[e])(t),t.stopped)break}}else"click"===e.type&&rt.forEach((t=>{t({type:"click",data:Qe(e.message)})}))},__f__:function(e,t,...n){t&&n.push(t),console[e].apply(console,n)}};const Nt=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],Bt=["lanDebug","router","worklet"],Ut=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function Ft(e){return(!Ut||1154!==Ut.scene||!Bt.includes(e))&&(Nt.indexOf(e)>-1||"function"==typeof wx[e])}function qt(){const e={};for(const t in wx)Ft(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const Wt=["__route__","__wxExparserNodeId__","__wxWebviewId__"],Ht=(zt={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]},function({service:e,success:t,fail:n,complete:r}){let o;zt[e]?(o={errMsg:"getProvider:ok",service:e,provider:zt[e]},d(t)&&t(o)):(o={errMsg:"getProvider:fail:服务["+e+"]不存在"},d(n)&&n(o)),d(r)&&r(o)});var zt;const Vt=qt();Vt.canIUse("getAppBaseInfo")||(Vt.getAppBaseInfo=Vt.getSystemInfoSync),Vt.canIUse("getWindowInfo")||(Vt.getWindowInfo=Vt.getSystemInfoSync),Vt.canIUse("getDeviceInfo")||(Vt.getDeviceInfo=Vt.getSystemInfoSync);let Kt=Vt.getAppBaseInfo&&Vt.getAppBaseInfo();Kt||(Kt=Vt.getSystemInfoSync());const Jt=Kt?Kt.host:null,Yt=Jt&&"SAAASDK"===Jt.env?Vt.miniapp.shareVideoMessage:Vt.shareVideoMessage;var Gt=Object.freeze({__proto__:null,createSelectorQuery:function(){const e=Vt.createSelectorQuery(),t=e.in;return e.in=function(e){return e.$scope?t.call(this,e.$scope):t.call(this,function(e){const t=Object.create(null);return Wt.forEach((n=>{t[n]=e[n]})),t}(e))},e},getProvider:Ht,shareVideoMessage:Yt});const Qt={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var Zt=Object.freeze({__proto__:null,compressImage:Qt,getAppAuthorizeSetting:{returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},getAppBaseInfo:$t,getDeviceInfo:jt,getSystemInfo:Tt,getSystemInfoSync:At,getWindowInfo:Et,offError:Ct,onError:Lt,onSocketMessage:Rt,onSocketOpen:Mt,previewImage:It,redirectTo:{},showActionSheet:Pt});const Xt=qt();var en=function(e,t,n=wx){const r=function(e){function t(e,t,n){return function(o){return t(r(e,o,n))}}function n(e,n,r={},o={},i=!1){if(w(n)){const s=!0===i?n:{};d(r)&&(r=r(n,s)||{});for(const a in n)if(l(r,a)){let t=r[a];d(t)&&(t=t(n[a],n,s)),t?g(t)?s[t]=n[a]:w(t)&&(s[t.name?t.name:a]=t.value):console.warn(`微信小程序 ${e} 暂不支持 ${a}`)}else if(-1!==gt.indexOf(a)){const r=n[a];d(r)&&(s[a]=t(e,r,o))}else i||l(s,a)||(s[a]=n[a]);return s}return d(n)&&(d(r)&&r(n,{}),n=t(e,n,o)),n}function r(t,r,o,i=!1){return d(e.returnValue)&&(r=e.returnValue(t,r)),n(t,r,o,{},i||!1)}return function(t,o){const i=l(e,t);if(!i&&"function"!=typeof wx[t])return o;const s=i||d(e.returnValue)||lt(t)||ht(t),a=i||d(o);if(!i&&!o)return function(){console.error(`微信小程序 暂不支持${t}`)};if(!s||!a)return o;const c=e[t];return function(e,o){let i=c||{};d(c)&&(i=c(e));const s=[e=n(t,e,i.args,i.returnValue)];void 0!==o&&s.push(o);const a=wx[i.name||t].apply(wx,s);return(lt(t)||ht(t))&&a&&!a.__v_skip&&(a.__v_skip=!0),ft(t)?r(t,a,i.returnValue,lt(t)):a}}}(t);return new Proxy({},{get:(t,o)=>l(t,o)?t[o]:l(e,o)?dt(o,e[o]):l(Dt,o)?dt(o,Dt[o]):dt(o,r(o,n[o]))})}(Gt,Zt,Xt);let tn,nn;class rn{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=tn,!e&&tn&&(this.index=(tn.scopes||(tn.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=tn;try{return tn=this,e()}finally{tn=t}}}on(){tn=this}off(){tn=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function on(e){return new rn(e)}function sn(){return tn}class an{constructor(e,t,n,r){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=tn){t&&t.active&&t.effects.push(e)}(this,r)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,dn();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),gn()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=fn,t=nn;try{return fn=!0,nn=this,this._runnings++,cn(this),this.fn()}finally{un(this),this._runnings--,nn=t,fn=e}}stop(){var e;this.active&&(cn(this),un(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function cn(e){e._trackId++,e._depsLength=0}function un(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)ln(e.deps[t],e);e.deps.length=e._depsLength}}function ln(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let fn=!0,hn=0;const pn=[];function dn(){pn.push(fn),fn=!1}function gn(){const e=pn.pop();fn=void 0===e||e}function vn(){hn++}function mn(){for(hn--;!hn&&_n.length;)_n.shift()()}function yn(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&ln(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const _n=[];function bn(e,t,n){vn();for(const r of e.keys()){let n;r._dirtyLevel<t&&(null!=n?n:n=e.get(r)===r._trackId)&&(r._shouldSchedule||(r._shouldSchedule=0===r._dirtyLevel),r._dirtyLevel=t),r._shouldSchedule&&(null!=n?n:n=e.get(r)===r._trackId)&&(r.trigger(),r._runnings&&!r.allowRecurse||2===r._dirtyLevel||(r._shouldSchedule=!1,r.scheduler&&_n.push(r.scheduler)))}mn()}const wn=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},xn=new WeakMap,Sn=Symbol(""),On=Symbol("");function kn(e,t,n){if(fn&&nn){let t=xn.get(e);t||xn.set(e,t=new Map);let r=t.get(n);r||t.set(n,r=wn((()=>t.delete(n)))),yn(nn,r)}}function Tn(e,t,n,r,o,i){const s=xn.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&f(e)){const e=Number(r);s.forEach(((t,n)=>{("length"===n||!v(n)&&n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":f(e)?x(n)&&a.push(s.get("length")):(a.push(s.get(Sn)),h(e)&&a.push(s.get(On)));break;case"delete":f(e)||(a.push(s.get(Sn)),h(e)&&a.push(s.get(On)));break;case"set":h(e)&&a.push(s.get(Sn))}vn();for(const c of a)c&&bn(c,4);mn()}const An=e("__proto__,__v_isRef,__isVue"),In=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(v)),Pn=jn();function jn(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=vr(this);for(let t=0,o=this.length;t<o;t++)kn(n,0,t+"");const r=n[t](...e);return-1===r||!1===r?n[t](...e.map(vr)):r}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){dn(),vn();const n=vr(this)[t].apply(this,e);return mn(),gn(),n}})),e}function $n(e){const t=vr(this);return kn(t,0,e),t.hasOwnProperty(e)}class En{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const r=this._isReadonly,o=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return o;if("__v_raw"===t)return n===(r?o?cr:ar:o?sr:ir).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=f(e);if(!r){if(i&&l(Pn,t))return Reflect.get(Pn,t,n);if("hasOwnProperty"===t)return $n}const s=Reflect.get(e,t,n);return(v(t)?In.has(t):An(t))?s:(r||kn(e,0,t),o?s:Sr(s)?i&&x(t)?s:s.value:m(s)?r?fr(s):lr(s):s)}}class Ln extends En{constructor(e=!1){super(!1,e)}set(e,t,n,r){let o=e[t];if(!this._isShallow){const t=dr(o);if(gr(n)||dr(n)||(o=vr(o),n=vr(n)),!f(e)&&Sr(o)&&!Sr(n))return!t&&(o.value=n,!0)}const i=f(e)&&x(t)?Number(t)<e.length:l(e,t),s=Reflect.set(e,t,n,r);return e===vr(r)&&(i?$(n,o)&&Tn(e,"set",t,n):Tn(e,"add",t,n)),s}deleteProperty(e,t){const n=l(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&Tn(e,"delete",t,void 0),r}has(e,t){const n=Reflect.has(e,t);return v(t)&&In.has(t)||kn(e,0,t),n}ownKeys(e){return kn(e,0,f(e)?"length":Sn),Reflect.ownKeys(e)}}class Cn extends En{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Mn=new Ln,Rn=new Cn,Dn=new Ln(!0),Nn=e=>e,Bn=e=>Reflect.getPrototypeOf(e);function Un(e,t,n=!1,r=!1){const o=vr(e=e.__v_raw),i=vr(t);n||($(t,i)&&kn(o,0,t),kn(o,0,i));const{has:s}=Bn(o),a=r?Nn:n?_r:yr;return s.call(o,t)?a(e.get(t)):s.call(o,i)?a(e.get(i)):void(e!==o&&e.get(t))}function Fn(e,t=!1){const n=this.__v_raw,r=vr(n),o=vr(e);return t||($(e,o)&&kn(r,0,e),kn(r,0,o)),e===o?n.has(e):n.has(e)||n.has(o)}function qn(e,t=!1){return e=e.__v_raw,!t&&kn(vr(e),0,Sn),Reflect.get(e,"size",e)}function Wn(e){e=vr(e);const t=vr(this);return Bn(t).has.call(t,e)||(t.add(e),Tn(t,"add",e,e)),this}function Hn(e,t){t=vr(t);const n=vr(this),{has:r,get:o}=Bn(n);let i=r.call(n,e);i||(e=vr(e),i=r.call(n,e));const s=o.call(n,e);return n.set(e,t),i?$(t,s)&&Tn(n,"set",e,t):Tn(n,"add",e,t),this}function zn(e){const t=vr(this),{has:n,get:r}=Bn(t);let o=n.call(t,e);o||(e=vr(e),o=n.call(t,e)),r&&r.call(t,e);const i=t.delete(e);return o&&Tn(t,"delete",e,void 0),i}function Vn(){const e=vr(this),t=0!==e.size,n=e.clear();return t&&Tn(e,"clear",void 0,void 0),n}function Kn(e,t){return function(n,r){const o=this,i=o.__v_raw,s=vr(i),a=t?Nn:e?_r:yr;return!e&&kn(s,0,Sn),i.forEach(((e,t)=>n.call(r,a(e),a(t),o)))}}function Jn(e,t,n){return function(...r){const o=this.__v_raw,i=vr(o),s=h(i),a="entries"===e||e===Symbol.iterator&&s,c="keys"===e&&s,u=o[e](...r),l=n?Nn:t?_r:yr;return!t&&kn(i,0,c?On:Sn),{next(){const{value:e,done:t}=u.next();return t?{value:e,done:t}:{value:a?[l(e[0]),l(e[1])]:l(e),done:t}},[Symbol.iterator](){return this}}}}function Yn(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Gn(){const e={get(e){return Un(this,e)},get size(){return qn(this)},has:Fn,add:Wn,set:Hn,delete:zn,clear:Vn,forEach:Kn(!1,!1)},t={get(e){return Un(this,e,!1,!0)},get size(){return qn(this)},has:Fn,add:Wn,set:Hn,delete:zn,clear:Vn,forEach:Kn(!1,!0)},n={get(e){return Un(this,e,!0)},get size(){return qn(this,!0)},has(e){return Fn.call(this,e,!0)},add:Yn("add"),set:Yn("set"),delete:Yn("delete"),clear:Yn("clear"),forEach:Kn(!0,!1)},r={get(e){return Un(this,e,!0,!0)},get size(){return qn(this,!0)},has(e){return Fn.call(this,e,!0)},add:Yn("add"),set:Yn("set"),delete:Yn("delete"),clear:Yn("clear"),forEach:Kn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((o=>{e[o]=Jn(o,!1,!1),n[o]=Jn(o,!0,!1),t[o]=Jn(o,!1,!0),r[o]=Jn(o,!0,!0)})),[e,n,t,r]}const[Qn,Zn,Xn,er]=Gn();function tr(e,t){const n=t?e?er:Xn:e?Zn:Qn;return(t,r,o)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(l(n,r)&&r in t?n:t,r,o)}const nr={get:tr(!1,!1)},rr={get:tr(!1,!0)},or={get:tr(!0,!1)},ir=new WeakMap,sr=new WeakMap,ar=new WeakMap,cr=new WeakMap;function ur(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>b(e).slice(8,-1))(e))}function lr(e){return dr(e)?e:hr(e,!1,Mn,nr,ir)}function fr(e){return hr(e,!0,Rn,or,ar)}function hr(e,t,n,r,o){if(!m(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=o.get(e);if(i)return i;const s=ur(e);if(0===s)return e;const a=new Proxy(e,2===s?r:n);return o.set(e,a),a}function pr(e){return dr(e)?pr(e.__v_raw):!(!e||!e.__v_isReactive)}function dr(e){return!(!e||!e.__v_isReadonly)}function gr(e){return!(!e||!e.__v_isShallow)}function vr(e){const t=e&&e.__v_raw;return t?vr(t):e}function mr(e){return Object.isExtensible(e)&&((e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})})(e,"__v_skip",!0),e}const yr=e=>m(e)?lr(e):e,_r=e=>m(e)?fr(e):e;class br{constructor(e,t,n,r){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new an((()=>e(this._value)),(()=>xr(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){const e=vr(this);return e._cacheable&&!e.effect.dirty||!$(e._value,e._value=e.effect.run())||xr(e,4),wr(e),e.effect._dirtyLevel>=2&&xr(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function wr(e){var t;fn&&nn&&(e=vr(e),yn(nn,null!=(t=e.dep)?t:e.dep=wn((()=>e.dep=void 0),e instanceof br?e:void 0)))}function xr(e,t=4,n){const r=(e=vr(e)).dep;r&&bn(r,t)}function Sr(e){return!(!e||!0!==e.__v_isRef)}function Or(e){return function(e,t){if(Sr(e))return e;return new kr(e,t)}(e,!1)}class kr{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:vr(e),this._value=t?e:yr(e)}get value(){return wr(this),this._value}set value(e){const t=this.__v_isShallow||gr(e)||dr(e);e=t?e:vr(e),$(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:yr(e),xr(this,4))}}function Tr(e){return Sr(e)?e.value:e}const Ar={get:(e,t,n)=>Tr(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Sr(o)&&!Sr(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Ir(e){return pr(e)?e:new Proxy(e,Ar)}function Pr(e){const t=f(e)?new Array(e.length):{};for(const n in e)t[n]=$r(e,n);return t}class jr{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return e=vr(this._object),t=this._key,null==(n=xn.get(e))?void 0:n.get(t);var e,t,n}}function $r(e,t,n){const r=e[t];return Sr(r)?r:new jr(e,t,n)}function Er(e,t,n,r){try{return r?e(...r):e()}catch(o){Cr(o,t,n)}}function Lr(e,t,n,r){if(d(e)){const o=Er(e,t,n,r);return o&&y(o)&&o.catch((e=>{Cr(e,t,n)})),o}const o=[];for(let i=0;i<e.length;i++)o.push(Lr(e[i],t,n,r));return o}function Cr(e,t,n,r=!0){const o=t?t.vnode:null;if(t){let r=t.parent;const o=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;r;){const t=r.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,i))return;r=r.parent}const s=t.appContext.config.errorHandler;if(s)return void Er(s,null,10,[e,o,i])}Mr(e,n,o,r)}function Mr(e,t,n,r=!0){console.error(e)}let Rr=!1,Dr=!1;const Nr=[];let Br=0;const Ur=[];let Fr=null,qr=0;const Wr=Promise.resolve();let Hr=null;function zr(e){const t=Hr||Wr;return e?t.then(this?e.bind(this):e):t}function Vr(e){Nr.length&&Nr.includes(e,Rr&&e.allowRecurse?Br+1:Br)||(null==e.id?Nr.push(e):Nr.splice(function(e){let t=Br+1,n=Nr.length;for(;t<n;){const r=t+n>>>1,o=Nr[r],i=Gr(o);i<e||i===e&&o.pre?t=r+1:n=r}return t}(e.id),0,e),Kr())}function Kr(){Rr||Dr||(Dr=!0,Hr=Wr.then(Zr))}function Jr(e){f(e)?Ur.push(...e):Fr&&Fr.includes(e,e.allowRecurse?qr+1:qr)||Ur.push(e),Kr()}function Yr(e,t,n=(Rr?Br+1:0)){for(;n<Nr.length;n++){const e=Nr[n];e&&e.pre&&(Nr.splice(n,1),n--,e())}}const Gr=e=>null==e.id?1/0:e.id,Qr=(e,t)=>{const n=Gr(e)-Gr(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Zr(e){Dr=!1,Rr=!0,Nr.sort(Qr);try{for(Br=0;Br<Nr.length;Br++){const e=Nr[Br];e&&!1!==e.active&&Er(e,null,14)}}finally{Br=0,Nr.length=0,function(e){if(Ur.length){const e=[...new Set(Ur)].sort(((e,t)=>Gr(e)-Gr(t)));if(Ur.length=0,Fr)return void Fr.push(...e);for(Fr=e,qr=0;qr<Fr.length;qr++)Fr[qr]();Fr=null,qr=0}}(),Rr=!1,Hr=null,(Nr.length||Ur.length)&&Zr()}}function Xr(e,n,...r){if(e.isUnmounted)return;const o=e.vnode.props||t;let i=r;const s=n.startsWith("update:"),a=s&&n.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:n,trim:s}=o[e]||t;s&&(i=r.map((e=>g(e)?e.trim():e))),n&&(i=r.map(L))}let c,u=o[c=j(n)]||o[c=j(T(n))];!u&&s&&(u=o[c=j(I(n))]),u&&Lr(u,e,6,i);const l=o[c+"Once"];if(l){if(e.emitted){if(e.emitted[c])return}else e.emitted={};e.emitted[c]=!0,Lr(l,e,6,i)}}function eo(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;const i=e.emits;let s={},c=!1;if(!d(e)){const r=e=>{const n=eo(e,t,!0);n&&(c=!0,a(s,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return i||c?(f(i)?i.forEach((e=>s[e]=null)):a(s,i),m(e)&&r.set(e,s),s):(m(e)&&r.set(e,null),null)}function to(e,t){return!(!e||!i(t))&&(t=t.slice(2).replace(/Once$/,""),l(e,t[0].toLowerCase()+t.slice(1))||l(e,I(t))||l(e,t))}let no=null;function ro(e){const t=no;return no=e,e&&e.type.__scopeId,t}function oo(e,t){return e&&(e[t]||e[T(t)]||e[P(T(t))])}const io={};function so(e,t,n){return ao(e,t,n)}function ao(e,n,{immediate:o,deep:i,flush:s,once:a,onTrack:u,onTrigger:l}=t){if(n&&a){const e=n;n=(...t)=>{e(...t),k()}}const h=ci,p=e=>!0===i?e:lo(e,!1===i?1:void 0);let g,v,m=!1,y=!1;if(Sr(e)?(g=()=>e.value,m=gr(e)):pr(e)?(g=()=>p(e),m=!0):f(e)?(y=!0,m=e.some((e=>pr(e)||gr(e))),g=()=>e.map((e=>Sr(e)?e.value:pr(e)?p(e):d(e)?Er(e,h,2):void 0))):g=d(e)?n?()=>Er(e,h,2):()=>(v&&v(),Lr(e,h,3,[_])):r,n&&i){const e=g;g=()=>lo(e())}let _=e=>{v=S.onStop=()=>{Er(e,h,4),v=S.onStop=void 0}},b=y?new Array(e.length).fill(io):io;const w=()=>{if(S.active&&S.dirty)if(n){const e=S.run();(i||m||(y?e.some(((e,t)=>$(e,b[t]))):$(e,b)))&&(v&&v(),Lr(n,h,3,[e,b===io?void 0:y&&b[0]===io?[]:b,_]),b=e)}else S.run()};let x;w.allowRecurse=!!n,"sync"===s?x=w:"post"===s?x=()=>ri(w,h&&h.suspense):(w.pre=!0,h&&(w.id=h.uid),x=()=>Vr(w));const S=new an(g,r,x),O=sn(),k=()=>{S.stop(),O&&c(O.effects,S)};return n?o?w():b=S.run():"post"===s?ri(S.run.bind(S),h&&h.suspense):S.run(),k}function co(e,t,n){const r=this.proxy,o=g(e)?e.includes(".")?uo(r,e):()=>r[e]:e.bind(r,r);let i;d(t)?i=t:(i=t.handler,n=t);const s=hi(this),a=ao(o,i.bind(r),n);return s(),a}function uo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function lo(e,t,n=0,r){if(!m(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((r=r||new Set).has(e))return e;if(r.add(e),Sr(e))lo(e.value,t,n,r);else if(f(e))for(let o=0;o<e.length;o++)lo(e[o],t,n,r);else if(p(e)||h(e))e.forEach((e=>{lo(e,t,n,r)}));else if(w(e))for(const o in e)lo(e[o],t,n,r);return e}function fo(){return{app:null,config:{isNativeTag:o,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ho=0;let po=null;function go(e,t,n=!1){const r=ci||no;if(r||po){const o=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:po._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&d(t)?t.call(r&&r.proxy):t}}function vo(e,t){yo(e,"a",t)}function mo(e,t){yo(e,"da",t)}function yo(e,t,n=ci){const r=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(bo(t,r,n),n){let e=n.parent;for(;e&&e.parent;)e.parent.vnode.type.__isKeepAlive&&_o(r,t,n,e),e=e.parent}}function _o(e,t,n,r){const o=bo(t,e,r,!0);Ao((()=>{c(r[t],o)}),n)}function bo(e,t,n=ci,r=!1){if(n){(function(e){return J.indexOf(e)>-1})(e)&&(n=n.root);const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;dn();const o=hi(n),i=Lr(t,n,e,r);return o(),gn(),i});return r?o.unshift(i):o.push(i),i}}const wo=e=>(t,n=ci)=>(!gi||"sp"===e)&&bo(e,((...e)=>t(...e)),n),xo=wo("bm"),So=wo("m"),Oo=wo("bu"),ko=wo("u"),To=wo("bum"),Ao=wo("um"),Io=wo("sp"),Po=wo("rtg"),jo=wo("rtc");function $o(e,t=ci){bo("ec",e,t)}const Eo=e=>e?di(e)?yi(e)||e.proxy:Eo(e.parent):null,Lo=a(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Eo(e.parent),$root:e=>Eo(e.root),$emit:e=>e.emit,$options:e=>Fo(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Vr(e.update)}),$watch:e=>co.bind(e)}),Co=(e,n)=>e!==t&&!e.__isScriptSetup&&l(e,n),Mo={get({_:e},n){const{ctx:r,setupState:o,data:i,props:s,accessCache:a,type:c,appContext:u}=e;let f;if("$"!==n[0]){const c=a[n];if(void 0!==c)switch(c){case 1:return o[n];case 2:return i[n];case 4:return r[n];case 3:return s[n]}else{if(Co(o,n))return a[n]=1,o[n];if(i!==t&&l(i,n))return a[n]=2,i[n];if((f=e.propsOptions[0])&&l(f,n))return a[n]=3,s[n];if(r!==t&&l(r,n))return a[n]=4,r[n];Do&&(a[n]=0)}}const h=Lo[n];let p,d;return h?("$attrs"===n&&kn(e,0,n),h(e)):(p=c.__cssModules)&&(p=p[n])?p:r!==t&&l(r,n)?(a[n]=4,r[n]):(d=u.config.globalProperties,l(d,n)?d[n]:void 0)},set({_:e},n,r){const{data:o,setupState:i,ctx:s}=e;return Co(i,n)?(i[n]=r,!0):o!==t&&l(o,n)?(o[n]=r,!0):!l(e.props,n)&&(("$"!==n[0]||!(n.slice(1)in e))&&(s[n]=r,!0))},has({_:{data:e,setupState:n,accessCache:r,ctx:o,appContext:i,propsOptions:s}},a){let c;return!!r[a]||e!==t&&l(e,a)||Co(n,a)||(c=s[0])&&l(c,a)||l(o,a)||l(Lo,a)||l(i.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:l(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ro(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Do=!0;function No(e){const t=Fo(e),n=e.proxy,o=e.ctx;Do=!1,t.beforeCreate&&Bo(t.beforeCreate,e,"bc");const{data:i,computed:s,methods:a,watch:c,provide:u,inject:l,created:h,beforeMount:p,mounted:g,beforeUpdate:v,updated:y,activated:_,deactivated:b,beforeDestroy:w,beforeUnmount:x,destroyed:S,unmounted:O,render:k,renderTracked:T,renderTriggered:A,errorCaptured:I,serverPrefetch:P,expose:j,inheritAttrs:$,components:E,directives:L,filters:C}=t;if(l&&function(e,t,n=r){f(e)&&(e=zo(e));for(const r in e){const n=e[r];let o;o=m(n)?"default"in n?go(n.from||r,n.default,!0):go(n.from||r):go(n),Sr(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[r]=o}}(l,o,null),a)for(const r in a){const e=a[r];d(e)&&(o[r]=e.bind(n))}if(i){const t=i.call(n,n);m(t)&&(e.data=lr(t))}if(Do=!0,s)for(const f in s){const e=s[f],t=d(e)?e.bind(n,n):d(e.get)?e.get.bind(n,n):r,i=!d(e)&&d(e.set)?e.set.bind(n):r,a=_i({get:t,set:i});Object.defineProperty(o,f,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(c)for(const r in c)Uo(c[r],o,n,r);function M(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(function(){if(u){const e=d(u)?u.call(n):u;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(ci){let n=ci.provides;const r=ci.parent&&ci.parent.provides;r===n&&(n=ci.provides=Object.create(r)),n[e]=t,"app"===ci.type.mpType&&ci.appContext.app.provide(e,t)}}(t,e[t])}))}}(),h&&Bo(h,e,"c"),M(xo,p),M(So,g),M(Oo,v),M(ko,y),M(vo,_),M(mo,b),M($o,I),M(jo,T),M(Po,A),M(To,x),M(Ao,O),M(Io,P),f(j))if(j.length){const t=e.exposed||(e.exposed={});j.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===r&&(e.render=k),null!=$&&(e.inheritAttrs=$),E&&(e.components=E),L&&(e.directives=L),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function Bo(e,t,n){Lr(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Uo(e,t,n,r){const o=r.includes(".")?uo(n,r):()=>n[r];if(g(e)){const n=t[e];d(n)&&so(o,n)}else if(d(e))so(o,e.bind(n));else if(m(e))if(f(e))e.forEach((e=>Uo(e,t,n,r)));else{const r=d(e.handler)?e.handler.bind(n):t[e.handler];d(r)&&so(o,r,e)}}function Fo(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let c;return a?c=a:o.length||n||r?(c={},o.length&&o.forEach((e=>qo(c,e,s,!0))),qo(c,t,s)):c=t,m(t)&&i.set(t,c),c}function qo(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&qo(e,i,n,!0),o&&o.forEach((t=>qo(e,t,n,!0)));for(const s in t)if(r&&"expose"===s);else{const r=Wo[s]||n&&n[s];e[s]=r?r(e[s],t[s]):t[s]}return e}const Wo={data:Ho,props:Jo,emits:Jo,methods:Ko,computed:Ko,beforeCreate:Vo,created:Vo,beforeMount:Vo,mounted:Vo,beforeUpdate:Vo,updated:Vo,beforeDestroy:Vo,beforeUnmount:Vo,destroyed:Vo,unmounted:Vo,activated:Vo,deactivated:Vo,errorCaptured:Vo,serverPrefetch:Vo,components:Ko,directives:Ko,watch:function(e,t){if(!e)return t;if(!t)return e;const n=a(Object.create(null),e);for(const r in t)n[r]=Vo(e[r],t[r]);return n},provide:Ho,inject:function(e,t){return Ko(zo(e),zo(t))}};function Ho(e,t){return t?e?function(){return a(d(e)?e.call(this,this):e,d(t)?t.call(this,this):t)}:t:e}function zo(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Vo(e,t){return e?[...new Set([].concat(e,t))]:t}function Ko(e,t){return e?a(Object.create(null),e,t):t}function Jo(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:a(Object.create(null),Ro(e),Ro(null!=t?t:{})):t}function Yo(e,t,n,r=!1){const o={},i={};e.propsDefaults=Object.create(null),Go(e,t,o,i);for(const s in e.propsOptions[0])s in o||(o[s]=void 0);n?e.props=r?o:hr(o,!1,Dn,rr,sr):e.type.props?e.props=o:e.props=i,e.attrs=i}function Go(e,n,r,o){const[i,s]=e.propsOptions;let a,c=!1;if(n)for(let t in n){if(S(t))continue;const u=n[t];let f;i&&l(i,f=T(t))?s&&s.includes(f)?(a||(a={}))[f]=u:r[f]=u:to(e.emitsOptions,t)||t in o&&u===o[t]||(o[t]=u,c=!0)}if(s){const n=vr(r),o=a||t;for(let t=0;t<s.length;t++){const a=s[t];r[a]=Qo(i,n,a,o[a],e,!l(o,a))}}return c}function Qo(e,t,n,r,o,i){const s=e[n];if(null!=s){const e=l(s,"default");if(e&&void 0===r){const e=s.default;if(s.type!==Function&&!s.skipFactory&&d(e)){const{propsDefaults:i}=o;if(n in i)r=i[n];else{const s=hi(o);r=i[n]=e.call(null,t),s()}}else r=e}s[0]&&(i&&!e?r=!1:!s[1]||""!==r&&r!==I(n)||(r=!0))}return r}function Zo(e,r,o=!1){const i=r.propsCache,s=i.get(e);if(s)return s;const c=e.props,u={},h=[];let p=!1;if(!d(e)){const t=e=>{p=!0;const[t,n]=Zo(e,r,!0);a(u,t),n&&h.push(...n)};!o&&r.mixins.length&&r.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!c&&!p)return m(e)&&i.set(e,n),n;if(f(c))for(let n=0;n<c.length;n++){const e=T(c[n]);Xo(e)&&(u[e]=t)}else if(c)for(const t in c){const e=T(t);if(Xo(e)){const n=c[t],r=u[e]=f(n)||d(n)?{type:n}:a({},n);if(r){const t=ni(Boolean,r.type),n=ni(String,r.type);r[0]=t>-1,r[1]=n<0||t<n,(t>-1||l(r,"default"))&&h.push(e)}}}const g=[u,h];return m(e)&&i.set(e,g),g}function Xo(e){return"$"!==e[0]&&!S(e)}function ei(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function ti(e,t){return ei(e)===ei(t)}function ni(e,t){return f(t)?t.findIndex((t=>ti(t,e))):d(t)&&ti(t,e)?0:-1}const ri=Jr;function oi(e){return e?pr(t=e)||dr(t)||"__vInternal"in e?a({},e):e:null;var t}const ii=fo();let si=0;function ai(e,n,r){const o=e.type,i=(n?n.appContext:e.appContext)||ii,s={uid:si++,vnode:e,type:o,parent:n,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new rn(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Zo(o,i),emitsOptions:eo(o,i),emit:null,emitted:null,propsDefaults:t,inheritAttrs:o.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null,$uniElements:new Map,$templateUniElementRefs:[],$templateUniElementStyles:{},$eS:{},$eA:{}};return s.ctx={_:s},s.root=n?n.root:s,s.emit=Xr.bind(null,s),e.ce&&e.ce(s),s}let ci=null;const ui=()=>ci||no;let li,fi;li=e=>{ci=e},fi=e=>{gi=e};const hi=e=>{const t=ci;return li(e),e.scope.on(),()=>{e.scope.off(),li(t)}},pi=()=>{ci&&ci.scope.off(),li(null)};function di(e){return 4&e.vnode.shapeFlag}let gi=!1;function vi(e,t=!1){t&&fi(t);const{props:n}=e.vnode,r=di(e);Yo(e,n,r,t);const o=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=mr(new Proxy(e.ctx,Mo));const{setup:r}=n;if(r){const t=e.setupContext=r.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(kn(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,n=hi(e);dn();const o=Er(r,e,0,[e.props,t]);gn(),n(),y(o)?o.then(pi,pi):function(e,t,n){d(t)?e.render=t:m(t)&&(e.setupState=Ir(t));mi(e)}(e,o)}else mi(e)}(e):void 0;return t&&fi(!1),o}function mi(e,t,n){const o=e.type;e.render||(e.render=o.render||r);{const t=hi(e);dn();try{No(e)}finally{gn(),t()}}}function yi(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Ir(mr(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in Lo}))}const _i=(e,t)=>{const n=function(e,t,n=!1){let o,i;const s=d(e);return s?(o=e,i=r):(o=e.get,i=e.set),new br(o,i,s||!i,n)}(e,0,gi);return n},bi="3.4.21";function wi(e){return Tr(e)}const xi="[object Array]",Si="[object Object]";function Oi(e,t){const n={};return ki(e,t),Ti(e,t,"",n),n}function ki(e,t){if((e=wi(e))===t)return;const n=b(e),r=b(t);if(n==Si&&r==Si)for(let o in t){const n=e[o];void 0===n?e[o]=null:ki(n,t[o])}else n==xi&&r==xi&&e.length>=t.length&&t.forEach(((t,n)=>{ki(e[n],t)}))}function Ti(e,t,n,r){if((e=wi(e))===t)return;const o=b(e),i=b(t);if(o==Si)if(i!=Si||Object.keys(e).length<Object.keys(t).length)Ai(r,n,e);else for(let s in e){const o=wi(e[s]),i=t[s],a=b(o),c=b(i);if(a!=xi&&a!=Si)o!=i&&Ai(r,(""==n?"":n+".")+s,o);else if(a==xi)c!=xi||o.length<i.length?Ai(r,(""==n?"":n+".")+s,o):o.forEach(((e,t)=>{Ti(e,i[t],(""==n?"":n+".")+s+"["+t+"]",r)}));else if(a==Si)if(c!=Si||Object.keys(o).length<Object.keys(i).length)Ai(r,(""==n?"":n+".")+s,o);else for(let e in o)Ti(o[e],i[e],(""==n?"":n+".")+s+"."+e,r)}else o==xi?i!=xi||e.length<t.length?Ai(r,n,e):e.forEach(((e,o)=>{Ti(e,t[o],n+"["+o+"]",r)})):Ai(r,n,e)}function Ai(e,t,n){e[t]=n}function Ii(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function Pi(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!function(e){return Nr.includes(e.update)}(e))return zr(t&&t.bind(e.proxy));let r;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push((()=>{t?Er(t.bind(e.proxy),e,14):r&&r(e.proxy)})),new Promise((e=>{r=e}))}function ji(e,t){const n=typeof(e=wi(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(f(e)){const r=e.length;n=new Array(r),t.set(e,n);for(let o=0;o<r;o++)n[o]=ji(e[o],t)}else{n={},t.set(e,n);for(const r in e)l(e,r)&&(n[r]=ji(e[r],t))}return n}if("symbol"!==n)return e}function $i(e){return ji(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function Ei(e,t,n){if(!t)return;(t=$i(t)).$eS=e.$eS||{},t.$eA=e.$eA||{};const r=e.ctx,o=r.mpType;if("page"===o||"component"===o){t.r0=1;const o=r.$scope,i=Object.keys(t),s=Oi(t,n||function(e,t){const n=e.data,r=Object.create(null);return t.forEach((e=>{r[e]=n[e]})),r}(o,i));Object.keys(s).length?(r.__next_tick_pending=!0,o.setData(s,(()=>{r.__next_tick_pending=!1,Ii(e)})),Yr()):Ii(e)}}function Li(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const r=e.computed;if(r){const e=Object.keys(r);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function Ci(e,t=!1){const{setupState:n,$templateRefs:r,$templateUniElementRefs:o,ctx:{$scope:i,$mpPlatform:s}}=e;if("mp-alipay"===s)return;if(!i||!r&&!o)return;if(t)return r&&r.forEach((e=>Mi(e,null,n))),void(o&&o.forEach((e=>Mi(e,null,n))));const a="mp-baidu"===s||"mp-toutiao"===s,c=e=>{if(0===e.length)return[];const t=(i.selectAllComponents(".r")||[]).concat(i.selectAllComponents(".r-i-f")||[]);return e.filter((e=>{const r=function(e,t){const n=e.find((e=>e&&(e.properties||e.props).uI===t));if(n){const e=n.$vm;return e?yi(e.$)||e:function(e){m(e)&&mr(e);return e}(n)}return null}(t,e.i);return!(!a||null!==r)||(Mi(e,r,n),!1)}))},u=()=>{if(r){const t=c(r);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},(()=>{c(t)}))}};o&&o.length&&Pi(e,(()=>{o.forEach((e=>{f(e.v)?e.v.forEach((t=>{Mi(e,t,n)})):Mi(e,e.v,n)}))})),i._$setRef?i._$setRef(u):Pi(e,u)}function Mi({r:e,f:t},n,r){if(d(e))e(n,{});else{const o=g(e),i=Sr(e);if(o||i)if(t){if(!i)return;f(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;n.$&&To((()=>c(t,n)),n.$)}}else o?l(r,e)&&(r[e]=n):Sr(e)&&(e.value=n)}}const Ri=Jr;function Di(e,t){const n=e.component=ai(e,t.parentComponent,null);return n.ctx.$onApplyOptions=Li,n.ctx.$children=[],"app"===t.mpType&&(n.render=r),t.onBeforeSetup&&t.onBeforeSetup(n,t),vi(n),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(yi(n)||n.proxy),function(e){const t=Ui.bind(e);e.$updateScopedSlots=()=>zr((()=>Vr(t)));const n=()=>{if(e.isMounted){const{next:t,bu:n,u:r}=e;Fi(e,!1),dn(),Yr(),gn(),n&&E(n),Fi(e,!0),Ei(e,Ni(e)),r&&Ri(r)}else To((()=>{Ci(e,!0)}),e),Ei(e,Ni(e))},o=e.effect=new an(n,r,(()=>Vr(i)),e.scope),i=e.update=()=>{o.dirty&&o.run()};i.id=e.uid,Fi(e,!0),i()}(n),n.proxy}function Ni(e){const{type:t,vnode:n,proxy:r,withProxy:o,props:s,propsOptions:[a],slots:c,attrs:u,emit:l,render:f,renderCache:h,data:p,setupState:d,ctx:g,uid:v,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:m}}}},inheritAttrs:y}=e;let _;e.$uniElementIds=new Map,e.$templateRefs=[],e.$templateUniElementRefs=[],e.$templateUniElementStyles={},e.$ei=0,m(v),e.__counter=0===e.__counter?1:0;const b=ro(e);try{if(4&n.shapeFlag){Bi(y,s,a,u);const e=o||r;_=f.call(e,e,h,s,d,p,g)}else{Bi(y,s,a,t.props?u:(e=>{let t;for(const n in e)("class"===n||"style"===n||i(n))&&((t||(t={}))[n]=e[n]);return t})(u));const e=t;_=e.length>1?e(s,{attrs:u,slots:c,emit:l}):e(s,null)}}catch(w){Cr(w,e,1),_=!1}return Ci(e),ro(b),_}function Bi(e,t,n,r){if(t&&r&&!1!==e){const e=Object.keys(r).filter((e=>"class"!==e&&"style"!==e));if(!e.length)return;n&&e.some(s)?e.forEach((e=>{s(e)&&e.slice(9)in n||(t[e]=r[e])})):e.forEach((e=>t[e]=r[e]))}}function Ui(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,r=Object.create(null);e.forEach((({path:e,index:t,data:o})=>{const i=W(n,e),s=g(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===i||void 0===i[t])r[s]=o;else{const e=Oi(o,i[t]);Object.keys(e).forEach((t=>{r[s+"."+t]=e[t]}))}})),e.length=0,Object.keys(r).length&&t.setData(r)}function Fi({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}const qi=function(e,t=null){d(e)||(e=a({},e)),null==t||m(t)||(t=null);const n=fo(),r=new WeakSet,o=n.app={_uid:ho++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:bi,get config(){return n.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&d(e.install)?(r.add(e),e.install(o,...t)):d(e)&&(r.add(e),e(o,...t))),o),mixin:e=>(n.mixins.includes(e)||n.mixins.push(e),o),component:(e,t)=>t?(n.components[e]=t,o):n.components[e],directive:(e,t)=>t?(n.directives[e]=t,o):n.directives[e],mount(){},unmount(){},provide:(e,t)=>(n.provides[e]=t,o),runWithContext(e){const t=po;po=o;try{return e()}finally{po=t}}};return o};function Wi(e,t=null){("undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0).__VUE__=!0;const n=qi(e,t),o=n._context;o.config.globalProperties.$nextTick=function(e){return Pi(this.$,e)};const i=e=>(e.appContext=o,e.shapeFlag=6,e),s=function(e,t){return Di(i(e),t)},a=function(e){return e&&function(e){const{bum:t,scope:n,update:r,um:o}=e;t&&E(t);{const t=e.parent;if(t){const n=t.ctx.$children,r=yi(e)||e.proxy,o=n.indexOf(r);o>-1&&n.splice(o,1)}}n.stop(),r&&(r.active=!1),o&&Ri(o),Ri((()=>{e.isUnmounted=!0}))}(e.$)};return n.mount=function(){e.render=r;const t=Di(i({type:e}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});return n._instance=t.$,t.$app=n,t.$createComponent=s,t.$destroyComponent=a,o.$appInstance=t,t},n.unmount=function(){},n}function Hi(e,t,n,r){d(t)&&bo(e,t.bind(n),r)}function zi(e,t,n){!function(e,t,n){const r=e.mpType||n.$mpType;r&&"component"!==r&&Object.keys(e).forEach((r=>{if(Q(r,e[r],!1)){const o=e[r];f(o)?o.forEach((e=>Hi(r,e,n,t))):Hi(r,o,n,t)}}))}(e,t,n)}function Vi(e,t,n){return e[t]=n}function Ki(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Ji(e){const t=e.config.errorHandler;return function(n,r,o){t&&t(n,r,o);const i=e._instance;if(!i||!i.proxy)throw n;i.onError?i.proxy.$callHook("onError",n):Mr(n,0,r&&r.$.vnode,!1)}}function Yi(e,t){return e?[...new Set([].concat(e,t))]:t}let Gi;const Qi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Zi=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Xi(){const e=en.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((r=t[1],decodeURIComponent(Gi(r).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var r;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function es(e){const t=e.config;var n;t.errorHandler=ee(e,Ji),n=t.optionMergeStrategies,Y.forEach((e=>{n[e]=Yi}));const r=t.globalProperties;!function(e){e.uniIDHasRole=function(e){const{role:t}=Xi();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=Xi();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=Xi();return e>Date.now()}}(r),r.$set=Vi,r.$applyOptions=zi,r.$callMethod=Ki,en.invokeCreateVueAppHook(e)}Gi="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Zi.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,o="",i=0;i<e.length;)t=Qi.indexOf(e.charAt(i++))<<18|Qi.indexOf(e.charAt(i++))<<12|(n=Qi.indexOf(e.charAt(i++)))<<6|(r=Qi.indexOf(e.charAt(i++))),o+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;const ts=Object.create(null);function ns(e){delete ts[e]}function rs(e){if(!e)return;const[t,n]=e.split(",");return ts[t]?ts[t][parseInt(n)]:void 0}var os={install(e){es(e),e.config.globalProperties.pruneComponentPropsCache=ns;const t=e.mount;e.mount=function(n){const r=t.call(e,n),o=function(){const e="createApp";if("undefined"!=typeof global&&void 0!==global[e])return global[e];if("undefined"!=typeof my)return my[e]}();return o?o(r):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(r),r}}};function is(e){return g(e)?e:function(e){let t="";if(!e||g(e))return t;for(const n in e)t+=`${n.startsWith("--")?n:I(n)}:${e[n]};`;return t}(C(e))}function ss(e,t){const n=ui(),o=n.ctx,i=void 0===t||"mp-weixin"!==o.$mpPlatform&&"mp-qq"!==o.$mpPlatform&&"mp-xhs"!==o.$mpPlatform||!g(t)&&"number"!=typeof t?"":"_"+t,s="e"+n.$ei+++i,c=o.$scope;if(!e)return delete c[s],s;const u=c[s];return u?u.value=e:c[s]=function(e,t){const n=e=>{var o;(o=e).type&&o.target&&(o.preventDefault=r,o.stopPropagation=r,o.stopImmediatePropagation=r,l(o,"detail")||(o.detail={}),l(o,"markerId")&&(o.detail="object"==typeof o.detail?o.detail:{},o.detail.markerId=o.markerId),w(o.detail)&&l(o.detail,"checked")&&!l(o.detail,"value")&&(o.detail.value=o.detail.checked),w(o.detail)&&(o.target=a({},o.target,o.detail)));let i=[e];t&&t.ctx.$getTriggerEventDetail&&"number"==typeof e.detail&&(e.detail=t.ctx.$getTriggerEventDetail(e.detail)),e.detail&&e.detail.__args__&&(i=e.detail.__args__);const s=n.value,c=()=>Lr(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e(t)))}return t}(e,s),t,5,i),u=e.target,h=!!u&&(!!u.dataset&&"true"===String(u.dataset.eventsync));if(!as.includes(e.type)||h){const t=c();if("input"===e.type&&(f(t)||y(t)))return;return t}setTimeout(c)};return n.value=e,n}(e,n),s}const as=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function cs(e,{name:t,path:n,vueId:r}){const o=ui();e.path=n;const i=o.$ssi||(o.$ssi={}),s=i[r]||(i[r]=function(e){const t=(n,r,o)=>{const i=t.slots[n];if(!i)return;const s=void 0!==o;o=o||0;const a=ro(e),c=i.fn(r,n+(s?"-"+o:""),o),u=i.fn.path;ro(a),(e.$scopedSlotsData||(e.$scopedSlotsData=[])).push({path:u,index:o,data:c}),e.$updateScopedSlots()};return t.slots={},t}(o));return s.slots[t]?s.slots[t].fn=e:s.slots[t]={fn:e},W(o.ctx.$scope.data,n)}const us=function(e,t=null){return e&&(e.mpType="app"),Wi(e,t).use(os)};const ls=["externalClasses"];const fs=/_(.*)_worklet_factory_/;function hs(e,t){const n=e.$children;for(let o=n.length-1;o>=0;o--){const e=n[o];if(e.$scope._$vueId===t)return e}let r;for(let o=n.length-1;o>=0;o--)if(r=hs(n[o],t),r)return r}const ps=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function ds(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,Object.defineProperties(n,{virtualHostId:{get(){const e=this.$scope.data.virtualHostId;return void 0===e?"":e}}}),n.$mp={},n._self={},e.slots={},f(t.slots)&&t.slots.length&&(t.slots.forEach((t=>{e.slots[t]=!0})),e.slots.d&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=gs,n.$callHook=vs,e.emit=function(e,t){return function(n,...r){const o=t.$scope;if(o&&n){const e={__args__:r};o.triggerEvent(n,e)}return e.apply(this,[n,...r])}}(e.emit,n)}function gs(e){const t=this.$[e];return!(!t||!t.length)}function vs(e,t){"mounted"===e&&(vs.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&((e,t)=>{let n;for(let r=0;r<e.length;r++)n=e[r](t);return n})(n,t)}const ms=["onLoad","onShow","onHide","onUnload","onResize","onTabItemTap","onReachBottom","onPullDownRefresh","onAddToFavorites"];function ys(e,t=new Set){if(e){Object.keys(e).forEach((n=>{Q(n,e[n])&&t.add(n)}));{const{extends:n,mixins:r}=e;r&&r.forEach((e=>ys(e,t))),n&&ys(n,t)}}return t}function _s(e,t,n){-1!==n.indexOf(t)||l(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const bs=["onReady"];function ws(e,t,n=bs){t.forEach((t=>_s(e,t,n)))}function xs(e,t,n=bs){ys(t).forEach((t=>_s(e,t,n)))}const Ss=q((()=>{const e=[],t=d(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(f(n)){const t=Object.keys(G);n.forEach((n=>{t.forEach((t=>{l(n,t)&&!e.includes(t)&&e.push(t)}))}))}}return e}));const Os=["onShow","onHide","onError","onThemeChange","onPageNotFound","onUnhandledRejection"];function ks(e,t){const n=e.$,r={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const r=n.ctx;this.$vm&&r.$scope&&r.$callHook||(ds(n,{mpType:"app",mpInstance:this,slots:[]}),r.globalData=this.globalData,e.$callHook("onLaunch",t))}},o=wx.$onErrorHandlers;o&&(o.forEach((e=>{bo("onError",e,n)})),o.length=0),function(e){const t=Or(function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=ue(n&&n.language?n.language:"en")||"en"}return t}());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(e);const i=e.$.type;ws(r,Os),xs(r,i);{const e=i.methods;e&&a(r,e)}return r}function Ts(e,t){if(d(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}d(e.onShow)&&wx.onAppShow&&wx.onAppShow((e=>{t.$callHook("onShow",e)})),d(e.onHide)&&wx.onAppHide&&wx.onAppHide((e=>{t.$callHook("onHide",e)}))}const As=["eO","uR","uRIF","uI","uT","uP","uS"];function Is(e){e.properties||(e.properties={}),a(e.properties,function(e,t=!1){const n={};if(!t){let e=function(e){const t=Object.create(null);e&&e.forEach((e=>{t[e]=!0})),this.setData({$slots:t})};As.forEach((e=>{n[e]={type:null,value:""}})),n.uS={type:null,value:[]},n.uS.observer=e}return e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}(e),function(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""},t.virtualHostHidden={type:null,value:""},t.virtualHostId={type:null,value:""}),t}(e.options))}const Ps=[String,Number,Boolean,Object,Array,null];function js(e,t){const n=function(e,t){return f(e)&&1===e.length?e[0]:e}(e);return-1!==Ps.indexOf(n)?n:null}function $s(e,t){return(t?function(e){const t={};w(e)&&Object.keys(e).forEach((n=>{-1===As.indexOf(n)&&(t[n]=e[n])}));return t}(e):rs(e.uP))||{}}function Es(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?function(e,t){const n=vr(t.props),r=rs(e)||{};Ls(n,r)&&(!function(e,t,n,r){const{props:o,attrs:i,vnode:{patchFlag:s}}=e,a=vr(o),[c]=e.propsOptions;let u=!1;if(!(r||s>0)||16&s){let r;Go(e,t,o,i)&&(u=!0);for(const i in a)t&&(l(t,i)||(r=I(i))!==i&&l(t,r))||(c?!n||void 0===n[i]&&void 0===n[r]||(o[i]=Qo(c,a,i,void 0,e,!0)):delete o[i]);if(i!==a)for(const e in i)t&&l(t,e)||(delete i[e],u=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let s=n[r];if(to(e.emitsOptions,s))continue;const f=t[s];if(c)if(l(i,s))f!==i[s]&&(i[s]=f,u=!0);else{const t=T(s);o[t]=Qo(c,a,t,f,e,!1)}else f!==i[s]&&(i[s]=f,u=!0)}}u&&Tn(e,"set","$attrs")}(t,r,n,!1),o=t.update,Nr.indexOf(o)>-1&&function(e){const t=Nr.indexOf(e);t>Br&&Nr.splice(t,1)}(t.update),t.update());var o}(e,this.$vm.$):"m"===this.properties.uT&&function(e,t){const n=t.properties,r=rs(e)||{};Ls(n,r,!1)&&t.setData(r)}(e,this))};e.observers||(e.observers={}),e.observers.uP=t}function Ls(e,t,n=!0){const r=Object.keys(t);if(n&&r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const n=r[o];if(t[n]!==e[n])return!0}return!1}function Cs(e,t){e.data={},e.behaviors=function(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const r=[];return f(t)&&t.forEach((e=>{r.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(f(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),r}(t)}function Ms(e,{parse:t,mocks:n,isPage:r,isPageInProject:o,initRelation:i,handleLink:s,initLifetimes:c}){e=e.default||e;const u={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};f(e.mixins)&&e.mixins.forEach((e=>{m(e.options)&&a(u,e.options)})),e.options&&a(u,e.options);const h={options:u,lifetimes:c({mocks:n,isPage:r,initRelation:i,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:s}};var p,d,g,v;return Cs(h,e),Is(h),Es(h),function(e,t){ls.forEach((n=>{l(t,n)&&(e[n]=t[n])}))}(h,e),p=h.methods,d=e.wxsCallMethods,f(d)&&d.forEach((e=>{p[e]=function(t){return this.$vm[e](t)}})),g=h.methods,(v=e.methods)&&Object.keys(v).forEach((e=>{const t=e.match(fs);if(t){const n=t[1];g[e]=v[e],g[n]=v[n]}})),t&&t(h,{handleLink:s}),h}let Rs,Ds;function Ns(){return getApp().$vm}function Bs(e,t){const{parse:n,mocks:r,isPage:o,initRelation:i,handleLink:s,initLifetimes:a}=t,c=Ms(e,{mocks:r,isPage:o,isPageInProject:!0,initRelation:i,handleLink:s,initLifetimes:a});!function({properties:e},t){f(t)?t.forEach((t=>{e[t]={type:String,value:""}})):w(t)&&Object.keys(t).forEach((n=>{const r=t[n];if(w(r)){let t=r.default;d(t)&&(t=t());const o=r.type;r.type=js(o),e[n]={type:r.type,value:t}}else e[n]={type:js(r)}}))}(c,(e.default||e).props);const u=c.methods;return u.onLoad=function(e){var t;return this.options=e,this.$page={fullPath:(t=this.route+K(e),function(e){return 0===e.indexOf("/")}(t)?t:"/"+t)},this.$vm&&this.$vm.$callHook("onLoad",e)},ws(u,ms),xs(u,e),function(e,t){if(!t)return;Object.keys(G).forEach((n=>{t&G[n]&&_s(e,n,[])}))}(u,e.__runtimeHooks),ws(u,Ss()),n&&n(c,{handleLink:s}),c}const Us=Page,Fs=Component;function qs(e){const t=e.triggerEvent,n=function(n,...r){return t.apply(e,[(o=n,T(o.replace(z,"-"))),...r]);var o};try{e.triggerEvent=n}catch(r){e._triggerEvent=n}}function Ws(e,t,n){const r=t[e];t[e]=r?function(...e){return qs(this),r.apply(this,e)}:function(){qs(this)}}Page=function(e){return Ws("onLoad",e),Us(e)},Component=function(e){Ws("created",e);return e.properties&&e.properties.uP||(Is(e),Es(e)),Fs(e)};var Hs=Object.freeze({__proto__:null,handleLink:function(e){const t=e.detail||e.value,n=t.vuePid;let r;n&&(r=hs(this.$vm,n)),r||(r=this.$vm),t.parent=r},initLifetimes:function({mocks:e,isPage:t,initRelation:n,vueOptions:r}){return{attached(){let o=this.properties;!function(e,t){if(!e)return;const n=e.split(","),r=n.length;1===r?t._$vueId=n[0]:2===r&&(t._$vueId=n[0],t._$vuePid=n[1])}(o.uI,this);const i={vuePid:this._$vuePid};n(this,i);const s=this,a=t(s);let c=o;this.$vm=function(e,t){Rs||(Rs=Ns().$createComponent);const n=Rs(e,t);return yi(n.$)||n}({type:r,props:$s(c,a)},{mpType:a?"page":"component",mpInstance:s,slots:o.uS||{},parentComponent:i.parent&&i.parent.$,onBeforeSetup(t,n){!function(e,t){Object.defineProperty(e,"refs",{get(){const e={};return function(e,t,n){e.selectAllComponents(t).forEach((e=>{const t=e.properties.uR;n[t]=e.$vm||e}))}(t,".r",e),t.selectAllComponents(".r-i-f").forEach((t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))})),e}})}(t,s),function(e,t,n){const r=e.ctx;n.forEach((n=>{l(t,n)&&(e[n]=r[n]=t[n])}))}(t,s,e),function(e,t){ds(e,t);const n=e.ctx;ps.forEach((e=>{n[e]=function(...t){const r=n.$scope;if(r&&r[e])return r[e].apply(r,t)}}))}(t,n)}}),a||function(e){const t=e.$options;f(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",(()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})}),{immediate:!0})}(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook("onReady"))},detached(){var e;this.$vm&&(ns(this.$vm.$.uid),e=this.$vm,Ds||(Ds=Ns().$destroyComponent),Ds(e))}}},initRelation:function(e,t){e.triggerEvent("__l",t)},isPage:function(e){return!!e.route},mocks:["__route__","__wxExparserNodeId__","__wxWebviewId__"]});const zs=function(e){return App(ks(e))},Vs=(Ks=Hs,function(e){return Component(Bs(e,Ks))});var Ks;const Js=function(e){return function(t){return Component(Ms(t,e))}}(Hs),Ys=function(e){Ts(ks(e),e)},Gs=function(e){const t=ks(e),n=d(getApp)&&getApp({allowDefault:!0});if(!n)return;e.$.ctx.$scope=n;const r=n.globalData;r&&Object.keys(t.globalData).forEach((e=>{l(r,e)||(r[e]=t.globalData[e])})),Object.keys(t).forEach((e=>{l(n,e)||(n[e]=t[e])})),Ts(t,e)};wx.createApp=global.createApp=zs,wx.createPage=Vs,wx.createComponent=Js,wx.createPluginApp=global.createPluginApp=Ys,wx.createSubpackageApp=global.createSubpackageApp=Gs;var Qs="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Zs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function t(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function n(e,t,n){return t=c(t),f(e,l()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,v(r.key),r)}}function i(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=m(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){a=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw i}}}}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=p(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}},a.apply(null,arguments)}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(l=function(){return!!e})()}function f(e,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return t(e)}function h(e,t){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}function d(e,t,n,r){var o=a(c(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof o?function(e){return o.apply(n,e)}:o}function g(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function v(e){var t=g(e,"string");return"symbol"==typeof t?t:t+""}function m(t,n){if(t){if("string"==typeof t)return e(t,n);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(t,n):void 0}}function y(e){var t;try{t=new Event("abort")}catch(G_){"undefined"!=typeof document?document.createEvent?(t=document.createEvent("Event")).initEvent("abort",!1,!1):(t=document.createEventObject()).type="abort":t={type:"abort",bubbles:!1,cancelable:!1}}return t.reason=e,t}function _(e){if(void 0===e)if("undefined"==typeof document)(e=new Error("This operation was aborted")).name="AbortError";else try{e=new DOMException("signal is aborted without reason"),Object.defineProperty(e,"name",{value:"AbortError"})}catch(t){(e=new Error("This operation was aborted")).name="AbortError"}return e}var b;(b="undefined"!=typeof self?self:Qs).AbortSignal,b.AbortController;var w=function(){function e(){r(this,e),Object.defineProperty(this,"listeners",{value:{},writable:!0,configurable:!0})}return i(e,[{key:"addEventListener",value:function(e,t,n){e in this.listeners||(this.listeners[e]=[]),this.listeners[e].push({callback:t,options:n})}},{key:"removeEventListener",value:function(e,t){if(e in this.listeners)for(var n=this.listeners[e],r=0,o=n.length;r<o;r++)if(n[r].callback===t)return void n.splice(r,1)}},{key:"dispatchEvent",value:function(e){var t=this;if(e.type in this.listeners){for(var n=this.listeners[e.type].slice(),r=function(){var r=n[o];try{r.callback.call(t,e)}catch(G_){Promise.resolve().then((function(){throw G_}))}r.options&&r.options.once&&t.removeEventListener(e.type,r.callback)},o=0,i=n.length;o<i;o++)r();return!e.defaultPrevented}}}])}(),x=function(e){function t(){var e;return r(this,t),(e=n(this,t)).listeners||w.call(e),Object.defineProperty(e,"aborted",{value:!1,writable:!0,configurable:!0}),Object.defineProperty(e,"onabort",{value:null,writable:!0,configurable:!0}),Object.defineProperty(e,"reason",{value:void 0,writable:!0,configurable:!0}),e}return u(t,e),i(t,[{key:"toString",value:function(){return"[object AbortSignal]"}},{key:"dispatchEvent",value:function(e){"abort"===e.type&&(this.aborted=!0,"function"==typeof this.onabort&&this.onabort.call(this,e)),d(t,"dispatchEvent",this,3)([e])}},{key:"throwIfAborted",value:function(){var e=this.aborted,t=this.reason;if(e)throw void 0===t?"Aborted":t}}],[{key:"timeout",value:function(e){var t=new S;return setTimeout((function(){return t.abort(new DOMException("This signal is timeout in ".concat(e,"ms"),"TimeoutError"))}),e),t.signal}},{key:"any",value:function(e){var t=new S;function n(){t.abort(this.reason),r()}function r(){var t,r=s(e);try{for(r.s();!(t=r.n()).done;)t.value.removeEventListener("abort",n)}catch(o){r.e(o)}finally{r.f()}}var o,i=s(e);try{for(i.s();!(o=i.n()).done;){var a=o.value;if(a.aborted){t.abort(a.reason);break}a.addEventListener("abort",n)}}catch(c){i.e(c)}finally{i.f()}return t.signal}}])}(w),S=function(){function e(){r(this,e),Object.defineProperty(this,"signal",{value:new x,writable:!0,configurable:!0})}return i(e,[{key:"abort",value:function(e){var t=_(e),n=y(t);this.signal.reason=t,this.signal.dispatchEvent(n)}},{key:"toString",value:function(){return"[object AbortController]"}}])}();function O(e){return e.__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL?(console.log("__FORCE_INSTALL_ABORTCONTROLLER_POLYFILL=true is set, will force install polyfill"),!0):"function"==typeof e.Request&&!e.Request.prototype.hasOwnProperty("signal")||!e.AbortController}"undefined"!=typeof Symbol&&Symbol.toStringTag&&(S.prototype[Symbol.toStringTag]="AbortController",x.prototype[Symbol.toStringTag]="AbortSignal"),function(e){O(e)&&(e.AbortController=S,e.AbortSignal=x)}("undefined"!=typeof self?self:Qs)}();const Xs=e=>(t,n=ui())=>{!gi&&bo(e,t,n)},ea=Xs("onShow"),ta=Xs("onHide"),na=Xs("onLaunch"),ra=Xs("onLoad"),oa=Xs("onReady"),ia=Xs("onUnload"),sa=Xs("onBackPress"),aa=Xs("onPageScroll"),ca=Xs("onReachBottom"),ua=Xs("onPullDownRefresh"),la=Xs("onShareTimeline"),fa=Xs("onShareAppMessage");var ha=Object.prototype.toString;function pa(e){return"[object Array]"===ha.call(e)}function da(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),pa(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}function ga(){let e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=ga(e[n],t):e[n]="object"==typeof t?ga({},t):t}for(let n=0,r=arguments.length;n<r;n++)da(arguments[n],t);return e}function va(e){return void 0===e}function ma(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ya(e,t,n){if(!t)return e;var r,o;if(n)r=n(t);else if(o=t,"undefined"!=typeof URLSearchParams&&o instanceof URLSearchParams)r=t.toString();else{var i=[];da(t,(function(e,t){null!=e&&(pa(e)?t+="[]":e=[e],da(e,(function(e){!function(e){return"[object Date]"===ha.call(e)}(e)?function(e){return null!==e&&"object"==typeof e}(e)&&(e=JSON.stringify(e)):e=e.toISOString(),i.push(ma(t)+"="+ma(e))})))})),r=i.join("&")}if(r){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e}const _a=(e,t)=>{let n={};return e.forEach((e=>{va(t[e])||(n[e]=t[e])})),n},ba=e=>(e=>new Promise(((t,n)=>{let r=ya((o=e.baseURL,i=e.url,o&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(o,i):i),e.params,e.paramsSerializer);var o,i;const s={url:r,header:e.header,complete:o=>{e.fullPath=r,o.config=e,o.rawData=o.data;try{let t=!1;const n=typeof e.forcedJSONParsing;"boolean"===n?t=e.forcedJSONParsing:"object"===n&&(t=(e.forcedJSONParsing.include||[]).includes(e.method)),t&&"string"==typeof o.data&&(o.data=JSON.parse(o.data))}catch(G_){}!function(e,t,n){const r=n.config.validateStatus,o=n.statusCode;!o||r&&!r(o)?t(n):e(n)}(t,n,o)}};let a;if("UPLOAD"===e.method){delete s.header["content-type"],delete s.header["Content-Type"];let t={filePath:e.filePath,name:e.name};const n=["timeout","formData"];a=en.uploadFile({...s,...t,..._a(n,e)})}else if("DOWNLOAD"===e.method){const t=["timeout","filePath"];a=en.downloadFile({...s,..._a(t,e)})}else{const t=["data","method","timeout","dataType","responseType","enableHttp2","enableQuic","enableCache","enableHttpDNS","httpDNSServiceId","enableChunked","forceCellularNetwork"];a=en.request({...s,..._a(t,e)})}e.getTask&&e.getTask(a,e)})))(e);function wa(){this.handlers=[]}wa.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},wa.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},wa.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const xa=(e,t,n)=>{let r={};return e.forEach((e=>{va(n[e])?va(t[e])||(r[e]=t[e]):r[e]=n[e]})),r},Sa={baseURL:"",header:{},method:"GET",dataType:"json",paramsSerializer:null,responseType:"text",custom:{},timeout:6e4,validateStatus:function(e){return e>=200&&e<300},forcedJSONParsing:!0};var Oa=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,r;try{t=Map}catch(a){t=function(){}}try{n=Set}catch(a){n=function(){}}try{r=Promise}catch(a){r=function(){}}function o(i,a,c,u,l){"object"==typeof a&&(c=a.depth,u=a.prototype,l=a.includeNonEnumerable,a=a.circular);var f=[],h=[],p="undefined"!=typeof Buffer;return void 0===a&&(a=!0),void 0===c&&(c=1/0),function i(c,d){if(null===c)return null;if(0===d)return c;var g,v;if("object"!=typeof c)return c;if(e(c,t))g=new t;else if(e(c,n))g=new n;else if(e(c,r))g=new r((function(e,t){c.then((function(t){e(i(t,d-1))}),(function(e){t(i(e,d-1))}))}));else if(o.__isArray(c))g=[];else if(o.__isRegExp(c))g=new RegExp(c.source,s(c)),c.lastIndex&&(g.lastIndex=c.lastIndex);else if(o.__isDate(c))g=new Date(c.getTime());else{if(p&&Buffer.isBuffer(c))return Buffer.from?g=Buffer.from(c):(g=new Buffer(c.length),c.copy(g)),g;e(c,Error)?g=Object.create(c):void 0===u?(v=Object.getPrototypeOf(c),g=Object.create(v)):(g=Object.create(u),v=u)}if(a){var m=f.indexOf(c);if(-1!=m)return h[m];f.push(c),h.push(g)}for(var y in e(c,t)&&c.forEach((function(e,t){var n=i(t,d-1),r=i(e,d-1);g.set(n,r)})),e(c,n)&&c.forEach((function(e){var t=i(e,d-1);g.add(t)})),c){Object.getOwnPropertyDescriptor(c,y)&&(g[y]=i(c[y],d-1));try{if("undefined"===Object.getOwnPropertyDescriptor(c,y).set)continue;g[y]=i(c[y],d-1)}catch(G_){if(G_ instanceof TypeError)continue;if(G_ instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var _=Object.getOwnPropertySymbols(c);for(y=0;y<_.length;y++){var b=_[y];(!(x=Object.getOwnPropertyDescriptor(c,b))||x.enumerable||l)&&(g[b]=i(c[b],d-1),Object.defineProperty(g,b,x))}}if(l){var w=Object.getOwnPropertyNames(c);for(y=0;y<w.length;y++){var x,S=w[y];(x=Object.getOwnPropertyDescriptor(c,S))&&x.enumerable||(g[S]=i(c[S],d-1),Object.defineProperty(g,S,x))}}return g}(i,c)}function i(e){return Object.prototype.toString.call(e)}function s(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return o.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},o.__objToStr=i,o.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},o.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},o.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},o.__getRegExpFlags=s,o}();var ka="object"==typeof global&&global&&global.Object===Object&&global,Ta="object"==typeof self&&self&&self.Object===Object&&self,Aa=ka||Ta||Function("return this")(),Ia=Aa.Symbol,Pa=Object.prototype,ja=Pa.hasOwnProperty,$a=Pa.toString,Ea=Ia?Ia.toStringTag:void 0;var La=Object.prototype.toString;var Ca=Ia?Ia.toStringTag:void 0;function Ma(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Ca&&Ca in Object(e)?function(e){var t=ja.call(e,Ea),n=e[Ea];try{e[Ea]=void 0;var r=!0}catch(G_){}var o=$a.call(e);return r&&(t?e[Ea]=n:delete e[Ea]),o}(e):function(e){return La.call(e)}(e)}function Ra(e){return null!=e&&"object"==typeof e}function Da(e){return"symbol"==typeof e||Ra(e)&&"[object Symbol]"==Ma(e)}function Na(e){return"number"==typeof e?e:Da(e)?NaN:+e}function Ba(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}var Ua=Array.isArray,Fa=Ia?Ia.prototype:void 0,qa=Fa?Fa.toString:void 0;function Wa(e){if("string"==typeof e)return e;if(Ua(e))return Ba(e,Wa)+"";if(Da(e))return qa?qa.call(e):"";var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t}function Ha(e,t){return function(n,r){var o;if(void 0===n&&void 0===r)return t;if(void 0!==n&&(o=n),void 0!==r){if(void 0===o)return r;"string"==typeof n||"string"==typeof r?(n=Wa(n),r=Wa(r)):(n=Na(n),r=Na(r)),o=e(n,r)}return o}}const za=Ha((function(e,t){return e+t}),0);var Va=/\s/;function Ka(e){for(var t=e.length;t--&&Va.test(e.charAt(t)););return t}var Ja=/^\s+/;function Ya(e){return e?e.slice(0,Ka(e)+1).replace(Ja,""):e}function Ga(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}var Qa=/^[-+]0x[0-9a-f]+$/i,Za=/^0b[01]+$/i,Xa=/^0o[0-7]+$/i,ec=parseInt;function tc(e){if("number"==typeof e)return e;if(Da(e))return NaN;if(Ga(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Ga(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Ya(e);var n=Za.test(e);return n||Xa.test(e)?ec(e.slice(2),n?2:8):Qa.test(e)?NaN:+e}function nc(e){return e?Infinity===(e=tc(e))||-Infinity===e?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function rc(e){var t=nc(e),n=t%1;return t==t?n?t-n:t:0}function oc(e){return e}function ic(e){if(!Ga(e))return!1;var t=Ma(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}var sc,ac=Aa["__core-js_shared__"],cc=(sc=/[^.]+$/.exec(ac&&ac.keys&&ac.keys.IE_PROTO||""))?"Symbol(src)_1."+sc:"";var uc=Function.prototype.toString;function lc(e){if(null!=e){try{return uc.call(e)}catch(G_){}try{return e+""}catch(G_){}}return""}var fc=/^\[object .+?Constructor\]$/,hc=Function.prototype,pc=Object.prototype,dc=hc.toString,gc=pc.hasOwnProperty,vc=RegExp("^"+dc.call(gc).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function mc(e){return!(!Ga(e)||(t=e,cc&&cc in t))&&(ic(e)?vc:fc).test(lc(e));var t}function yc(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return mc(n)?n:void 0}var _c=yc(Aa,"WeakMap"),bc=_c&&new _c,wc=bc?function(e,t){return bc.set(e,t),e}:oc,xc=Object.create,Sc=function(){function e(){}return function(t){if(!Ga(t))return{};if(xc)return xc(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();function Oc(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Sc(e.prototype),r=e.apply(n,t);return Ga(r)?r:n}}function kc(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}var Tc=Math.max;function Ac(e,t,n,r){for(var o=-1,i=e.length,s=n.length,a=-1,c=t.length,u=Tc(i-s,0),l=Array(c+u),f=!r;++a<c;)l[a]=t[a];for(;++o<s;)(f||o<i)&&(l[n[o]]=e[o]);for(;u--;)l[a++]=e[o++];return l}var Ic=Math.max;function Pc(e,t,n,r){for(var o=-1,i=e.length,s=-1,a=n.length,c=-1,u=t.length,l=Ic(i-a,0),f=Array(l+u),h=!r;++o<l;)f[o]=e[o];for(var p=o;++c<u;)f[p+c]=t[c];for(;++s<a;)(h||o<i)&&(f[p+n[s]]=e[o++]);return f}function jc(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}function $c(){}function Ec(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=**********,this.__views__=[]}function Lc(){}Ec.prototype=Sc($c.prototype),Ec.prototype.constructor=Ec;var Cc=bc?function(e){return bc.get(e)}:Lc,Mc={},Rc=Object.prototype.hasOwnProperty;function Dc(e){for(var t=e.name+"",n=Mc[t],r=Rc.call(Mc,t)?n.length:0;r--;){var o=n[r],i=o.func;if(null==i||i==e)return o.name}return t}function Nc(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=void 0}function Bc(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}function Uc(e){if(e instanceof Ec)return e.clone();var t=new Nc(e.__wrapped__,e.__chain__);return t.__actions__=Bc(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}Nc.prototype=Sc($c.prototype),Nc.prototype.constructor=Nc;var Fc=Object.prototype.hasOwnProperty;function qc(e){if(Ra(e)&&!Ua(e)&&!(e instanceof Ec)){if(e instanceof Nc)return e;if(Fc.call(e,"__wrapped__"))return Uc(e)}return new Nc(e)}function Wc(e){var t=Dc(e),n=qc[t];if("function"!=typeof n||!(t in Ec.prototype))return!1;if(e===n)return!0;var r=Cc(n);return!!r&&e===r[0]}qc.prototype=$c.prototype,qc.prototype.constructor=qc;var Hc=Date.now;function zc(e){var t=0,n=0;return function(){var r=Hc(),o=16-(r-n);if(n=r,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var Vc=zc(wc),Kc=/\{\n\/\* \[wrapped with (.+)\] \*/,Jc=/,? & /;var Yc=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/;function Gc(e){return function(){return e}}var Qc=function(){try{var e=yc(Object,"defineProperty");return e({},"",{}),e}catch(G_){}}(),Zc=Qc?function(e,t){return Qc(e,"toString",{configurable:!0,enumerable:!1,value:Gc(t),writable:!0})}:oc;var Xc=zc(Zc);function eu(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function tu(e,t,n,r){for(var o=e.length,i=n+(r?1:-1);r?i--:++i<o;)if(t(e[i],i,e))return i;return-1}function nu(e){return e!=e}function ru(e,t,n){return t==t?function(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}(e,t,n):tu(e,nu,n)}function ou(e,t){return!!(null==e?0:e.length)&&ru(e,t,0)>-1}var iu=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]];function su(e,t,n){var r=t+"";return Xc(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(Yc,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return eu(iu,(function(n){var r="_."+n[0];t&n[1]&&!ou(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(Kc);return t?t[1].split(Jc):[]}(r),n)))}function au(e,t,n,r,o,i,s,a,c,u){var l=8&t;t|=l?32:64,4&(t&=~(l?64:32))||(t&=-4);var f=[e,t,o,l?i:void 0,l?s:void 0,l?void 0:i,l?void 0:s,a,c,u],h=n.apply(void 0,f);return Wc(e)&&Vc(h,f),h.placeholder=r,su(h,e,t)}function cu(e){return e.placeholder}var uu=/^(?:0|[1-9]\d*)$/;function lu(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&uu.test(e))&&e>-1&&e%1==0&&e<t}var fu=Math.min;function hu(e,t){for(var n=e.length,r=fu(t.length,n),o=Bc(e);r--;){var i=t[r];e[r]=lu(i,n)?o[i]:void 0}return e}function pu(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var s=e[n];s!==t&&"__lodash_placeholder__"!==s||(e[n]="__lodash_placeholder__",i[o++]=n)}return i}function du(e,t,n,r,o,i,s,a,c,u){var l=128&t,f=1&t,h=2&t,p=24&t,d=512&t,g=h?void 0:Oc(e);return function v(){for(var m=arguments.length,y=Array(m),_=m;_--;)y[_]=arguments[_];if(p)var b=cu(v),w=jc(y,b);if(r&&(y=Ac(y,r,o,p)),i&&(y=Pc(y,i,s,p)),m-=w,p&&m<u){var x=pu(y,b);return au(e,t,du,v.placeholder,n,y,x,a,c,u-m)}var S=f?n:this,O=h?S[e]:e;return m=y.length,a?y=hu(y,a):d&&m>1&&y.reverse(),l&&c<m&&(y.length=c),this&&this!==Aa&&this instanceof v&&(O=g||Oc(O)),O.apply(S,y)}}var gu=Math.min;var vu=Math.max;function mu(e,t,n,r,o,i,s,a){var c=2&t;if(!c&&"function"!=typeof e)throw new TypeError("Expected a function");var u=r?r.length:0;if(u||(t&=-97,r=o=void 0),s=void 0===s?s:vu(rc(s),0),a=void 0===a?a:rc(a),u-=o?o.length:0,64&t){var l=r,f=o;r=o=void 0}var h=c?void 0:Cc(e),p=[e,t,n,r,o,l,f,i,s,a];if(h&&function(e,t){var n=e[1],r=t[1],o=n|r,i=o<131,s=128==r&&8==n||128==r&&256==n&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!i&&!s)return e;1&r&&(e[2]=t[2],o|=1&n?0:4);var a=t[3];if(a){var c=e[3];e[3]=c?Ac(c,a,t[4]):a,e[4]=c?pu(e[3],"__lodash_placeholder__"):t[4]}(a=t[5])&&(c=e[5],e[5]=c?Pc(c,a,t[6]):a,e[6]=c?pu(e[5],"__lodash_placeholder__"):t[6]),(a=t[7])&&(e[7]=a),128&r&&(e[8]=null==e[8]?t[8]:gu(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=o}(p,h),e=p[0],t=p[1],n=p[2],r=p[3],o=p[4],!(a=p[9]=void 0===p[9]?c?0:e.length:vu(p[9]-u,0))&&24&t&&(t&=-25),t&&1!=t)d=8==t||16==t?function(e,t,n){var r=Oc(e);return function o(){for(var i=arguments.length,s=Array(i),a=i,c=cu(o);a--;)s[a]=arguments[a];var u=i<3&&s[0]!==c&&s[i-1]!==c?[]:pu(s,c);return(i-=u.length)<n?au(e,t,du,o.placeholder,void 0,s,u,void 0,void 0,n-i):kc(this&&this!==Aa&&this instanceof o?r:e,this,s)}}(e,t,a):32!=t&&33!=t||o.length?du.apply(void 0,p):function(e,t,n,r){var o=1&t,i=Oc(e);return function t(){for(var s=-1,a=arguments.length,c=-1,u=r.length,l=Array(u+a),f=this&&this!==Aa&&this instanceof t?i:e;++c<u;)l[c]=r[c];for(;a--;)l[c++]=arguments[++s];return kc(f,o?n:this,l)}}(e,t,n,r);else var d=function(e,t,n){var r=1&t,o=Oc(e);return function t(){return(this&&this!==Aa&&this instanceof t?o:e).apply(r?n:this,arguments)}}(e,t,n);return su((h?wc:Vc)(d,p),e,t)}function yu(e,t,n){return t=n?void 0:t,mu(e,128,void 0,void 0,void 0,void 0,t=e&&null==t?e.length:t)}function _u(e,t,n){"__proto__"==t&&Qc?Qc(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function bu(e,t){return e===t||e!=e&&t!=t}var wu=Object.prototype.hasOwnProperty;function xu(e,t,n){var r=e[t];wu.call(e,t)&&bu(r,n)&&(void 0!==n||t in e)||_u(e,t,n)}function Su(e,t,n,r){var o=!n;n||(n={});for(var i=-1,s=t.length;++i<s;){var a=t[i],c=r?r(n[a],e[a],a,n,e):void 0;void 0===c&&(c=e[a]),o?_u(n,a,c):xu(n,a,c)}return n}var Ou=Math.max;function ku(e,t,n){return t=Ou(void 0===t?e.length-1:t,0),function(){for(var r=arguments,o=-1,i=Ou(r.length-t,0),s=Array(i);++o<i;)s[o]=r[t+o];o=-1;for(var a=Array(t+1);++o<t;)a[o]=r[o];return a[t]=n(s),kc(e,this,a)}}function Tu(e,t){return Xc(ku(e,t,oc),e+"")}function Au(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function Iu(e){return null!=e&&Au(e.length)&&!ic(e)}function Pu(e,t,n){if(!Ga(n))return!1;var r=typeof t;return!!("number"==r?Iu(n)&&lu(t,n.length):"string"==r&&t in n)&&bu(n[t],e)}function ju(e){return Tu((function(t,n){var r=-1,o=n.length,i=o>1?n[o-1]:void 0,s=o>2?n[2]:void 0;for(i=e.length>3&&"function"==typeof i?(o--,i):void 0,s&&Pu(n[0],n[1],s)&&(i=o<3?void 0:i,o=1),t=Object(t);++r<o;){var a=n[r];a&&e(t,a,r,i)}return t}))}var $u=Object.prototype;function Eu(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||$u)}function Lu(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function Cu(e){return Ra(e)&&"[object Arguments]"==Ma(e)}var Mu=Object.prototype,Ru=Mu.hasOwnProperty,Du=Mu.propertyIsEnumerable;const Nu=Cu(function(){return arguments}())?Cu:function(e){return Ra(e)&&Ru.call(e,"callee")&&!Du.call(e,"callee")};function Bu(){return!1}var Uu="object"==typeof exports&&exports&&!exports.nodeType&&exports,Fu=Uu&&"object"==typeof module&&module&&!module.nodeType&&module,qu=Fu&&Fu.exports===Uu?Aa.Buffer:void 0;const Wu=(qu?qu.isBuffer:void 0)||Bu;var Hu={};function zu(e){return function(t){return e(t)}}Hu["[object Float32Array]"]=Hu["[object Float64Array]"]=Hu["[object Int8Array]"]=Hu["[object Int16Array]"]=Hu["[object Int32Array]"]=Hu["[object Uint8Array]"]=Hu["[object Uint8ClampedArray]"]=Hu["[object Uint16Array]"]=Hu["[object Uint32Array]"]=!0,Hu["[object Arguments]"]=Hu["[object Array]"]=Hu["[object ArrayBuffer]"]=Hu["[object Boolean]"]=Hu["[object DataView]"]=Hu["[object Date]"]=Hu["[object Error]"]=Hu["[object Function]"]=Hu["[object Map]"]=Hu["[object Number]"]=Hu["[object Object]"]=Hu["[object RegExp]"]=Hu["[object Set]"]=Hu["[object String]"]=Hu["[object WeakMap]"]=!1;var Vu="object"==typeof exports&&exports&&!exports.nodeType&&exports,Ku=Vu&&"object"==typeof module&&module&&!module.nodeType&&module,Ju=Ku&&Ku.exports===Vu&&ka.process,Yu=function(){try{var e=Ku&&Ku.require&&Ku.require("util").types;return e||Ju&&Ju.binding&&Ju.binding("util")}catch(G_){}}(),Gu=Yu&&Yu.isTypedArray;const Qu=Gu?zu(Gu):function(e){return Ra(e)&&Au(e.length)&&!!Hu[Ma(e)]};var Zu=Object.prototype.hasOwnProperty;function Xu(e,t){var n=Ua(e),r=!n&&Nu(e),o=!n&&!r&&Wu(e),i=!n&&!r&&!o&&Qu(e),s=n||r||o||i,a=s?Lu(e.length,String):[],c=a.length;for(var u in e)!t&&!Zu.call(e,u)||s&&("length"==u||o&&("offset"==u||"parent"==u)||i&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||lu(u,c))||a.push(u);return a}function el(e,t){return function(n){return e(t(n))}}var tl=el(Object.keys,Object),nl=Object.prototype.hasOwnProperty;function rl(e){if(!Eu(e))return tl(e);var t=[];for(var n in Object(e))nl.call(e,n)&&"constructor"!=n&&t.push(n);return t}function ol(e){return Iu(e)?Xu(e):rl(e)}var il=Object.prototype.hasOwnProperty,sl=ju((function(e,t){if(Eu(t)||Iu(t))Su(t,ol(t),e);else for(var n in t)il.call(t,n)&&xu(e,n,t[n])}));const al=sl;var cl=Object.prototype.hasOwnProperty;function ul(e){if(!Ga(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=Eu(e),n=[];for(var r in e)("constructor"!=r||!t&&cl.call(e,r))&&n.push(r);return n}function ll(e){return Iu(e)?Xu(e,!0):ul(e)}var fl=ju((function(e,t){Su(t,ll(t),e)}));const hl=fl;var pl=ju((function(e,t,n,r){Su(t,ll(t),e,r)}));const dl=pl;var gl=ju((function(e,t,n,r){Su(t,ol(t),e,r)}));const vl=gl;var ml=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,yl=/^\w*$/;function _l(e,t){if(Ua(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Da(e))||(yl.test(e)||!ml.test(e)||null!=t&&e in Object(t))}var bl=yc(Object,"create");var wl=Object.prototype.hasOwnProperty;var xl=Object.prototype.hasOwnProperty;function Sl(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Ol(e,t){for(var n=e.length;n--;)if(bu(e[n][0],t))return n;return-1}Sl.prototype.clear=function(){this.__data__=bl?bl(null):{},this.size=0},Sl.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Sl.prototype.get=function(e){var t=this.__data__;if(bl){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return wl.call(t,e)?t[e]:void 0},Sl.prototype.has=function(e){var t=this.__data__;return bl?void 0!==t[e]:xl.call(t,e)},Sl.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=bl&&void 0===t?"__lodash_hash_undefined__":t,this};var kl=Array.prototype.splice;function Tl(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Tl.prototype.clear=function(){this.__data__=[],this.size=0},Tl.prototype.delete=function(e){var t=this.__data__,n=Ol(t,e);return!(n<0)&&(n==t.length-1?t.pop():kl.call(t,n,1),--this.size,!0)},Tl.prototype.get=function(e){var t=this.__data__,n=Ol(t,e);return n<0?void 0:t[n][1]},Tl.prototype.has=function(e){return Ol(this.__data__,e)>-1},Tl.prototype.set=function(e,t){var n=this.__data__,r=Ol(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};var Al=yc(Aa,"Map");function Il(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map}function Pl(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Pl.prototype.clear=function(){this.size=0,this.__data__={hash:new Sl,map:new(Al||Tl),string:new Sl}},Pl.prototype.delete=function(e){var t=Il(this,e).delete(e);return this.size-=t?1:0,t},Pl.prototype.get=function(e){return Il(this,e).get(e)},Pl.prototype.has=function(e){return Il(this,e).has(e)},Pl.prototype.set=function(e,t){var n=Il(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function jl(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var s=e.apply(this,r);return n.cache=i.set(o,s)||i,s};return n.cache=new(jl.Cache||Pl),n}jl.Cache=Pl;var $l,El,Ll,Cl=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ml=/\\(\\)?/g,Rl=($l=function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Cl,(function(e,n,r,o){t.push(r?o.replace(Ml,"$1"):n||e)})),t},El=jl($l,(function(e){return 500===Ll.size&&Ll.clear(),e})),Ll=El.cache,El);function Dl(e){return null==e?"":Wa(e)}function Nl(e,t){return Ua(e)?e:_l(e,t)?[e]:Rl(Dl(e))}function Bl(e){if("string"==typeof e||Da(e))return e;var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t}function Ul(e,t){for(var n=0,r=(t=Nl(t,e)).length;null!=e&&n<r;)e=e[Bl(t[n++])];return n&&n==r?e:void 0}function Fl(e,t,n){var r=null==e?void 0:Ul(e,t);return void 0===r?n:r}function ql(e,t){for(var n=-1,r=t.length,o=Array(r),i=null==e;++n<r;)o[n]=i?void 0:Fl(e,t[n]);return o}function Wl(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}var Hl=Ia?Ia.isConcatSpreadable:void 0;function zl(e){return Ua(e)||Nu(e)||!!(Hl&&e&&e[Hl])}function Vl(e,t,n,r,o){var i=-1,s=e.length;for(n||(n=zl),o||(o=[]);++i<s;){var a=e[i];t>0&&n(a)?t>1?Vl(a,t-1,n,r,o):Wl(o,a):r||(o[o.length]=a)}return o}function Kl(e){return(null==e?0:e.length)?Vl(e,1):[]}function Jl(e){return Xc(ku(e,void 0,Kl),e+"")}const Yl=Jl(ql);var Gl=el(Object.getPrototypeOf,Object),Ql=Function.prototype,Zl=Object.prototype,Xl=Ql.toString,ef=Zl.hasOwnProperty,tf=Xl.call(Object);function nf(e){if(!Ra(e)||"[object Object]"!=Ma(e))return!1;var t=Gl(e);if(null===t)return!0;var n=ef.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Xl.call(n)==tf}function rf(e){if(!Ra(e))return!1;var t=Ma(e);return"[object Error]"==t||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!nf(e)}var of=Tu((function(e,t){try{return kc(e,void 0,t)}catch(G_){return rf(G_)?G_:new Error(G_)}}));const sf=of;function af(e,t){var n;if("function"!=typeof t)throw new TypeError("Expected a function");return e=rc(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=void 0),n}}var cf=Tu((function(e,t,n){var r=1;if(n.length){var o=pu(n,cu(cf));r|=32}return mu(e,r,t,n,o)}));cf.placeholder={};const uf=cf;const lf=Jl((function(e,t){return eu(t,(function(t){t=Bl(t),_u(e,t,uf(e[t],e))})),e}));var ff=Tu((function(e,t,n){var r=3;if(n.length){var o=pu(n,cu(ff));r|=32}return mu(t,r,e,n,o)}));ff.placeholder={};const hf=ff;function pf(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var i=Array(o);++r<o;)i[r]=e[r+t];return i}function df(e,t,n){var r=e.length;return n=void 0===n?r:n,!t&&n>=r?e:pf(e,t,n)}var gf=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");function vf(e){return gf.test(e)}var mf="[\\ud800-\\udfff]",yf="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",_f="\\ud83c[\\udffb-\\udfff]",bf="[^\\ud800-\\udfff]",wf="(?:\\ud83c[\\udde6-\\uddff]){2}",xf="[\\ud800-\\udbff][\\udc00-\\udfff]",Sf="(?:"+yf+"|"+_f+")"+"?",Of="[\\ufe0e\\ufe0f]?"+Sf+("(?:\\u200d(?:"+[bf,wf,xf].join("|")+")[\\ufe0e\\ufe0f]?"+Sf+")*"),kf="(?:"+[bf+yf+"?",yf,wf,xf,mf].join("|")+")",Tf=RegExp(_f+"(?="+_f+")|"+kf+Of,"g");function Af(e){return vf(e)?function(e){return e.match(Tf)||[]}(e):function(e){return e.split("")}(e)}function If(e){return function(t){var n=vf(t=Dl(t))?Af(t):void 0,r=n?n[0]:t.charAt(0),o=n?df(n,1).join(""):t.slice(1);return r[e]()+o}}const Pf=If("toUpperCase");function jf(e){return Pf(Dl(e).toLowerCase())}function $f(e,t,n,r){var o=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function Ef(e){return function(t){return null==e?void 0:e[t]}}const Lf=Ef({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"});var Cf=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Mf=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");function Rf(e){return(e=Dl(e))&&e.replace(Cf,Lf).replace(Mf,"")}var Df=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;var Nf=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;var Bf="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Uf="["+Bf+"]",Ff="\\d+",qf="[\\u2700-\\u27bf]",Wf="[a-z\\xdf-\\xf6\\xf8-\\xff]",Hf="[^\\ud800-\\udfff"+Bf+Ff+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",zf="(?:\\ud83c[\\udde6-\\uddff]){2}",Vf="[\\ud800-\\udbff][\\udc00-\\udfff]",Kf="[A-Z\\xc0-\\xd6\\xd8-\\xde]",Jf="(?:"+Wf+"|"+Hf+")",Yf="(?:"+Kf+"|"+Hf+")",Gf="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",Qf="[\\ufe0e\\ufe0f]?"+Gf+("(?:\\u200d(?:"+["[^\\ud800-\\udfff]",zf,Vf].join("|")+")[\\ufe0e\\ufe0f]?"+Gf+")*"),Zf="(?:"+[qf,zf,Vf].join("|")+")"+Qf,Xf=RegExp([Kf+"?"+Wf+"+(?:['’](?:d|ll|m|re|s|t|ve))?(?="+[Uf,Kf,"$"].join("|")+")",Yf+"+(?:['’](?:D|LL|M|RE|S|T|VE))?(?="+[Uf,Kf+Jf,"$"].join("|")+")",Kf+"?"+Jf+"+(?:['’](?:d|ll|m|re|s|t|ve))?",Kf+"+(?:['’](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ff,Zf].join("|"),"g");function eh(e,t,n){return e=Dl(e),void 0===(t=n?void 0:t)?function(e){return Nf.test(e)}(e)?function(e){return e.match(Xf)||[]}(e):function(e){return e.match(Df)||[]}(e):e.match(t)||[]}var th=RegExp("['’]","g");function nh(e){return function(t){return $f(eh(Rf(t).replace(th,"")),e,"")}}var rh=nh((function(e,t,n){return t=t.toLowerCase(),e+(n?jf(t):t)}));const oh=rh;var ih=Aa.isFinite,sh=Math.min;function ah(e){var t=Math[e];return function(e,n){if(e=tc(e),(n=null==n?0:sh(rc(n),292))&&ih(e)){var r=(Dl(e)+"e").split("e");return+((r=(Dl(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}const ch=ah("ceil");function uh(e){var t=qc(e);return t.__chain__=!0,t}var lh=Math.ceil,fh=Math.max;function hh(e,t,n){return e==e&&(void 0!==n&&(e=e<=n?e:n),void 0!==t&&(e=e>=t?e:t)),e}function ph(e){var t=this.__data__=new Tl(e);this.size=t.size}function dh(e,t){return e&&Su(t,ol(t),e)}ph.prototype.clear=function(){this.__data__=new Tl,this.size=0},ph.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ph.prototype.get=function(e){return this.__data__.get(e)},ph.prototype.has=function(e){return this.__data__.has(e)},ph.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Tl){var r=n.__data__;if(!Al||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Pl(r)}return n.set(e,t),this.size=n.size,this};var gh="object"==typeof exports&&exports&&!exports.nodeType&&exports,vh=gh&&"object"==typeof module&&module&&!module.nodeType&&module,mh=vh&&vh.exports===gh?Aa.Buffer:void 0,yh=mh?mh.allocUnsafe:void 0;function _h(e,t){if(t)return e.slice();var n=e.length,r=yh?yh(n):new e.constructor(n);return e.copy(r),r}function bh(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var s=e[n];t(s,n,e)&&(i[o++]=s)}return i}function wh(){return[]}var xh=Object.prototype.propertyIsEnumerable,Sh=Object.getOwnPropertySymbols,Oh=Sh?function(e){return null==e?[]:(e=Object(e),bh(Sh(e),(function(t){return xh.call(e,t)})))}:wh;var kh=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)Wl(t,Oh(e)),e=Gl(e);return t}:wh;function Th(e,t,n){var r=t(e);return Ua(e)?r:Wl(r,n(e))}function Ah(e){return Th(e,ol,Oh)}function Ih(e){return Th(e,ll,kh)}var Ph=yc(Aa,"DataView"),jh=yc(Aa,"Promise"),$h=yc(Aa,"Set"),Eh=lc(Ph),Lh=lc(Al),Ch=lc(jh),Mh=lc($h),Rh=lc(_c),Dh=Ma;(Ph&&"[object DataView]"!=Dh(new Ph(new ArrayBuffer(1)))||Al&&"[object Map]"!=Dh(new Al)||jh&&"[object Promise]"!=Dh(jh.resolve())||$h&&"[object Set]"!=Dh(new $h)||_c&&"[object WeakMap]"!=Dh(new _c))&&(Dh=function(e){var t=Ma(e),n="[object Object]"==t?e.constructor:void 0,r=n?lc(n):"";if(r)switch(r){case Eh:return"[object DataView]";case Lh:return"[object Map]";case Ch:return"[object Promise]";case Mh:return"[object Set]";case Rh:return"[object WeakMap]"}return t});const Nh=Dh;var Bh=Object.prototype.hasOwnProperty;var Uh=Aa.Uint8Array;function Fh(e){var t=new e.constructor(e.byteLength);return new Uh(t).set(new Uh(e)),t}var qh=/\w*$/;var Wh=Ia?Ia.prototype:void 0,Hh=Wh?Wh.valueOf:void 0;function zh(e,t){var n=t?Fh(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function Vh(e,t,n){var r,o=e.constructor;switch(t){case"[object ArrayBuffer]":return Fh(e);case"[object Boolean]":case"[object Date]":return new o(+e);case"[object DataView]":return function(e,t){var n=t?Fh(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return zh(e,n);case"[object Map]":case"[object Set]":return new o;case"[object Number]":case"[object String]":return new o(e);case"[object RegExp]":return function(e){var t=new e.constructor(e.source,qh.exec(e));return t.lastIndex=e.lastIndex,t}(e);case"[object Symbol]":return r=e,Hh?Object(Hh.call(r)):{}}}function Kh(e){return"function"!=typeof e.constructor||Eu(e)?{}:Sc(Gl(e))}var Jh=Yu&&Yu.isMap,Yh=Jh?zu(Jh):function(e){return Ra(e)&&"[object Map]"==Nh(e)};var Gh=Yu&&Yu.isSet,Qh=Gh?zu(Gh):function(e){return Ra(e)&&"[object Set]"==Nh(e)},Zh={};function Xh(e,t,n,r,o,i){var s,a=1&t,c=2&t,u=4&t;if(n&&(s=o?n(e,r,o,i):n(e)),void 0!==s)return s;if(!Ga(e))return e;var l=Ua(e);if(l){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Bh.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!a)return Bc(e,s)}else{var f=Nh(e),h="[object Function]"==f||"[object GeneratorFunction]"==f;if(Wu(e))return _h(e,a);if("[object Object]"==f||"[object Arguments]"==f||h&&!o){if(s=c||h?{}:Kh(e),!a)return c?function(e,t){return Su(e,kh(e),t)}(e,function(e,t){return e&&Su(t,ll(t),e)}(s,e)):function(e,t){return Su(e,Oh(e),t)}(e,dh(s,e))}else{if(!Zh[f])return o?e:{};s=Vh(e,f,a)}}i||(i=new ph);var p=i.get(e);if(p)return p;i.set(e,s),Qh(e)?e.forEach((function(r){s.add(Xh(r,t,n,r,e,i))})):Yh(e)&&e.forEach((function(r,o){s.set(o,Xh(r,t,n,o,e,i))}));var d=l?void 0:(u?c?Ih:Ah:c?ll:ol)(e);return eu(d||e,(function(r,o){d&&(r=e[o=r]),xu(s,o,Xh(r,t,n,o,e,i))})),s}Zh["[object Arguments]"]=Zh["[object Array]"]=Zh["[object ArrayBuffer]"]=Zh["[object DataView]"]=Zh["[object Boolean]"]=Zh["[object Date]"]=Zh["[object Float32Array]"]=Zh["[object Float64Array]"]=Zh["[object Int8Array]"]=Zh["[object Int16Array]"]=Zh["[object Int32Array]"]=Zh["[object Map]"]=Zh["[object Number]"]=Zh["[object Object]"]=Zh["[object RegExp]"]=Zh["[object Set]"]=Zh["[object String]"]=Zh["[object Symbol]"]=Zh["[object Uint8Array]"]=Zh["[object Uint8ClampedArray]"]=Zh["[object Uint16Array]"]=Zh["[object Uint32Array]"]=!0,Zh["[object Error]"]=Zh["[object Function]"]=Zh["[object WeakMap]"]=!1;function ep(e){return Xh(e,4)}function tp(e){return Xh(e,5)}function np(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Pl;++t<n;)this.add(e[t])}function rp(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function op(e,t){return e.has(t)}np.prototype.add=np.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},np.prototype.has=function(e){return this.__data__.has(e)};function ip(e,t,n,r,o,i){var s=1&n,a=e.length,c=t.length;if(a!=c&&!(s&&c>a))return!1;var u=i.get(e),l=i.get(t);if(u&&l)return u==t&&l==e;var f=-1,h=!0,p=2&n?new np:void 0;for(i.set(e,t),i.set(t,e);++f<a;){var d=e[f],g=t[f];if(r)var v=s?r(g,d,f,t,e,i):r(d,g,f,e,t,i);if(void 0!==v){if(v)continue;h=!1;break}if(p){if(!rp(t,(function(e,t){if(!op(p,t)&&(d===e||o(d,e,n,r,i)))return p.push(t)}))){h=!1;break}}else if(d!==g&&!o(d,g,n,r,i)){h=!1;break}}return i.delete(e),i.delete(t),h}function sp(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function ap(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}var cp=Ia?Ia.prototype:void 0,up=cp?cp.valueOf:void 0;var lp=Object.prototype.hasOwnProperty;var fp="[object Object]",hp=Object.prototype.hasOwnProperty;function pp(e,t,n,r,o,i){var s=Ua(e),a=Ua(t),c=s?"[object Array]":Nh(e),u=a?"[object Array]":Nh(t),l=(c="[object Arguments]"==c?fp:c)==fp,f=(u="[object Arguments]"==u?fp:u)==fp,h=c==u;if(h&&Wu(e)){if(!Wu(t))return!1;s=!0,l=!1}if(h&&!l)return i||(i=new ph),s||Qu(e)?ip(e,t,n,r,o,i):function(e,t,n,r,o,i,s){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!i(new Uh(e),new Uh(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return bu(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=sp;case"[object Set]":var c=1&r;if(a||(a=ap),e.size!=t.size&&!c)return!1;var u=s.get(e);if(u)return u==t;r|=2,s.set(e,t);var l=ip(a(e),a(t),r,o,i,s);return s.delete(e),l;case"[object Symbol]":if(up)return up.call(e)==up.call(t)}return!1}(e,t,c,n,r,o,i);if(!(1&n)){var p=l&&hp.call(e,"__wrapped__"),d=f&&hp.call(t,"__wrapped__");if(p||d){var g=p?e.value():e,v=d?t.value():t;return i||(i=new ph),o(g,v,n,r,i)}}return!!h&&(i||(i=new ph),function(e,t,n,r,o,i){var s=1&n,a=Ah(e),c=a.length;if(c!=Ah(t).length&&!s)return!1;for(var u=c;u--;){var l=a[u];if(!(s?l in t:lp.call(t,l)))return!1}var f=i.get(e),h=i.get(t);if(f&&h)return f==t&&h==e;var p=!0;i.set(e,t),i.set(t,e);for(var d=s;++u<c;){var g=e[l=a[u]],v=t[l];if(r)var m=s?r(v,g,l,t,e,i):r(g,v,l,e,t,i);if(!(void 0===m?g===v||o(g,v,n,r,i):m)){p=!1;break}d||(d="constructor"==l)}if(p&&!d){var y=e.constructor,_=t.constructor;y==_||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof _&&_ instanceof _||(p=!1)}return i.delete(e),i.delete(t),p}(e,t,n,r,o,i))}function dp(e,t,n,r,o){return e===t||(null==e||null==t||!Ra(e)&&!Ra(t)?e!=e&&t!=t:pp(e,t,n,r,dp,o))}function gp(e,t,n,r){var o=n.length,i=o,s=!r;if(null==e)return!i;for(e=Object(e);o--;){var a=n[o];if(s&&a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++o<i;){var c=(a=n[o])[0],u=e[c],l=a[1];if(s&&a[2]){if(void 0===u&&!(c in e))return!1}else{var f=new ph;if(r)var h=r(u,l,c,e,t,f);if(!(void 0===h?dp(l,u,3,r,f):h))return!1}}return!0}function vp(e){return e==e&&!Ga(e)}function mp(e){for(var t=ol(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,vp(o)]}return t}function yp(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}}function _p(e){var t=mp(e);return 1==t.length&&t[0][2]?yp(t[0][0],t[0][1]):function(n){return n===e||gp(n,e,t)}}function bp(e,t){return null!=e&&t in Object(e)}function wp(e,t,n){for(var r=-1,o=(t=Nl(t,e)).length,i=!1;++r<o;){var s=Bl(t[r]);if(!(i=null!=e&&n(e,s)))break;e=e[s]}return i||++r!=o?i:!!(o=null==e?0:e.length)&&Au(o)&&lu(s,o)&&(Ua(e)||Nu(e))}function xp(e,t){return null!=e&&wp(e,t,bp)}function Sp(e,t){return _l(e)&&vp(t)?yp(Bl(e),t):function(n){var r=Fl(n,e);return void 0===r&&r===t?xp(n,e):dp(t,r,3)}}function Op(e){return function(t){return null==t?void 0:t[e]}}function kp(e){return _l(e)?Op(Bl(e)):function(e){return function(t){return Ul(t,e)}}(e)}function Tp(e){return"function"==typeof e?e:null==e?oc:"object"==typeof e?Ua(e)?Sp(e[0],e[1]):_p(e):kp(e)}function Ap(e,t,n){var r=n.length;if(null==e)return!r;for(e=Object(e);r--;){var o=n[r],i=t[o],s=e[o];if(void 0===s&&!(o in e)||!i(s))return!1}return!0}function Ip(e,t,n,r){for(var o=-1,i=null==e?0:e.length;++o<i;){var s=e[o];t(r,s,n(s),e)}return r}function Pp(e){return function(t,n,r){for(var o=-1,i=Object(t),s=r(t),a=s.length;a--;){var c=s[e?a:++o];if(!1===n(i[c],c,i))break}return t}}var jp=Pp();function $p(e,t){return e&&jp(e,t,ol)}function Ep(e,t){return function(n,r){if(null==n)return n;if(!Iu(n))return e(n,r);for(var o=n.length,i=t?o:-1,s=Object(n);(t?i--:++i<o)&&!1!==r(s[i],i,s););return n}}var Lp=Ep($p);function Cp(e,t,n,r){return Lp(e,(function(e,o,i){t(r,e,n(e),i)})),r}function Mp(e,t){return function(n,r){var o=Ua(n)?Ip:Cp,i=t?t():{};return o(n,e,Tp(r),i)}}var Rp=Object.prototype.hasOwnProperty,Dp=Mp((function(e,t,n){Rp.call(e,n)?++e[n]:_u(e,n,1)}));const Np=Dp;function Bp(e,t,n){var r=mu(e,8,void 0,void 0,void 0,void 0,void 0,t=n?void 0:t);return r.placeholder=Bp.placeholder,r}Bp.placeholder={};function Up(e,t,n){var r=mu(e,16,void 0,void 0,void 0,void 0,void 0,t=n?void 0:t);return r.placeholder=Up.placeholder,r}Up.placeholder={};const Fp=function(){return Aa.Date.now()};var qp=Math.max,Wp=Math.min;function Hp(e,t,n){var r,o,i,s,a,c,u=0,l=!1,f=!1,h=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var n=r,i=o;return r=o=void 0,u=t,s=e.apply(i,n)}function d(e){return u=e,a=setTimeout(v,t),l?p(e):s}function g(e){var n=e-c;return void 0===c||n>=t||n<0||f&&e-u>=i}function v(){var e=Fp();if(g(e))return m(e);a=setTimeout(v,function(e){var n=t-(e-c);return f?Wp(n,i-(e-u)):n}(e))}function m(e){return a=void 0,h&&r?p(e):(r=o=void 0,s)}function y(){var e=Fp(),n=g(e);if(r=arguments,o=this,c=e,n){if(void 0===a)return d(c);if(f)return clearTimeout(a),a=setTimeout(v,t),p(c)}return void 0===a&&(a=setTimeout(v,t)),s}return t=tc(t)||0,Ga(n)&&(l=!!n.leading,i=(f="maxWait"in n)?qp(tc(n.maxWait)||0,t):i,h="trailing"in n?!!n.trailing:h),y.cancel=function(){void 0!==a&&clearTimeout(a),u=0,r=c=o=a=void 0},y.flush=function(){return void 0===a?s:m(Fp())},y}var zp=Object.prototype,Vp=zp.hasOwnProperty,Kp=Tu((function(e,t){e=Object(e);var n=-1,r=t.length,o=r>2?t[2]:void 0;for(o&&Pu(t[0],t[1],o)&&(r=1);++n<r;)for(var i=t[n],s=ll(i),a=-1,c=s.length;++a<c;){var u=s[a],l=e[u];(void 0===l||bu(l,zp[u])&&!Vp.call(e,u))&&(e[u]=i[u])}return e}));const Jp=Kp;function Yp(e,t,n){(void 0!==n&&!bu(e[t],n)||void 0===n&&!(t in e))&&_u(e,t,n)}function Gp(e){return Ra(e)&&Iu(e)}function Qp(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function Zp(e){return Su(e,ll(e))}function Xp(e,t,n,r,o){e!==t&&jp(t,(function(i,s){if(o||(o=new ph),Ga(i))!function(e,t,n,r,o,i,s){var a=Qp(e,n),c=Qp(t,n),u=s.get(c);if(u)Yp(e,n,u);else{var l=i?i(a,c,n+"",e,t,s):void 0,f=void 0===l;if(f){var h=Ua(c),p=!h&&Wu(c),d=!h&&!p&&Qu(c);l=c,h||p||d?Ua(a)?l=a:Gp(a)?l=Bc(a):p?(f=!1,l=_h(c,!0)):d?(f=!1,l=zh(c,!0)):l=[]:nf(c)||Nu(c)?(l=a,Nu(a)?l=Zp(a):Ga(a)&&!ic(a)||(l=Kh(c))):f=!1}f&&(s.set(c,l),o(l,c,r,i,s),s.delete(c)),Yp(e,n,l)}}(e,t,s,n,Xp,r,o);else{var a=r?r(Qp(e,s),i,s+"",e,t,o):void 0;void 0===a&&(a=i),Yp(e,s,a)}}),ll)}function ed(e,t,n,r,o,i){return Ga(e)&&Ga(t)&&(i.set(t,e),Xp(e,t,void 0,ed,i),i.delete(t)),e}var td=ju((function(e,t,n,r){Xp(e,t,n,r)}));const nd=td;const rd=Tu((function(e){return e.push(void 0,ed),kc(nd,void 0,e)}));function od(e,t,n){if("function"!=typeof e)throw new TypeError("Expected a function");return setTimeout((function(){e.apply(void 0,n)}),t)}var id=Tu((function(e,t){return od(e,1,t)}));const sd=id;var ad=Tu((function(e,t,n){return od(e,tc(t)||0,n)}));const cd=ad;function ud(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}function ld(e,t,n,r){var o=-1,i=ou,s=!0,a=e.length,c=[],u=t.length;if(!a)return c;n&&(t=Ba(t,zu(n))),r?(i=ud,s=!1):t.length>=200&&(i=op,s=!1,t=new np(t));e:for(;++o<a;){var l=e[o],f=null==n?l:n(l);if(l=r||0!==l?l:0,s&&f==f){for(var h=u;h--;)if(t[h]===f)continue e;c.push(l)}else i(t,f,r)||c.push(l)}return c}const fd=Tu((function(e,t){return Gp(e)?ld(e,Vl(t,1,Gp,!0)):[]}));function hd(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}const pd=Tu((function(e,t){var n=hd(t);return Gp(n)&&(n=void 0),Gp(e)?ld(e,Vl(t,1,Gp,!0),Tp(n)):[]}));const dd=Tu((function(e,t){var n=hd(t);return Gp(n)&&(n=void 0),Gp(e)?ld(e,Vl(t,1,Gp,!0),void 0,n):[]}));const gd=Ha((function(e,t){return e/t}),1);function vd(e,t,n,r){for(var o=e.length,i=r?o:-1;(r?i--:++i<o)&&t(e[i],i,e););return n?pf(e,r?0:i,r?i+1:o):pf(e,r?i+1:0,r?o:i)}function md(e){return"function"==typeof e?e:oc}function yd(e,t){return(Ua(e)?eu:Lp)(e,md(t))}function _d(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}var bd=Pp(!0);function wd(e,t){return e&&bd(e,t,ol)}const xd=Ep(wd,!0);function Sd(e,t){return(Ua(e)?_d:xd)(e,md(t))}function Od(e){return function(t){var n=Nh(t);return"[object Map]"==n?sp(t):"[object Set]"==n?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return Ba(t,(function(t){return[t,e[t]]}))}(t,e(t))}}const kd=Od(ol);const Td=Od(ll);const Ad=Ef({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});var Id=/[&<>"']/g,Pd=RegExp(Id.source);function jd(e){return(e=Dl(e))&&Pd.test(e)?e.replace(Id,Ad):e}var $d=/[\\^$.*+?()[\]{}|]/g,Ed=RegExp($d.source);function Ld(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function Cd(e,t){var n=!0;return Lp(e,(function(e,r,o){return n=!!t(e,r,o)})),n}function Md(e){return e?hh(rc(e),0,**********):0}function Rd(e,t){var n=[];return Lp(e,(function(e,r,o){t(e,r,o)&&n.push(e)})),n}function Dd(e){return function(t,n,r){var o=Object(t);if(!Iu(t)){var i=Tp(n);t=ol(t),n=function(e){return i(o[e],e,o)}}var s=e(t,n,r);return s>-1?o[i?t[s]:s]:void 0}}var Nd=Math.max;function Bd(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:rc(n);return o<0&&(o=Nd(r+o,0)),tu(e,Tp(t),o)}const Ud=Dd(Bd);function Fd(e,t,n){var r;return n(e,(function(e,n,o){if(t(e,n,o))return r=n,!1})),r}var qd=Math.max,Wd=Math.min;function Hd(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=r-1;return void 0!==n&&(o=rc(n),o=n<0?qd(r+o,0):Wd(o,r-1)),tu(e,Tp(t),o,!0)}const zd=Dd(Hd);function Vd(e){return e&&e.length?e[0]:void 0}function Kd(e,t){var n=-1,r=Iu(e)?Array(e.length):[];return Lp(e,(function(e,o,i){r[++n]=t(e,o,i)})),r}function Jd(e,t){return(Ua(e)?Ba:Kd)(e,Tp(t))}const Yd=ah("floor");function Gd(e){return Jl((function(t){var n=t.length,r=n,o=Nc.prototype.thru;for(e&&t.reverse();r--;){var i=t[r];if("function"!=typeof i)throw new TypeError("Expected a function");if(o&&!s&&"wrapper"==Dc(i))var s=new Nc([],!0)}for(r=s?r:n;++r<n;){var a=Dc(i=t[r]),c="wrapper"==a?Cc(i):void 0;s=c&&Wc(c[0])&&424==c[1]&&!c[4].length&&1==c[9]?s[Dc(c[0])].apply(s,c[3]):1==i.length&&Wc(i)?s[a]():s.thru(i)}return function(){var e=arguments,r=e[0];if(s&&1==e.length&&Ua(r))return s.plant(r).value();for(var o=0,i=n?t[o].apply(this,e):r;++o<n;)i=t[o].call(this,i);return i}}))}const Qd=Gd();const Zd=Gd(!0);function Xd(e,t){return bh(t,(function(t){return ic(e[t])}))}var eg=Object.prototype.hasOwnProperty,tg=Mp((function(e,t,n){eg.call(e,n)?e[n].push(t):_u(e,n,[t])}));const ng=tg;function rg(e,t){return e>t}function og(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=tc(t),n=tc(n)),e(t,n)}}const ig=og(rg);const sg=og((function(e,t){return e>=t}));var ag=Object.prototype.hasOwnProperty;function cg(e,t){return null!=e&&ag.call(e,t)}var ug=Math.max,lg=Math.min;function fg(e){return"string"==typeof e||!Ua(e)&&Ra(e)&&"[object String]"==Ma(e)}function hg(e,t){return Ba(t,(function(t){return e[t]}))}function pg(e){return null==e?[]:hg(e,ol(e))}var dg=Math.max;var gg=Math.max;var vg=Math.min;function mg(e,t,n){for(var r=n?ud:ou,o=e[0].length,i=e.length,s=i,a=Array(i),c=1/0,u=[];s--;){var l=e[s];s&&t&&(l=Ba(l,zu(t))),c=vg(l.length,c),a[s]=!n&&(t||o>=120&&l.length>=120)?new np(s&&l):void 0}l=e[0];var f=-1,h=a[0];e:for(;++f<o&&u.length<c;){var p=l[f],d=t?t(p):p;if(p=n||0!==p?p:0,!(h?op(h,d):r(u,d,n))){for(s=i;--s;){var g=a[s];if(!(g?op(g,d):r(e[s],d,n)))continue e}h&&h.push(d),u.push(p)}}return u}function yg(e){return Gp(e)?e:[]}const _g=Tu((function(e){var t=Ba(e,yg);return t.length&&t[0]===e[0]?mg(t):[]}));const bg=Tu((function(e){var t=hd(e),n=Ba(e,yg);return t===hd(n)?t=void 0:n.pop(),n.length&&n[0]===e[0]?mg(n,Tp(t)):[]}));const wg=Tu((function(e){var t=hd(e),n=Ba(e,yg);return(t="function"==typeof t?t:void 0)&&n.pop(),n.length&&n[0]===e[0]?mg(n,void 0,t):[]}));function xg(e,t){return function(n,r){return function(e,t,n,r){return $p(e,(function(e,o,i){t(r,n(e),o,i)})),r}(n,e,t(r),{})}}var Sg=Object.prototype.toString,Og=xg((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Sg.call(t)),e[t]=n}),Gc(oc));const kg=Og;var Tg=Object.prototype,Ag=Tg.hasOwnProperty,Ig=Tg.toString,Pg=xg((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ig.call(t)),Ag.call(e,t)?e[t].push(n):e[t]=[n]}),Tp);const jg=Pg;function $g(e,t){return t.length<2?e:Ul(e,pf(t,0,-1))}function Eg(e,t,n){var r=null==(e=$g(e,t=Nl(t,e)))?e:e[Bl(hd(t))];return null==r?void 0:kc(r,e,n)}const Lg=Tu(Eg);var Cg=Tu((function(e,t,n){var r=-1,o="function"==typeof t,i=Iu(e)?Array(e.length):[];return Lp(e,(function(e){i[++r]=o?kc(t,e,n):Eg(e,t,n)})),i}));const Mg=Cg;var Rg=Yu&&Yu.isArrayBuffer;const Dg=Rg?zu(Rg):function(e){return Ra(e)&&"[object ArrayBuffer]"==Ma(e)};var Ng=Yu&&Yu.isDate;const Bg=Ng?zu(Ng):function(e){return Ra(e)&&"[object Date]"==Ma(e)};var Ug=Object.prototype.hasOwnProperty;function Fg(e){if(null==e)return!0;if(Iu(e)&&(Ua(e)||"string"==typeof e||"function"==typeof e.splice||Wu(e)||Qu(e)||Nu(e)))return!e.length;var t=Nh(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(Eu(e))return!rl(e).length;for(var n in e)if(Ug.call(e,n))return!1;return!0}var qg=Aa.isFinite;function Wg(e){return"number"==typeof e&&e==rc(e)}function Hg(e){return"number"==typeof e||Ra(e)&&"[object Number]"==Ma(e)}var zg=ac?ic:Bu;function Vg(e){return null==e}var Kg=Yu&&Yu.isRegExp;const Jg=Kg?zu(Kg):function(e){return Ra(e)&&"[object RegExp]"==Ma(e)};var Yg=Array.prototype.join;var Gg=nh((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}));const Qg=Gg;var Zg=Mp((function(e,t,n){_u(e,n,t)}));const Xg=Zg;var ev=Math.max,tv=Math.min;var nv=nh((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}));const rv=nv;const ov=If("toLowerCase");function iv(e,t){return e<t}const sv=og(iv);const av=og((function(e,t){return e<=t}));function cv(e,t,n){for(var r=-1,o=e.length;++r<o;){var i=e[r],s=t(i);if(null!=s&&(void 0===a?s==s&&!Da(s):n(s,a)))var a=s,c=i}return c}function uv(e,t){for(var n,r=-1,o=e.length;++r<o;){var i=t(e[r]);void 0!==i&&(n=void 0===n?i:n+i)}return n}function lv(e,t){var n=null==e?0:e.length;return n?uv(e,t)/n:NaN}var fv=ju((function(e,t,n){Xp(e,t,n)}));const hv=fv;const pv=Tu((function(e,t){return function(n){return Eg(n,e,t)}}));const dv=Tu((function(e,t){return function(n){return Eg(e,n,t)}}));function gv(e){return e&&e.length?cv(e,oc,iv):void 0}function vv(e,t,n){var r=ol(t),o=Xd(t,r),i=!(Ga(n)&&"chain"in n&&!n.chain),s=ic(e);return eu(o,(function(n){var r=t[n];e[n]=r,s&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__),o=n.__actions__=Bc(this.__actions__);return o.push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,Wl([this.value()],arguments))})})),e}const mv=Ha((function(e,t){return e*t}),1);function yv(e){if("function"!=typeof e)throw new TypeError("Expected a function");return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}var _v=Ia?Ia.iterator:void 0;function bv(e){if(!e)return[];if(Iu(e))return fg(e)?Af(e):Bc(e);if(_v&&e[_v])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[_v]());var t=Nh(e);return("[object Map]"==t?sp:"[object Set]"==t?ap:pg)(e)}function wv(e,t){var n=e.length;if(n)return lu(t+=t<0?n:0,n)?e[t]:void 0}function xv(e,t){return null==(e=$g(e,t=Nl(t,e)))||delete e[Bl(hd(t))]}function Sv(e){return nf(e)?void 0:e}var Ov=Jl((function(e,t){var n={};if(null==e)return n;var r=!1;t=Ba(t,(function(t){return t=Nl(t,e),r||(r=t.length>1),t})),Su(e,Ih(e),n),r&&(n=Xh(n,7,Sv));for(var o=t.length;o--;)xv(n,t[o]);return n}));const kv=Ov;function Tv(e,t,n,r){if(!Ga(e))return e;for(var o=-1,i=(t=Nl(t,e)).length,s=i-1,a=e;null!=a&&++o<i;){var c=Bl(t[o]),u=n;if("__proto__"===c||"constructor"===c||"prototype"===c)return e;if(o!=s){var l=a[c];void 0===(u=r?r(l,c,a):void 0)&&(u=Ga(l)?l:lu(t[o+1])?[]:{})}xu(a,c,u),a=a[c]}return e}function Av(e,t,n){for(var r=-1,o=t.length,i={};++r<o;){var s=t[r],a=Ul(e,s);n(a,s)&&Tv(i,Nl(s,e),a)}return i}function Iv(e,t){if(null==e)return{};var n=Ba(Ih(e),(function(e){return[e]}));return t=Tp(t),Av(e,n,(function(e,n){return t(e,n[0])}))}function Pv(e,t){if(e!==t){var n=void 0!==e,r=null===e,o=e==e,i=Da(e),s=void 0!==t,a=null===t,c=t==t,u=Da(t);if(!a&&!u&&!i&&e>t||i&&s&&c&&!a&&!u||r&&s&&c||!n&&c||!o)return 1;if(!r&&!i&&!u&&e<t||u&&n&&o&&!r&&!i||a&&n&&o||!s&&o||!c)return-1}return 0}function jv(e,t,n){t=t.length?Ba(t,(function(e){return Ua(e)?function(t){return Ul(t,1===e.length?e[0]:e)}:e})):[oc];var r=-1;return t=Ba(t,zu(Tp)),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Kd(e,(function(e,n,o){return{criteria:Ba(t,(function(t){return t(e)})),index:++r,value:e}})),(function(e,t){return function(e,t,n){for(var r=-1,o=e.criteria,i=t.criteria,s=o.length,a=n.length;++r<s;){var c=Pv(o[r],i[r]);if(c)return r>=a?c:c*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}))}function $v(e){return Jl((function(t){return t=Ba(t,zu(Tp)),Tu((function(n){var r=this;return e(t,(function(e){return kc(e,r,n)}))}))}))}const Ev=$v(Ba);var Lv=Tu,Cv=Math.min,Mv=Lv((function(e,t){var n=(t=1==t.length&&Ua(t[0])?Ba(t[0],zu(Tp)):Ba(Vl(t,1),zu(Tp))).length;return Tu((function(r){for(var o=-1,i=Cv(r.length,n);++o<i;)r[o]=t[o].call(this,r[o]);return kc(e,this,r)}))}));const Rv=Mv;const Dv=$v(Ld);const Nv=$v(rp);var Bv=Math.floor;function Uv(e,t){var n="";if(!e||t<1||t>9007199254740991)return n;do{t%2&&(n+=e),(t=Bv(t/2))&&(e+=e)}while(t);return n}var Fv=Op("length"),qv="[\\ud800-\\udfff]",Wv="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Hv="\\ud83c[\\udffb-\\udfff]",zv="[^\\ud800-\\udfff]",Vv="(?:\\ud83c[\\udde6-\\uddff]){2}",Kv="[\\ud800-\\udbff][\\udc00-\\udfff]",Jv="(?:"+Wv+"|"+Hv+")"+"?",Yv="[\\ufe0e\\ufe0f]?"+Jv+("(?:\\u200d(?:"+[zv,Vv,Kv].join("|")+")[\\ufe0e\\ufe0f]?"+Jv+")*"),Gv="(?:"+[zv+Wv+"?",Wv,Vv,Kv,qv].join("|")+")",Qv=RegExp(Hv+"(?="+Hv+")|"+Gv+Yv,"g");function Zv(e){return vf(e)?function(e){for(var t=Qv.lastIndex=0;Qv.test(e);)++t;return t}(e):Fv(e)}var Xv=Math.ceil;function em(e,t){var n=(t=void 0===t?" ":Wa(t)).length;if(n<2)return n?Uv(t,e):t;var r=Uv(t,Xv(e/Zv(t)));return vf(t)?df(Af(r),0,e).join(""):r.slice(0,e)}var tm=Math.ceil,nm=Math.floor;var rm=/^\s+/,om=Aa.parseInt;var im=Tu((function(e,t){return mu(e,32,void 0,t,pu(t,cu(im)))}));im.placeholder={};const sm=im;var am=Tu((function(e,t){return mu(e,64,void 0,t,pu(t,cu(am)))}));am.placeholder={};const cm=am;var um=Mp((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]}));const lm=um;const fm=Jl((function(e,t){return null==e?{}:function(e,t){return Av(e,t,(function(t,n){return xp(e,n)}))}(e,t)}));function hm(e,t,n,r){for(var o=n-1,i=e.length;++o<i;)if(r(e[o],t))return o;return-1}var pm=Array.prototype.splice;function dm(e,t,n,r){var o=r?hm:ru,i=-1,s=t.length,a=e;for(e===t&&(t=Bc(t)),n&&(a=Ba(e,zu(n)));++i<s;)for(var c=0,u=t[i],l=n?n(u):u;(c=o(a,l,c,r))>-1;)a!==e&&pm.call(a,c,1),pm.call(e,c,1);return e}function gm(e,t){return e&&e.length&&t&&t.length?dm(e,t):e}const vm=Tu(gm);var mm=Array.prototype.splice;function ym(e,t){for(var n=e?t.length:0,r=n-1;n--;){var o=t[n];if(n==r||o!==i){var i=o;lu(o)?mm.call(e,o,1):xv(e,o)}}return e}var _m=Jl((function(e,t){var n=null==e?0:e.length,r=ql(e,t);return ym(e,Ba(t,(function(e){return lu(e,n)?+e:e})).sort(Pv)),r}));const bm=_m;var wm=Math.floor,xm=Math.random;function Sm(e,t){return e+wm(xm()*(t-e+1))}var Om=parseFloat,km=Math.min,Tm=Math.random;var Am=Math.ceil,Im=Math.max;function Pm(e){return function(t,n,r){return r&&"number"!=typeof r&&Pu(t,n,r)&&(n=r=void 0),t=nc(t),void 0===n?(n=t,t=0):n=nc(n),function(e,t,n,r){for(var o=-1,i=Im(Am((t-e)/(n||1)),0),s=Array(i);i--;)s[r?i:++o]=e,e+=n;return s}(t,n,r=void 0===r?t<n?1:-1:nc(r),e)}}const jm=Pm();const $m=Pm(!0);var Em=Jl((function(e,t){return mu(e,256,void 0,void 0,void 0,t)}));const Lm=Em;function Cm(e,t,n,r,o){return o(e,(function(e,o,i){n=r?(r=!1,e):t(n,e,o,i)})),n}function Mm(e,t,n,r){var o=null==e?0:e.length;for(r&&o&&(n=e[--o]);o--;)n=t(n,e[o],o,e);return n}var Rm=Array.prototype.reverse;function Dm(e){return null==e?e:Rm.call(e)}const Nm=ah("round");function Bm(e){var t=e.length;return t?e[Sm(0,t-1)]:void 0}function Um(e){return Bm(pg(e))}function Fm(e,t){var n=-1,r=e.length,o=r-1;for(t=void 0===t?r:t;++n<t;){var i=Sm(n,o),s=e[i];e[i]=e[n],e[n]=s}return e.length=t,e}function qm(e,t){return Fm(Bc(e),hh(t,0,e.length))}function Wm(e,t){var n=pg(e);return Fm(n,hh(t,0,n.length))}function Hm(e){return Fm(Bc(e))}function zm(e){return Fm(pg(e))}var Vm=nh((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}));const Km=Vm;function Jm(e,t){var n;return Lp(e,(function(e,r,o){return!(n=t(e,r,o))})),!!n}const Ym=Tu((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Pu(e,t[0],t[1])?t=[]:n>2&&Pu(t[0],t[1],t[2])&&(t=[t[0]]),jv(e,Vl(t,1),[])}));var Gm=Math.floor,Qm=Math.min;function Zm(e,t,n,r){var o=0,i=null==e?0:e.length;if(0===i)return 0;for(var s=(t=n(t))!=t,a=null===t,c=Da(t),u=void 0===t;o<i;){var l=Gm((o+i)/2),f=n(e[l]),h=void 0!==f,p=null===f,d=f==f,g=Da(f);if(s)var v=r||d;else v=u?d&&(r||h):a?d&&h&&(r||!p):c?d&&h&&!p&&(r||!g):!p&&!g&&(r?f<=t:f<t);v?o=l+1:i=l}return Qm(i,4294967294)}function Xm(e,t,n){var r=0,o=null==e?r:e.length;if("number"==typeof t&&t==t&&o<=2147483647){for(;r<o;){var i=r+o>>>1,s=e[i];null!==s&&!Da(s)&&(n?s<=t:s<t)?r=i+1:o=i}return o}return Zm(e,t,oc,n)}function ey(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var s=e[n],a=t?t(s):s;if(!n||!bu(a,c)){var c=a;i[o++]=0===s?0:s}}return i}var ty=Math.max;var ny=nh((function(e,t,n){return e+(n?" ":"")+Pf(t)}));const ry=ny;function oy(e,t,n){return e=Dl(e),n=null==n?0:hh(rc(n),0,e.length),t=Wa(t),e.slice(n,n+t.length)==t}const iy=Ha((function(e,t){return e-t}),0);var sy=Object.prototype,ay=sy.hasOwnProperty;function cy(e,t,n,r){return void 0===e||bu(e,sy[n])&&!ay.call(r,n)?t:e}var uy={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"};function ly(e){return"\\"+uy[e]}const fy=/<%=([\s\S]+?)%>/g;const hy={escape:/<%-([\s\S]+?)%>/g,evaluate:/<%([\s\S]+?)%>/g,interpolate:fy,variable:"",imports:{_:{escape:jd}}};var py=/\b__p \+= '';/g,dy=/\b(__p \+=) '' \+/g,gy=/(__e\(.*?\)|\b__t\)) \+\n'';/g,vy=/[()=,{}\[\]\/\s]/,yy=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,_y=/($^)/,by=/['\n\r\u2028\u2029\\]/g,wy=Object.prototype.hasOwnProperty;function xy(e,t){return t(e)}var Sy=Math.min;function Oy(e,t){var n=e;return n instanceof Ec&&(n=n.value()),$f(t,(function(e,t){return t.func.apply(t.thisArg,Wl([e],t.args))}),n)}function ky(){return Oy(this.__wrapped__,this.__actions__)}function Ty(e,t){for(var n=e.length;n--&&ru(t,e[n],0)>-1;);return n}function Ay(e,t){for(var n=-1,r=e.length;++n<r&&ru(t,e[n],0)>-1;);return n}var Iy=/^\s+/;var Py=/\w*$/;const jy=Ef({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var $y=/&(?:amp|lt|gt|quot|#39);/g,Ey=RegExp($y.source);var Ly=$h&&1/ap(new $h([,-0]))[1]==1/0?function(e){return new $h(e)}:Lc;function Cy(e,t,n){var r=-1,o=ou,i=e.length,s=!0,a=[],c=a;if(n)s=!1,o=ud;else if(i>=200){var u=t?null:Ly(e);if(u)return ap(u);s=!1,o=op,c=new np}else c=t?[]:a;e:for(;++r<i;){var l=e[r],f=t?t(l):l;if(l=n||0!==l?l:0,s&&f==f){for(var h=c.length;h--;)if(c[h]===f)continue e;t&&c.push(f),a.push(l)}else o(c,f,n)||(c!==a&&c.push(f),a.push(l))}return a}const My=Tu((function(e){return Cy(Vl(e,1,Gp,!0))}));const Ry=Tu((function(e){var t=hd(e);return Gp(t)&&(t=void 0),Cy(Vl(e,1,Gp,!0),Tp(t))}));const Dy=Tu((function(e){var t=hd(e);return t="function"==typeof t?t:void 0,Cy(Vl(e,1,Gp,!0),void 0,t)}));var Ny=0;var By=Math.max;function Uy(e){if(!e||!e.length)return[];var t=0;return e=bh(e,(function(e){if(Gp(e))return t=By(e.length,t),!0})),Lu(t,(function(t){return Ba(e,Op(t))}))}function Fy(e,t){if(!e||!e.length)return[];var n=Uy(e);return null==t?n:Ba(n,(function(e){return kc(t,void 0,e)}))}function qy(e,t,n,r){return Tv(e,t,n(Ul(e,t)),r)}var Wy=nh((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}));const Hy=Wy;const zy=Tu((function(e,t){return Gp(e)?ld(e,t):[]}));const Vy=Jl((function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,o=function(t){return ql(t,e)};return!(t>1||this.__actions__.length)&&r instanceof Ec&&lu(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:xy,args:[o],thisArg:void 0}),new Nc(r,this.__chain__).thru((function(e){return t&&!e.length&&e.push(void 0),e}))):this.thru(o)}));function Ky(e,t,n){var r=e.length;if(r<2)return r?Cy(e[0]):[];for(var o=-1,i=Array(r);++o<r;)for(var s=e[o],a=-1;++a<r;)a!=o&&(i[o]=ld(i[o]||s,e[a],t,n));return Cy(Vl(i,1),t,n)}const Jy=Tu((function(e){return Ky(bh(e,Gp))}));const Yy=Tu((function(e){var t=hd(e);return Gp(t)&&(t=void 0),Ky(bh(e,Gp),Tp(t))}));const Gy=Tu((function(e){var t=hd(e);return t="function"==typeof t?t:void 0,Ky(bh(e,Gp),void 0,t)}));const Qy=Tu(Uy);function Zy(e,t,n){for(var r=-1,o=e.length,i=t.length,s={};++r<o;){var a=r<i?t[r]:void 0;n(s,e[r],a)}return s}var Xy=Tu((function(e){var t=e.length,n=t>1?e[t-1]:void 0;return n="function"==typeof n?(e.pop(),n):void 0,Fy(e,n)}));const e_={chunk:function(e,t,n){t=(n?Pu(e,t,n):void 0===t)?1:fh(rc(t),0);var r=null==e?0:e.length;if(!r||t<1)return[];for(var o=0,i=0,s=Array(lh(r/t));o<r;)s[i++]=pf(e,o,o+=t);return s},compact:function(e){for(var t=-1,n=null==e?0:e.length,r=0,o=[];++t<n;){var i=e[t];i&&(o[r++]=i)}return o},concat:function(){var e=arguments.length;if(!e)return[];for(var t=Array(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return Wl(Ua(n)?Bc(n):[n],Vl(t,1))},difference:fd,differenceBy:pd,differenceWith:dd,drop:function(e,t,n){var r=null==e?0:e.length;return r?pf(e,(t=n||void 0===t?1:rc(t))<0?0:t,r):[]},dropRight:function(e,t,n){var r=null==e?0:e.length;return r?pf(e,0,(t=r-(t=n||void 0===t?1:rc(t)))<0?0:t):[]},dropRightWhile:function(e,t){return e&&e.length?vd(e,Tp(t),!0,!0):[]},dropWhile:function(e,t){return e&&e.length?vd(e,Tp(t),!0):[]},fill:function(e,t,n,r){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Pu(e,t,n)&&(n=0,r=o),function(e,t,n,r){var o=e.length;for((n=rc(n))<0&&(n=-n>o?0:o+n),(r=void 0===r||r>o?o:rc(r))<0&&(r+=o),r=n>r?0:Md(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},findIndex:Bd,findLastIndex:Hd,first:Vd,flatten:Kl,flattenDeep:function(e){return(null==e?0:e.length)?Vl(e,Infinity):[]},flattenDepth:function(e,t){return(null==e?0:e.length)?Vl(e,t=void 0===t?1:rc(t)):[]},fromPairs:function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var o=e[t];r[o[0]]=o[1]}return r},head:Vd,indexOf:function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:rc(n);return o<0&&(o=gg(r+o,0)),ru(e,t,o)},initial:function(e){return(null==e?0:e.length)?pf(e,0,-1):[]},intersection:_g,intersectionBy:bg,intersectionWith:wg,join:function(e,t){return null==e?"":Yg.call(e,t)},last:hd,lastIndexOf:function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=r;return void 0!==n&&(o=(o=rc(n))<0?ev(r+o,0):tv(o,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,o):tu(e,nu,o,!0)},nth:function(e,t){return e&&e.length?wv(e,rc(t)):void 0},pull:vm,pullAll:gm,pullAllBy:function(e,t,n){return e&&e.length&&t&&t.length?dm(e,t,Tp(n)):e},pullAllWith:function(e,t,n){return e&&e.length&&t&&t.length?dm(e,t,void 0,n):e},pullAt:bm,remove:function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,o=[],i=e.length;for(t=Tp(t);++r<i;){var s=e[r];t(s,r,e)&&(n.push(s),o.push(r))}return ym(e,o),n},reverse:Dm,slice:function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&Pu(e,t,n)?(t=0,n=r):(t=null==t?0:rc(t),n=void 0===n?r:rc(n)),pf(e,t,n)):[]},sortedIndex:function(e,t){return Xm(e,t)},sortedIndexBy:function(e,t,n){return Zm(e,t,Tp(n))},sortedIndexOf:function(e,t){var n=null==e?0:e.length;if(n){var r=Xm(e,t);if(r<n&&bu(e[r],t))return r}return-1},sortedLastIndex:function(e,t){return Xm(e,t,!0)},sortedLastIndexBy:function(e,t,n){return Zm(e,t,Tp(n),!0)},sortedLastIndexOf:function(e,t){if(null==e?0:e.length){var n=Xm(e,t,!0)-1;if(bu(e[n],t))return n}return-1},sortedUniq:function(e){return e&&e.length?ey(e):[]},sortedUniqBy:function(e,t){return e&&e.length?ey(e,Tp(t)):[]},tail:function(e){var t=null==e?0:e.length;return t?pf(e,1,t):[]},take:function(e,t,n){return e&&e.length?pf(e,0,(t=n||void 0===t?1:rc(t))<0?0:t):[]},takeRight:function(e,t,n){var r=null==e?0:e.length;return r?pf(e,(t=r-(t=n||void 0===t?1:rc(t)))<0?0:t,r):[]},takeRightWhile:function(e,t){return e&&e.length?vd(e,Tp(t),!1,!0):[]},takeWhile:function(e,t){return e&&e.length?vd(e,Tp(t)):[]},union:My,unionBy:Ry,unionWith:Dy,uniq:function(e){return e&&e.length?Cy(e):[]},uniqBy:function(e,t){return e&&e.length?Cy(e,Tp(t)):[]},uniqWith:function(e,t){return t="function"==typeof t?t:void 0,e&&e.length?Cy(e,void 0,t):[]},unzip:Uy,unzipWith:Fy,without:zy,xor:Jy,xorBy:Yy,xorWith:Gy,zip:Qy,zipObject:function(e,t){return Zy(e||[],t||[],xu)},zipObjectDeep:function(e,t){return Zy(e||[],t||[],Tv)},zipWith:Xy},t_={countBy:Np,each:yd,eachRight:Sd,every:function(e,t,n){var r=Ua(e)?Ld:Cd;return n&&Pu(e,t,n)&&(t=void 0),r(e,Tp(t))},filter:function(e,t){return(Ua(e)?bh:Rd)(e,Tp(t))},find:Ud,findLast:zd,flatMap:function(e,t){return Vl(Jd(e,t),1)},flatMapDeep:function(e,t){return Vl(Jd(e,t),Infinity)},flatMapDepth:function(e,t,n){return n=void 0===n?1:rc(n),Vl(Jd(e,t),n)},forEach:yd,forEachRight:Sd,groupBy:ng,includes:function(e,t,n,r){e=Iu(e)?e:pg(e),n=n&&!r?rc(n):0;var o=e.length;return n<0&&(n=dg(o+n,0)),fg(e)?n<=o&&e.indexOf(t,n)>-1:!!o&&ru(e,t,n)>-1},invokeMap:Mg,keyBy:Xg,map:Jd,orderBy:function(e,t,n,r){return null==e?[]:(Ua(t)||(t=null==t?[]:[t]),Ua(n=r?void 0:n)||(n=null==n?[]:[n]),jv(e,t,n))},partition:lm,reduce:function(e,t,n){var r=Ua(e)?$f:Cm,o=arguments.length<3;return r(e,Tp(t),n,o,Lp)},reduceRight:function(e,t,n){var r=Ua(e)?Mm:Cm,o=arguments.length<3;return r(e,Tp(t),n,o,xd)},reject:function(e,t){return(Ua(e)?bh:Rd)(e,yv(Tp(t)))},sample:function(e){return(Ua(e)?Bm:Um)(e)},sampleSize:function(e,t,n){return t=(n?Pu(e,t,n):void 0===t)?1:rc(t),(Ua(e)?qm:Wm)(e,t)},shuffle:function(e){return(Ua(e)?Hm:zm)(e)},size:function(e){if(null==e)return 0;if(Iu(e))return fg(e)?Zv(e):e.length;var t=Nh(e);return"[object Map]"==t||"[object Set]"==t?e.size:rl(e).length},some:function(e,t,n){var r=Ua(e)?rp:Jm;return n&&Pu(e,t,n)&&(t=void 0),r(e,Tp(t))},sortBy:Ym},n_={now:Fp},r_={after:function(e,t){if("function"!=typeof t)throw new TypeError("Expected a function");return e=rc(e),function(){if(--e<1)return t.apply(this,arguments)}},ary:yu,before:af,bind:uf,bindKey:hf,curry:Bp,curryRight:Up,debounce:Hp,defer:sd,delay:cd,flip:function(e){return mu(e,512)},memoize:jl,negate:yv,once:function(e){return af(2,e)},overArgs:Rv,partial:sm,partialRight:cm,rearg:Lm,rest:function(e,t){if("function"!=typeof e)throw new TypeError("Expected a function");return Tu(e,t=void 0===t?t:rc(t))},spread:function(e,t){if("function"!=typeof e)throw new TypeError("Expected a function");return t=null==t?0:ty(rc(t),0),Tu((function(n){var r=n[t],o=df(n,0,t);return r&&Wl(o,r),kc(e,this,o)}))},throttle:function(e,t,n){var r=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return Ga(n)&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),Hp(e,t,{leading:r,maxWait:t,trailing:o})},unary:function(e){return yu(e,1)},wrap:function(e,t){return sm(md(t),e)}},o_={castArray:function(){if(!arguments.length)return[];var e=arguments[0];return Ua(e)?e:[e]},clone:ep,cloneDeep:tp,cloneDeepWith:function(e,t){return Xh(e,5,t="function"==typeof t?t:void 0)},cloneWith:function(e,t){return Xh(e,4,t="function"==typeof t?t:void 0)},conformsTo:function(e,t){return null==t||Ap(e,t,ol(t))},eq:bu,gt:ig,gte:sg,isArguments:Nu,isArray:Ua,isArrayBuffer:Dg,isArrayLike:Iu,isArrayLikeObject:Gp,isBoolean:function(e){return!0===e||!1===e||Ra(e)&&"[object Boolean]"==Ma(e)},isBuffer:Wu,isDate:Bg,isElement:function(e){return Ra(e)&&1===e.nodeType&&!nf(e)},isEmpty:Fg,isEqual:function(e,t){return dp(e,t)},isEqualWith:function(e,t,n){var r=(n="function"==typeof n?n:void 0)?n(e,t):void 0;return void 0===r?dp(e,t,void 0,n):!!r},isError:rf,isFinite:function(e){return"number"==typeof e&&qg(e)},isFunction:ic,isInteger:Wg,isLength:Au,isMap:Yh,isMatch:function(e,t){return e===t||gp(e,t,mp(t))},isMatchWith:function(e,t,n){return n="function"==typeof n?n:void 0,gp(e,t,mp(t),n)},isNaN:function(e){return Hg(e)&&e!=+e},isNative:function(e){if(zg(e))throw new Error("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return mc(e)},isNil:Vg,isNull:function(e){return null===e},isNumber:Hg,isObject:Ga,isObjectLike:Ra,isPlainObject:nf,isRegExp:Jg,isSafeInteger:function(e){return Wg(e)&&e>=-9007199254740991&&e<=9007199254740991},isSet:Qh,isString:fg,isSymbol:Da,isTypedArray:Qu,isUndefined:function(e){return void 0===e},isWeakMap:function(e){return Ra(e)&&"[object WeakMap]"==Nh(e)},isWeakSet:function(e){return Ra(e)&&"[object WeakSet]"==Ma(e)},lt:sv,lte:av,toArray:bv,toFinite:nc,toInteger:rc,toLength:Md,toNumber:tc,toPlainObject:Zp,toSafeInteger:function(e){return e?hh(rc(e),-9007199254740991,9007199254740991):0===e?e:0},toString:Dl},i_={add:za,ceil:ch,divide:gd,floor:Yd,max:function(e){return e&&e.length?cv(e,oc,rg):void 0},maxBy:function(e,t){return e&&e.length?cv(e,Tp(t),rg):void 0},mean:function(e){return lv(e,oc)},meanBy:function(e,t){return lv(e,Tp(t))},min:gv,minBy:function(e,t){return e&&e.length?cv(e,Tp(t),iv):void 0},multiply:mv,round:Nm,subtract:iy,sum:function(e){return e&&e.length?uv(e,oc):0},sumBy:function(e,t){return e&&e.length?uv(e,Tp(t)):0}},s_=function(e,t,n){return void 0===n&&(n=t,t=void 0),void 0!==n&&(n=(n=tc(n))==n?n:0),void 0!==t&&(t=(t=tc(t))==t?t:0),hh(tc(e),t,n)},a_=function(e,t,n){return t=nc(t),void 0===n?(n=t,t=0):n=nc(n),function(e,t,n){return e>=lg(t,n)&&e<ug(t,n)}(e=tc(e),t,n)},c_=function(e,t,n){if(n&&"boolean"!=typeof n&&Pu(e,t,n)&&(t=n=void 0),void 0===n&&("boolean"==typeof t?(n=t,t=void 0):"boolean"==typeof e&&(n=e,e=void 0)),void 0===e&&void 0===t?(e=0,t=1):(e=nc(e),void 0===t?(t=e,e=0):t=nc(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var o=Tm();return km(e+o*(t-e+Om("1e-"+((o+"").length-1))),t)}return Sm(e,t)},u_={assign:al,assignIn:hl,assignInWith:dl,assignWith:vl,at:Yl,create:function(e,t){var n=Sc(e);return null==t?n:dh(n,t)},defaults:Jp,defaultsDeep:rd,entries:kd,entriesIn:Td,extend:hl,extendWith:dl,findKey:function(e,t){return Fd(e,Tp(t),$p)},findLastKey:function(e,t){return Fd(e,Tp(t),wd)},forIn:function(e,t){return null==e?e:jp(e,md(t),ll)},forInRight:function(e,t){return null==e?e:bd(e,md(t),ll)},forOwn:function(e,t){return e&&$p(e,md(t))},forOwnRight:function(e,t){return e&&wd(e,md(t))},functions:function(e){return null==e?[]:Xd(e,ol(e))},functionsIn:function(e){return null==e?[]:Xd(e,ll(e))},get:Fl,has:function(e,t){return null!=e&&wp(e,t,cg)},hasIn:xp,invert:kg,invertBy:jg,invoke:Lg,keys:ol,keysIn:ll,mapKeys:function(e,t){var n={};return t=Tp(t),$p(e,(function(e,r,o){_u(n,t(e,r,o),e)})),n},mapValues:function(e,t){var n={};return t=Tp(t),$p(e,(function(e,r,o){_u(n,r,t(e,r,o))})),n},merge:hv,mergeWith:nd,omit:kv,omitBy:function(e,t){return Iv(e,yv(Tp(t)))},pick:fm,pickBy:Iv,result:function(e,t,n){var r=-1,o=(t=Nl(t,e)).length;for(o||(o=1,e=void 0);++r<o;){var i=null==e?void 0:e[Bl(t[r])];void 0===i&&(r=o,i=n),e=ic(i)?i.call(e):i}return e},set:function(e,t,n){return null==e?e:Tv(e,t,n)},setWith:function(e,t,n,r){return r="function"==typeof r?r:void 0,null==e?e:Tv(e,t,n,r)},toPairs:kd,toPairsIn:Td,transform:function(e,t,n){var r=Ua(e),o=r||Wu(e)||Qu(e);if(t=Tp(t),null==n){var i=e&&e.constructor;n=o?r?new i:[]:Ga(e)&&ic(i)?Sc(Gl(e)):{}}return(o?eu:$p)(e,(function(e,r,o){return t(n,e,r,o)})),n},unset:function(e,t){return null==e||xv(e,t)},update:function(e,t,n){return null==e?e:qy(e,t,md(n))},updateWith:function(e,t,n,r){return r="function"==typeof r?r:void 0,null==e?e:qy(e,t,md(n),r)},values:pg,valuesIn:function(e){return null==e?[]:hg(e,ll(e))}},l_={at:Vy,chain:uh,commit:function(){return new Nc(this.value(),this.__chain__)},lodash:qc,next:function(){void 0===this.__values__&&(this.__values__=bv(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?void 0:this.__values__[this.__index__++]}},plant:function(e){for(var t,n=this;n instanceof $c;){var r=Uc(n);r.__index__=0,r.__values__=void 0,t?o.__wrapped__=r:t=r;var o=r;n=n.__wrapped__}return o.__wrapped__=e,t},reverse:function(){var e=this.__wrapped__;if(e instanceof Ec){var t=e;return this.__actions__.length&&(t=new Ec(this)),(t=t.reverse()).__actions__.push({func:xy,args:[Dm],thisArg:void 0}),new Nc(t,this.__chain__)}return this.thru(Dm)},tap:function(e,t){return t(e),e},thru:xy,toIterator:function(){return this},toJSON:ky,value:ky,valueOf:ky,wrapperChain:function(){return uh(this)}},f_={camelCase:oh,capitalize:jf,deburr:Rf,endsWith:function(e,t,n){e=Dl(e),t=Wa(t);var r=e.length,o=n=void 0===n?r:hh(rc(n),0,r);return(n-=t.length)>=0&&e.slice(n,o)==t},escape:jd,escapeRegExp:function(e){return(e=Dl(e))&&Ed.test(e)?e.replace($d,"\\$&"):e},kebabCase:Qg,lowerCase:rv,lowerFirst:ov,pad:function(e,t,n){e=Dl(e);var r=(t=rc(t))?Zv(e):0;if(!t||r>=t)return e;var o=(t-r)/2;return em(nm(o),n)+e+em(tm(o),n)},padEnd:function(e,t,n){e=Dl(e);var r=(t=rc(t))?Zv(e):0;return t&&r<t?e+em(t-r,n):e},padStart:function(e,t,n){e=Dl(e);var r=(t=rc(t))?Zv(e):0;return t&&r<t?em(t-r,n)+e:e},parseInt:function(e,t,n){return n||null==t?t=0:t&&(t=+t),om(Dl(e).replace(rm,""),t||0)},repeat:function(e,t,n){return t=(n?Pu(e,t,n):void 0===t)?1:rc(t),Uv(Dl(e),t)},replace:function(){var e=arguments,t=Dl(e[0]);return e.length<3?t:t.replace(e[1],e[2])},snakeCase:Km,split:function(e,t,n){return n&&"number"!=typeof n&&Pu(e,t,n)&&(t=n=void 0),(n=void 0===n?**********:n>>>0)?(e=Dl(e))&&("string"==typeof t||null!=t&&!Jg(t))&&!(t=Wa(t))&&vf(e)?df(Af(e),0,n):e.split(t,n):[]},startCase:ry,startsWith:oy,template:function(e,t,n){var r=hy.imports._.templateSettings||hy;n&&Pu(e,t,n)&&(t=void 0),e=Dl(e),t=dl({},t,r,cy);var o,i,s=dl({},t.imports,r.imports,cy),a=ol(s),c=hg(s,a),u=0,l=t.interpolate||_y,f="__p += '",h=RegExp((t.escape||_y).source+"|"+l.source+"|"+(l===fy?yy:_y).source+"|"+(t.evaluate||_y).source+"|$","g"),p=wy.call(t,"sourceURL")?"//# sourceURL="+(t.sourceURL+"").replace(/\s/g," ")+"\n":"";e.replace(h,(function(t,n,r,s,a,c){return r||(r=s),f+=e.slice(u,c).replace(by,ly),n&&(o=!0,f+="' +\n__e("+n+") +\n'"),a&&(i=!0,f+="';\n"+a+";\n__p += '"),r&&(f+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),u=c+t.length,t})),f+="';\n";var d=wy.call(t,"variable")&&t.variable;if(d){if(vy.test(d))throw new Error("Invalid `variable` option passed into `_.template`")}else f="with (obj) {\n"+f+"\n}\n";f=(i?f.replace(py,""):f).replace(dy,"$1").replace(gy,"$1;"),f="function("+(d||"obj")+") {\n"+(d?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(o?", __e = _.escape":"")+(i?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+f+"return __p\n}";var g=sf((function(){return Function(a,p+"return "+f).apply(void 0,c)}));if(g.source=f,rf(g))throw g;return g},templateSettings:hy,toLower:function(e){return Dl(e).toLowerCase()},toUpper:function(e){return Dl(e).toUpperCase()},trim:function(e,t,n){if((e=Dl(e))&&(n||void 0===t))return Ya(e);if(!e||!(t=Wa(t)))return e;var r=Af(e),o=Af(t);return df(r,Ay(r,o),Ty(r,o)+1).join("")},trimEnd:function(e,t,n){if((e=Dl(e))&&(n||void 0===t))return e.slice(0,Ka(e)+1);if(!e||!(t=Wa(t)))return e;var r=Af(e);return df(r,0,Ty(r,Af(t))+1).join("")},trimStart:function(e,t,n){if((e=Dl(e))&&(n||void 0===t))return e.replace(Iy,"");if(!e||!(t=Wa(t)))return e;var r=Af(e);return df(r,Ay(r,Af(t))).join("")},truncate:function(e,t){var n=30,r="...";if(Ga(t)){var o="separator"in t?t.separator:o;n="length"in t?rc(t.length):n,r="omission"in t?Wa(t.omission):r}var i=(e=Dl(e)).length;if(vf(e)){var s=Af(e);i=s.length}if(n>=i)return e;var a=n-Zv(r);if(a<1)return r;var c=s?df(s,0,a).join(""):e.slice(0,a);if(void 0===o)return c+r;if(s&&(a+=c.length-a),Jg(o)){if(e.slice(a).search(o)){var u,l=c;for(o.global||(o=RegExp(o.source,Dl(Py.exec(o))+"g")),o.lastIndex=0;u=o.exec(l);)var f=u.index;c=c.slice(0,void 0===f?a:f)}}else if(e.indexOf(Wa(o),a)!=a){var h=c.lastIndexOf(o);h>-1&&(c=c.slice(0,h))}return c+r},unescape:function(e){return(e=Dl(e))&&Ey.test(e)?e.replace($y,jy):e},upperCase:Hy,upperFirst:Pf,words:eh},h_={attempt:sf,bindAll:lf,cond:function(e){var t=null==e?0:e.length,n=Tp;return e=t?Ba(e,(function(e){if("function"!=typeof e[1])throw new TypeError("Expected a function");return[n(e[0]),e[1]]})):[],Tu((function(n){for(var r=-1;++r<t;){var o=e[r];if(kc(o[0],this,n))return kc(o[1],this,n)}}))},conforms:function(e){return function(e){var t=ol(e);return function(n){return Ap(n,e,t)}}(Xh(e,1))},constant:Gc,defaultTo:function(e,t){return null==e||e!=e?t:e},flow:Qd,flowRight:Zd,identity:oc,iteratee:function(e){return Tp("function"==typeof e?e:Xh(e,1))},matches:function(e){return _p(Xh(e,1))},matchesProperty:function(e,t){return Sp(e,Xh(t,1))},method:pv,methodOf:dv,mixin:vv,noop:Lc,nthArg:function(e){return e=rc(e),Tu((function(t){return wv(t,e)}))},over:Ev,overEvery:Dv,overSome:Nv,property:kp,propertyOf:function(e){return function(t){return null==e?void 0:Ul(e,t)}},range:jm,rangeRight:$m,stubArray:wh,stubFalse:Bu,stubObject:function(){return{}},stubString:function(){return""},stubTrue:function(){return!0},times:function(e,t){if((e=rc(e))<1||e>9007199254740991)return[];var n=**********,r=Sy(e,**********);e-=**********;for(var o=Lu(r,t=md(t));++n<e;)t(n);return o},toPath:function(e){return Ua(e)?Ba(e,Bl):Da(e)?[e]:Bc(Rl(Dl(e)))},uniqueId:function(e){var t=++Ny;return Dl(e)+t}};var p_=Math.max,d_=Math.min;var g_=Math.min;
/**
 * @license
 * Lodash (Custom Build) <https://lodash.com/>
 * Build: `lodash modularize exports="es" -o ./`
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */
var v_,m_=Array.prototype,y_=Object.prototype.hasOwnProperty,__=Ia?Ia.iterator:void 0,b_=Math.max,w_=Math.min,x_=function(e){return function(t,n,r){if(null==r){var o=Ga(n),i=o&&ol(n),s=i&&i.length&&Xd(n,i);(s?s.length:o)||(r=n,n=t,t=this)}return e(t,n,r)}}(vv);qc.after=r_.after,qc.ary=r_.ary,qc.assign=u_.assign,qc.assignIn=u_.assignIn,qc.assignInWith=u_.assignInWith,qc.assignWith=u_.assignWith,qc.at=u_.at,qc.before=r_.before,qc.bind=r_.bind,qc.bindAll=h_.bindAll,qc.bindKey=r_.bindKey,qc.castArray=o_.castArray,qc.chain=l_.chain,qc.chunk=e_.chunk,qc.compact=e_.compact,qc.concat=e_.concat,qc.cond=h_.cond,qc.conforms=h_.conforms,qc.constant=h_.constant,qc.countBy=t_.countBy,qc.create=u_.create,qc.curry=r_.curry,qc.curryRight=r_.curryRight,qc.debounce=r_.debounce,qc.defaults=u_.defaults,qc.defaultsDeep=u_.defaultsDeep,qc.defer=r_.defer,qc.delay=r_.delay,qc.difference=e_.difference,qc.differenceBy=e_.differenceBy,qc.differenceWith=e_.differenceWith,qc.drop=e_.drop,qc.dropRight=e_.dropRight,qc.dropRightWhile=e_.dropRightWhile,qc.dropWhile=e_.dropWhile,qc.fill=e_.fill,qc.filter=t_.filter,qc.flatMap=t_.flatMap,qc.flatMapDeep=t_.flatMapDeep,qc.flatMapDepth=t_.flatMapDepth,qc.flatten=e_.flatten,qc.flattenDeep=e_.flattenDeep,qc.flattenDepth=e_.flattenDepth,qc.flip=r_.flip,qc.flow=h_.flow,qc.flowRight=h_.flowRight,qc.fromPairs=e_.fromPairs,qc.functions=u_.functions,qc.functionsIn=u_.functionsIn,qc.groupBy=t_.groupBy,qc.initial=e_.initial,qc.intersection=e_.intersection,qc.intersectionBy=e_.intersectionBy,qc.intersectionWith=e_.intersectionWith,qc.invert=u_.invert,qc.invertBy=u_.invertBy,qc.invokeMap=t_.invokeMap,qc.iteratee=h_.iteratee,qc.keyBy=t_.keyBy,qc.keys=ol,qc.keysIn=u_.keysIn,qc.map=t_.map,qc.mapKeys=u_.mapKeys,qc.mapValues=u_.mapValues,qc.matches=h_.matches,qc.matchesProperty=h_.matchesProperty,qc.memoize=r_.memoize,qc.merge=u_.merge,qc.mergeWith=u_.mergeWith,qc.method=h_.method,qc.methodOf=h_.methodOf,qc.mixin=x_,qc.negate=yv,qc.nthArg=h_.nthArg,qc.omit=u_.omit,qc.omitBy=u_.omitBy,qc.once=r_.once,qc.orderBy=t_.orderBy,qc.over=h_.over,qc.overArgs=r_.overArgs,qc.overEvery=h_.overEvery,qc.overSome=h_.overSome,qc.partial=r_.partial,qc.partialRight=r_.partialRight,qc.partition=t_.partition,qc.pick=u_.pick,qc.pickBy=u_.pickBy,qc.property=h_.property,qc.propertyOf=h_.propertyOf,qc.pull=e_.pull,qc.pullAll=e_.pullAll,qc.pullAllBy=e_.pullAllBy,qc.pullAllWith=e_.pullAllWith,qc.pullAt=e_.pullAt,qc.range=h_.range,qc.rangeRight=h_.rangeRight,qc.rearg=r_.rearg,qc.reject=t_.reject,qc.remove=e_.remove,qc.rest=r_.rest,qc.reverse=e_.reverse,qc.sampleSize=t_.sampleSize,qc.set=u_.set,qc.setWith=u_.setWith,qc.shuffle=t_.shuffle,qc.slice=e_.slice,qc.sortBy=t_.sortBy,qc.sortedUniq=e_.sortedUniq,qc.sortedUniqBy=e_.sortedUniqBy,qc.split=f_.split,qc.spread=r_.spread,qc.tail=e_.tail,qc.take=e_.take,qc.takeRight=e_.takeRight,qc.takeRightWhile=e_.takeRightWhile,qc.takeWhile=e_.takeWhile,qc.tap=l_.tap,qc.throttle=r_.throttle,qc.thru=xy,qc.toArray=o_.toArray,qc.toPairs=u_.toPairs,qc.toPairsIn=u_.toPairsIn,qc.toPath=h_.toPath,qc.toPlainObject=o_.toPlainObject,qc.transform=u_.transform,qc.unary=r_.unary,qc.union=e_.union,qc.unionBy=e_.unionBy,qc.unionWith=e_.unionWith,qc.uniq=e_.uniq,qc.uniqBy=e_.uniqBy,qc.uniqWith=e_.uniqWith,qc.unset=u_.unset,qc.unzip=e_.unzip,qc.unzipWith=e_.unzipWith,qc.update=u_.update,qc.updateWith=u_.updateWith,qc.values=u_.values,qc.valuesIn=u_.valuesIn,qc.without=e_.without,qc.words=f_.words,qc.wrap=r_.wrap,qc.xor=e_.xor,qc.xorBy=e_.xorBy,qc.xorWith=e_.xorWith,qc.zip=e_.zip,qc.zipObject=e_.zipObject,qc.zipObjectDeep=e_.zipObjectDeep,qc.zipWith=e_.zipWith,qc.entries=u_.toPairs,qc.entriesIn=u_.toPairsIn,qc.extend=u_.assignIn,qc.extendWith=u_.assignInWith,x_(qc,qc),qc.add=i_.add,qc.attempt=h_.attempt,qc.camelCase=f_.camelCase,qc.capitalize=f_.capitalize,qc.ceil=i_.ceil,qc.clamp=s_,qc.clone=o_.clone,qc.cloneDeep=o_.cloneDeep,qc.cloneDeepWith=o_.cloneDeepWith,qc.cloneWith=o_.cloneWith,qc.conformsTo=o_.conformsTo,qc.deburr=f_.deburr,qc.defaultTo=h_.defaultTo,qc.divide=i_.divide,qc.endsWith=f_.endsWith,qc.eq=o_.eq,qc.escape=f_.escape,qc.escapeRegExp=f_.escapeRegExp,qc.every=t_.every,qc.find=t_.find,qc.findIndex=e_.findIndex,qc.findKey=u_.findKey,qc.findLast=t_.findLast,qc.findLastIndex=e_.findLastIndex,qc.findLastKey=u_.findLastKey,qc.floor=i_.floor,qc.forEach=t_.forEach,qc.forEachRight=t_.forEachRight,qc.forIn=u_.forIn,qc.forInRight=u_.forInRight,qc.forOwn=u_.forOwn,qc.forOwnRight=u_.forOwnRight,qc.get=u_.get,qc.gt=o_.gt,qc.gte=o_.gte,qc.has=u_.has,qc.hasIn=u_.hasIn,qc.head=e_.head,qc.identity=oc,qc.includes=t_.includes,qc.indexOf=e_.indexOf,qc.inRange=a_,qc.invoke=u_.invoke,qc.isArguments=o_.isArguments,qc.isArray=Ua,qc.isArrayBuffer=o_.isArrayBuffer,qc.isArrayLike=o_.isArrayLike,qc.isArrayLikeObject=o_.isArrayLikeObject,qc.isBoolean=o_.isBoolean,qc.isBuffer=o_.isBuffer,qc.isDate=o_.isDate,qc.isElement=o_.isElement,qc.isEmpty=o_.isEmpty,qc.isEqual=o_.isEqual,qc.isEqualWith=o_.isEqualWith,qc.isError=o_.isError,qc.isFinite=o_.isFinite,qc.isFunction=o_.isFunction,qc.isInteger=o_.isInteger,qc.isLength=o_.isLength,qc.isMap=o_.isMap,qc.isMatch=o_.isMatch,qc.isMatchWith=o_.isMatchWith,qc.isNaN=o_.isNaN,qc.isNative=o_.isNative,qc.isNil=o_.isNil,qc.isNull=o_.isNull,qc.isNumber=o_.isNumber,qc.isObject=Ga,qc.isObjectLike=o_.isObjectLike,qc.isPlainObject=o_.isPlainObject,qc.isRegExp=o_.isRegExp,qc.isSafeInteger=o_.isSafeInteger,qc.isSet=o_.isSet,qc.isString=o_.isString,qc.isSymbol=o_.isSymbol,qc.isTypedArray=o_.isTypedArray,qc.isUndefined=o_.isUndefined,qc.isWeakMap=o_.isWeakMap,qc.isWeakSet=o_.isWeakSet,qc.join=e_.join,qc.kebabCase=f_.kebabCase,qc.last=hd,qc.lastIndexOf=e_.lastIndexOf,qc.lowerCase=f_.lowerCase,qc.lowerFirst=f_.lowerFirst,qc.lt=o_.lt,qc.lte=o_.lte,qc.max=i_.max,qc.maxBy=i_.maxBy,qc.mean=i_.mean,qc.meanBy=i_.meanBy,qc.min=i_.min,qc.minBy=i_.minBy,qc.stubArray=h_.stubArray,qc.stubFalse=h_.stubFalse,qc.stubObject=h_.stubObject,qc.stubString=h_.stubString,qc.stubTrue=h_.stubTrue,qc.multiply=i_.multiply,qc.nth=e_.nth,qc.noop=h_.noop,qc.now=n_.now,qc.pad=f_.pad,qc.padEnd=f_.padEnd,qc.padStart=f_.padStart,qc.parseInt=f_.parseInt,qc.random=c_,qc.reduce=t_.reduce,qc.reduceRight=t_.reduceRight,qc.repeat=f_.repeat,qc.replace=f_.replace,qc.result=u_.result,qc.round=i_.round,qc.sample=t_.sample,qc.size=t_.size,qc.snakeCase=f_.snakeCase,qc.some=t_.some,qc.sortedIndex=e_.sortedIndex,qc.sortedIndexBy=e_.sortedIndexBy,qc.sortedIndexOf=e_.sortedIndexOf,qc.sortedLastIndex=e_.sortedLastIndex,qc.sortedLastIndexBy=e_.sortedLastIndexBy,qc.sortedLastIndexOf=e_.sortedLastIndexOf,qc.startCase=f_.startCase,qc.startsWith=f_.startsWith,qc.subtract=i_.subtract,qc.sum=i_.sum,qc.sumBy=i_.sumBy,qc.template=f_.template,qc.times=h_.times,qc.toFinite=o_.toFinite,qc.toInteger=rc,qc.toLength=o_.toLength,qc.toLower=f_.toLower,qc.toNumber=o_.toNumber,qc.toSafeInteger=o_.toSafeInteger,qc.toString=o_.toString,qc.toUpper=f_.toUpper,qc.trim=f_.trim,qc.trimEnd=f_.trimEnd,qc.trimStart=f_.trimStart,qc.truncate=f_.truncate,qc.unescape=f_.unescape,qc.uniqueId=h_.uniqueId,qc.upperCase=f_.upperCase,qc.upperFirst=f_.upperFirst,qc.each=t_.forEach,qc.eachRight=t_.forEachRight,qc.first=e_.head,x_(qc,(v_={},$p(qc,(function(e,t){y_.call(qc.prototype,t)||(v_[t]=e)})),v_),{chain:!1}),qc.VERSION="4.17.21",(qc.templateSettings=f_.templateSettings).imports._=qc,eu(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){qc[e].placeholder=qc})),eu(["drop","take"],(function(e,t){Ec.prototype[e]=function(n){n=void 0===n?1:b_(rc(n),0);var r=this.__filtered__&&!t?new Ec(this):this.clone();return r.__filtered__?r.__takeCount__=w_(n,r.__takeCount__):r.__views__.push({size:w_(n,**********),type:e+(r.__dir__<0?"Right":"")}),r},Ec.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),eu(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;Ec.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Tp(e),type:n}),t.__filtered__=t.__filtered__||r,t}})),eu(["head","last"],(function(e,t){var n="take"+(t?"Right":"");Ec.prototype[e]=function(){return this[n](1).value()[0]}})),eu(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");Ec.prototype[e]=function(){return this.__filtered__?new Ec(this):this[n](1)}})),Ec.prototype.compact=function(){return this.filter(oc)},Ec.prototype.find=function(e){return this.filter(e).head()},Ec.prototype.findLast=function(e){return this.reverse().find(e)},Ec.prototype.invokeMap=Tu((function(e,t){return"function"==typeof e?new Ec(this):this.map((function(n){return Eg(n,e,t)}))})),Ec.prototype.reject=function(e){return this.filter(yv(Tp(e)))},Ec.prototype.slice=function(e,t){e=rc(e);var n=this;return n.__filtered__&&(e>0||t<0)?new Ec(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),void 0!==t&&(n=(t=rc(t))<0?n.dropRight(-t):n.take(t-e)),n)},Ec.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Ec.prototype.toArray=function(){return this.take(**********)},$p(Ec.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),o=qc[r?"take"+("last"==t?"Right":""):t],i=r||/^find/.test(t);o&&(qc.prototype[t]=function(){var t=this.__wrapped__,s=r?[1]:arguments,a=t instanceof Ec,c=s[0],u=a||Ua(t),l=function(e){var t=o.apply(qc,Wl([e],s));return r&&f?t[0]:t};u&&n&&"function"==typeof c&&1!=c.length&&(a=u=!1);var f=this.__chain__,h=!!this.__actions__.length,p=i&&!f,d=a&&!h;if(!i&&u){t=d?t:new Ec(this);var g=e.apply(t,s);return g.__actions__.push({func:xy,args:[l],thisArg:void 0}),new Nc(g,f)}return p&&d?e.apply(this,s):(g=this.thru(l),p?r?g.value()[0]:g.value():g)})})),eu(["pop","push","shift","sort","splice","unshift"],(function(e){var t=m_[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);qc.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var o=this.value();return t.apply(Ua(o)?o:[],e)}return this[n]((function(n){return t.apply(Ua(n)?n:[],e)}))}})),$p(Ec.prototype,(function(e,t){var n=qc[t];if(n){var r=n.name+"";y_.call(Mc,r)||(Mc[r]=[]),Mc[r].push({name:t,func:n})}})),Mc[du(void 0,2).name]=[{name:"wrapper",func:void 0}],Ec.prototype.clone=function(){var e=new Ec(this.__wrapped__);return e.__actions__=Bc(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Bc(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Bc(this.__views__),e},Ec.prototype.reverse=function(){if(this.__filtered__){var e=new Ec(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Ec.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ua(e),r=t<0,o=n?e.length:0,i=function(e,t,n){for(var r=-1,o=n.length;++r<o;){var i=n[r],s=i.size;switch(i.type){case"drop":e+=s;break;case"dropRight":t-=s;break;case"take":t=d_(t,e+s);break;case"takeRight":e=p_(e,t-s)}}return{start:e,end:t}}(0,o,this.__views__),s=i.start,a=i.end,c=a-s,u=r?a:s-1,l=this.__iteratees__,f=l.length,h=0,p=g_(c,this.__takeCount__);if(!n||!r&&o==c&&p==c)return Oy(e,this.__actions__);var d=[];e:for(;c--&&h<p;){for(var g=-1,v=e[u+=t];++g<f;){var m=l[g],y=m.iteratee,_=m.type,b=y(v);if(2==_)v=b;else if(!b){if(1==_)continue e;break e}}d[h++]=v}return d},qc.prototype.at=l_.at,qc.prototype.chain=l_.wrapperChain,qc.prototype.commit=l_.commit,qc.prototype.next=l_.next,qc.prototype.plant=l_.plant,qc.prototype.reverse=l_.reverse,qc.prototype.toJSON=qc.prototype.valueOf=qc.prototype.value=l_.value,qc.prototype.first=qc.prototype.head,__&&(qc.prototype[__]=l_.toIterator);var S_={exports:{}},O_=S_.exports=function(){var e=1e3,t=6e4,n=36e5,r="millisecond",o="second",i="minute",s="hour",a="day",c="week",u="month",l="quarter",f="year",h="date",p="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,g=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,v={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],n=e%100;return"["+e+(t[(n-20)%10]||t[n]||t[0])+"]"}},m=function(e,t,n){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(n)+e},y={s:m,z:function(e){var t=-e.utcOffset(),n=Math.abs(t),r=Math.floor(n/60),o=n%60;return(t<=0?"+":"-")+m(r,2,"0")+":"+m(o,2,"0")},m:function e(t,n){if(t.date()<n.date())return-e(n,t);var r=12*(n.year()-t.year())+(n.month()-t.month()),o=t.clone().add(r,u),i=n-o<0,s=t.clone().add(r+(i?-1:1),u);return+(-(r+(n-o)/(i?o-s:s-o))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:u,y:f,w:c,d:a,D:h,h:s,m:i,s:o,ms:r,Q:l}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},_="en",b={};b[_]=v;var w="$isDayjsObject",x=function(e){return e instanceof T||!(!e||!e[w])},S=function e(t,n,r){var o;if(!t)return _;if("string"==typeof t){var i=t.toLowerCase();b[i]&&(o=i),n&&(b[i]=n,o=i);var s=t.split("-");if(!o&&s.length>1)return e(s[0])}else{var a=t.name;b[a]=t,o=a}return!r&&o&&(_=o),o||!r&&_},O=function(e,t){if(x(e))return e.clone();var n="object"==typeof t?t:{};return n.date=e,n.args=arguments,new T(n)},k=y;k.l=S,k.i=x,k.w=function(e,t){return O(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var T=function(){function v(e){this.$L=S(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[w]=!0}var m=v.prototype;return m.parse=function(e){this.$d=function(e){var t=e.date,n=e.utc;if(null===t)return new Date(NaN);if(k.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(d);if(r){var o=r[2]-1||0,i=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)):new Date(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)}}return new Date(t)}(e),this.init()},m.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},m.$utils=function(){return k},m.isValid=function(){return!(this.$d.toString()===p)},m.isSame=function(e,t){var n=O(e);return this.startOf(t)<=n&&n<=this.endOf(t)},m.isAfter=function(e,t){return O(e)<this.startOf(t)},m.isBefore=function(e,t){return this.endOf(t)<O(e)},m.$g=function(e,t,n){return k.u(e)?this[t]:this.set(n,e)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(e,t){var n=this,r=!!k.u(t)||t,l=k.p(e),p=function(e,t){var o=k.w(n.$u?Date.UTC(n.$y,t,e):new Date(n.$y,t,e),n);return r?o:o.endOf(a)},d=function(e,t){return k.w(n.toDate()[e].apply(n.toDate("s"),(r?[0,0,0,0]:[23,59,59,999]).slice(t)),n)},g=this.$W,v=this.$M,m=this.$D,y="set"+(this.$u?"UTC":"");switch(l){case f:return r?p(1,0):p(31,11);case u:return r?p(1,v):p(0,v+1);case c:var _=this.$locale().weekStart||0,b=(g<_?g+7:g)-_;return p(r?m-b:m+(6-b),v);case a:case h:return d(y+"Hours",0);case s:return d(y+"Minutes",1);case i:return d(y+"Seconds",2);case o:return d(y+"Milliseconds",3);default:return this.clone()}},m.endOf=function(e){return this.startOf(e,!1)},m.$set=function(e,t){var n,c=k.p(e),l="set"+(this.$u?"UTC":""),p=(n={},n[a]=l+"Date",n[h]=l+"Date",n[u]=l+"Month",n[f]=l+"FullYear",n[s]=l+"Hours",n[i]=l+"Minutes",n[o]=l+"Seconds",n[r]=l+"Milliseconds",n)[c],d=c===a?this.$D+(t-this.$W):t;if(c===u||c===f){var g=this.clone().set(h,1);g.$d[p](d),g.init(),this.$d=g.set(h,Math.min(this.$D,g.daysInMonth())).$d}else p&&this.$d[p](d);return this.init(),this},m.set=function(e,t){return this.clone().$set(e,t)},m.get=function(e){return this[k.p(e)]()},m.add=function(r,l){var h,p=this;r=Number(r);var d=k.p(l),g=function(e){var t=O(p);return k.w(t.date(t.date()+Math.round(e*r)),p)};if(d===u)return this.set(u,this.$M+r);if(d===f)return this.set(f,this.$y+r);if(d===a)return g(1);if(d===c)return g(7);var v=(h={},h[i]=t,h[s]=n,h[o]=e,h)[d]||1,m=this.$d.getTime()+r*v;return k.w(m,this)},m.subtract=function(e,t){return this.add(-1*e,t)},m.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return n.invalidDate||p;var r=e||"YYYY-MM-DDTHH:mm:ssZ",o=k.z(this),i=this.$H,s=this.$m,a=this.$M,c=n.weekdays,u=n.months,l=n.meridiem,f=function(e,n,o,i){return e&&(e[n]||e(t,r))||o[n].slice(0,i)},h=function(e){return k.s(i%12||12,e,"0")},d=l||function(e,t,n){var r=e<12?"AM":"PM";return n?r.toLowerCase():r};return r.replace(g,(function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return k.s(t.$y,4,"0");case"M":return a+1;case"MM":return k.s(a+1,2,"0");case"MMM":return f(n.monthsShort,a,u,3);case"MMMM":return f(u,a);case"D":return t.$D;case"DD":return k.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return f(n.weekdaysMin,t.$W,c,2);case"ddd":return f(n.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(i);case"HH":return k.s(i,2,"0");case"h":return h(1);case"hh":return h(2);case"a":return d(i,s,!0);case"A":return d(i,s,!1);case"m":return String(s);case"mm":return k.s(s,2,"0");case"s":return String(t.$s);case"ss":return k.s(t.$s,2,"0");case"SSS":return k.s(t.$ms,3,"0");case"Z":return o}return null}(e)||o.replace(":","")}))},m.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},m.diff=function(r,h,p){var d,g=this,v=k.p(h),m=O(r),y=(m.utcOffset()-this.utcOffset())*t,_=this-m,b=function(){return k.m(g,m)};switch(v){case f:d=b()/12;break;case u:d=b();break;case l:d=b()/3;break;case c:d=(_-y)/6048e5;break;case a:d=(_-y)/864e5;break;case s:d=_/n;break;case i:d=_/t;break;case o:d=_/e;break;default:d=_}return p?d:k.a(d)},m.daysInMonth=function(){return this.endOf(u).$D},m.$locale=function(){return b[this.$L]},m.locale=function(e,t){if(!e)return this.$L;var n=this.clone(),r=S(e,t,!0);return r&&(n.$L=r),n},m.clone=function(){return k.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},v}(),A=T.prototype;return O.prototype=A,[["$ms",r],["$s",o],["$m",i],["$H",s],["$W",a],["$M",u],["$y",f],["$D",h]].forEach((function(e){A[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),O.extend=function(e,t){return e.$i||(e(t,T,O),e.$i=!0),O},O.locale=S,O.isDayjs=x,O.unix=function(e){return O(1e3*e)},O.en=b[_],O.Ls=b,O.p={},O}();const k_=Zs(O_);
/*!
 * pinia v2.1.7
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */
let T_;const A_=e=>T_=e,I_=Symbol();function P_(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var j_,$_;($_=j_||(j_={})).direct="direct",$_.patchObject="patch object",$_.patchFunction="patch function";const E_=()=>{};function L_(e,t,n,r=E_){e.push(t);const o=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};return!n&&sn()&&function(e){tn&&tn.cleanups.push(e)}(o),o}function C_(e,...t){e.slice().forEach((e=>{e(...t)}))}const M_=e=>e();function R_(e,t){e instanceof Map&&t instanceof Map&&t.forEach(((t,n)=>e.set(n,t))),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];P_(o)&&P_(r)&&e.hasOwnProperty(n)&&!Sr(r)&&!pr(r)?e[n]=R_(o,r):e[n]=r}return e}const D_=Symbol();const{assign:N_}=Object;function B_(e,t,n={},r,o,i){let s;const a=N_({actions:{}},n),c={deep:!0};let u,l,f,h=[],p=[];const d=r.state.value[e];let g;function v(t){let n;u=l=!1,"function"==typeof t?(t(r.state.value[e]),n={type:j_.patchFunction,storeId:e,events:f}):(R_(r.state.value[e],t),n={type:j_.patchObject,payload:t,storeId:e,events:f});const o=g=Symbol();zr().then((()=>{g===o&&(u=!0)})),l=!0,C_(h,n,r.state.value[e])}i||d||(r.state.value[e]={}),Or({});const m=i?function(){const{state:e}=n,t=e?e():{};this.$patch((e=>{N_(e,t)}))}:E_;function y(t,n){return function(){A_(r);const o=Array.from(arguments),i=[],s=[];function a(e){i.push(e)}function c(e){s.push(e)}let u;C_(p,{args:o,name:t,store:_,after:a,onError:c});try{u=n.apply(this&&this.$id===e?this:_,o)}catch(l){throw C_(s,l),l}return u instanceof Promise?u.then((e=>(C_(i,e),e))).catch((e=>(C_(s,e),Promise.reject(e)))):(C_(i,u),u)}}const _=lr({_p:r,$id:e,$onAction:L_.bind(null,p),$patch:v,$reset:m,$subscribe(t,n={}){const o=L_(h,t,n.detached,(()=>i())),i=s.run((()=>so((()=>r.state.value[e]),(r=>{("sync"===n.flush?l:u)&&t({storeId:e,type:j_.direct,events:f},r)}),N_({},c,n))));return o},$dispose:function(){s.stop(),h=[],p=[],r._s.delete(e)}});r._s.set(e,_);const b=(r._a&&r._a.runWithContext||M_)((()=>r._e.run((()=>(s=on()).run(t)))));for(const S in b){const t=b[S];if(Sr(t)&&(!Sr(x=t)||!x.effect)||pr(t))i||(!d||P_(w=t)&&w.hasOwnProperty(D_)||(Sr(t)?t.value=d[S]:R_(t,d[S])),r.state.value[e][S]=t);else if("function"==typeof t){const e=y(S,t);b[S]=e,a.actions[S]=t}}var w,x;return N_(_,b),N_(vr(_),b),Object.defineProperty(_,"$state",{get:()=>r.state.value[e],set:e=>{v((t=>{N_(t,e)}))}}),r._p.forEach((e=>{N_(_,s.run((()=>e({store:_,app:r._a,pinia:r,options:a}))))})),d&&i&&n.hydrate&&n.hydrate(_.$state,d),u=!0,l=!0,_}var U_,F_;const q_=void 0===en||["web","h5",void 0].includes(null==(F_=null==(U_=null==en?void 0:en.getSystemInfoSync())?void 0:U_.uniPlatform)?void 0:F_.toLocaleLowerCase()),W_=(e,t,n)=>{const r=e.storage,o=e.key||t.$id,i=q_||(null==n?void 0:n.enforceCustomStorage);if(e.paths){const n=e.paths.reduce(((e,n)=>(e[n]=t.$state[n],e)),{});i&&r?r.setItem(o,JSON.stringify(n)):en.setStorage({key:o,data:JSON.stringify(n)})}else i&&r?r.setItem(o,JSON.stringify(t.$state)):en.setStorage({key:o,data:JSON.stringify(t.$state)})};var H_={exports:{}};const z_=Zs(H_.exports=function(e,t,n){e=e||{};var r=t.prototype,o={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function i(e,t,n,o){return r.fromToBase(e,t,n,o)}n.en.relativeTime=o,r.fromToBase=function(t,r,i,s,a){for(var c,u,l,f=i.$locale().relativeTime||o,h=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],p=h.length,d=0;d<p;d+=1){var g=h[d];g.d&&(c=s?n(t).diff(i,g.d,!0):i.diff(t,g.d,!0));var v=(e.rounding||Math.round)(Math.abs(c));if(l=c>0,v<=g.r||!g.r){v<=1&&d>0&&(g=h[d-1]);var m=f[g.l];a&&(v=a(""+v)),u="string"==typeof m?m.replace("%d",v):m(v,r,g.l,l);break}}if(r)return u;var y=l?f.future:f.past;return"function"==typeof y?y(u):y.replace("%s",u)},r.to=function(e,t){return i(e,t,this,!0)},r.from=function(e,t){return i(e,t,this)};var s=function(e){return e.$u?n.utc():n()};r.toNow=function(e){return this.to(s(this),e)},r.fromNow=function(e){return this.from(s(this),e)}});var V_,K_,J_,Y_,G_,Q_,Z_,X_,eb,tb,nb,rb,ob,ib,sb,ab,cb,ub,lb,fb,hb={exports:{}};const pb=Zs(hb.exports=(Z_=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,tb=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,nb={years:X_=31536e6,months:eb=2628e6,days:Q_=864e5,hours:G_=36e5,minutes:Y_=6e4,seconds:J_=1e3,milliseconds:1,weeks:6048e5},rb=function(e){return e instanceof lb},ob=function(e,t,n){return new lb(e,n,t.$l)},ib=function(e){return K_.p(e)+"s"},sb=function(e){return e<0},ab=function(e){return sb(e)?Math.ceil(e):Math.floor(e)},cb=function(e){return Math.abs(e)},ub=function(e,t){return e?sb(e)?{negative:!0,format:""+cb(e)+t}:{negative:!1,format:""+e+t}:{negative:!1,format:""}},lb=function(){function e(e,t,n){var r=this;if(this.$d={},this.$l=n,void 0===e&&(this.$ms=0,this.parseFromMilliseconds()),t)return ob(e*nb[ib(t)],this);if("number"==typeof e)return this.$ms=e,this.parseFromMilliseconds(),this;if("object"==typeof e)return Object.keys(e).forEach((function(t){r.$d[ib(t)]=e[t]})),this.calMilliseconds(),this;if("string"==typeof e){var o=e.match(tb);if(o){var i=o.slice(2).map((function(e){return null!=e?Number(e):0}));return this.$d.years=i[0],this.$d.months=i[1],this.$d.weeks=i[2],this.$d.days=i[3],this.$d.hours=i[4],this.$d.minutes=i[5],this.$d.seconds=i[6],this.calMilliseconds(),this}}return this}var t=e.prototype;return t.calMilliseconds=function(){var e=this;this.$ms=Object.keys(this.$d).reduce((function(t,n){return t+(e.$d[n]||0)*nb[n]}),0)},t.parseFromMilliseconds=function(){var e=this.$ms;this.$d.years=ab(e/X_),e%=X_,this.$d.months=ab(e/eb),e%=eb,this.$d.days=ab(e/Q_),e%=Q_,this.$d.hours=ab(e/G_),e%=G_,this.$d.minutes=ab(e/Y_),e%=Y_,this.$d.seconds=ab(e/J_),e%=J_,this.$d.milliseconds=e},t.toISOString=function(){var e=ub(this.$d.years,"Y"),t=ub(this.$d.months,"M"),n=+this.$d.days||0;this.$d.weeks&&(n+=7*this.$d.weeks);var r=ub(n,"D"),o=ub(this.$d.hours,"H"),i=ub(this.$d.minutes,"M"),s=this.$d.seconds||0;this.$d.milliseconds&&(s+=this.$d.milliseconds/1e3,s=Math.round(1e3*s)/1e3);var a=ub(s,"S"),c=e.negative||t.negative||r.negative||o.negative||i.negative||a.negative,u=o.format||i.format||a.format?"T":"",l=(c?"-":"")+"P"+e.format+t.format+r.format+u+o.format+i.format+a.format;return"P"===l||"-P"===l?"P0D":l},t.toJSON=function(){return this.toISOString()},t.format=function(e){var t=e||"YYYY-MM-DDTHH:mm:ss",n={Y:this.$d.years,YY:K_.s(this.$d.years,2,"0"),YYYY:K_.s(this.$d.years,4,"0"),M:this.$d.months,MM:K_.s(this.$d.months,2,"0"),D:this.$d.days,DD:K_.s(this.$d.days,2,"0"),H:this.$d.hours,HH:K_.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:K_.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:K_.s(this.$d.seconds,2,"0"),SSS:K_.s(this.$d.milliseconds,3,"0")};return t.replace(Z_,(function(e,t){return t||String(n[e])}))},t.as=function(e){return this.$ms/nb[ib(e)]},t.get=function(e){var t=this.$ms,n=ib(e);return"milliseconds"===n?t%=1e3:t="weeks"===n?ab(t/nb[n]):this.$d[n],t||0},t.add=function(e,t,n){var r;return r=t?e*nb[ib(t)]:rb(e)?e.$ms:ob(e,this).$ms,ob(this.$ms+r*(n?-1:1),this)},t.subtract=function(e,t){return this.add(e,t,!0)},t.locale=function(e){var t=this.clone();return t.$l=e,t},t.clone=function(){return ob(this.$ms,this)},t.humanize=function(e){return V_().add(this.$ms,"ms").locale(this.$l).fromNow(!e)},t.valueOf=function(){return this.asMilliseconds()},t.milliseconds=function(){return this.get("milliseconds")},t.asMilliseconds=function(){return this.as("milliseconds")},t.seconds=function(){return this.get("seconds")},t.asSeconds=function(){return this.as("seconds")},t.minutes=function(){return this.get("minutes")},t.asMinutes=function(){return this.as("minutes")},t.hours=function(){return this.get("hours")},t.asHours=function(){return this.as("hours")},t.days=function(){return this.get("days")},t.asDays=function(){return this.as("days")},t.weeks=function(){return this.get("weeks")},t.asWeeks=function(){return this.as("weeks")},t.months=function(){return this.get("months")},t.asMonths=function(){return this.as("months")},t.years=function(){return this.get("years")},t.asYears=function(){return this.as("years")},e}(),fb=function(e,t,n){return e.add(t.years()*n,"y").add(t.months()*n,"M").add(t.days()*n,"d").add(t.hours()*n,"h").add(t.minutes()*n,"m").add(t.seconds()*n,"s").add(t.milliseconds()*n,"ms")},function(e,t,n){V_=n,K_=n().$utils(),n.duration=function(e,t){var r=n.locale();return ob(e,{$l:r},t)},n.isDuration=rb;var r=t.prototype.add,o=t.prototype.subtract;t.prototype.add=function(e,t){return rb(e)?fb(this,e,1):r.bind(this)(e,t)},t.prototype.subtract=function(e,t){return rb(e)?fb(this,e,-1):o.bind(this)(e,t)}}));function db(e){let t,n,r,o=!1;return function(i){void 0===t?(t=i,n=0,r=-1):t=function(e,t){const n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}(t,i);const s=t.length;let a=0;for(;n<s;){o&&(10===t[n]&&(a=++n),o=!1);let i=-1;for(;n<s&&-1===i;++n)switch(t[n]){case 58:-1===r&&(r=n-a);break;case 13:o=!0;case 10:i=n}if(-1===i)break;e(t.subarray(a,i),r),a=n,r=-1}a===s?t=void 0:0!==a&&(t=t.subarray(a),n-=a)}}!function(e){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e),r={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(e,t){return"W"===t?e+"周":e+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(e,t){var n=100*e+t;return n<600?"凌晨":n<900?"早上":n<1100?"上午":n<1300?"中午":n<1800?"下午":"晚上"}};n.default.locale(r,null,!0)}(O_);function gb(e){const t=e.headers.get("content-type");if(!(null==t?void 0:t.startsWith("text/event-stream")))throw new Error(`Expected content-type to be text/event-stream, Actual: ${t}`)}const vb={easycom:{autoscan:!0,custom:{"^s-(.*)":"@/sheep/components/s-$1/s-$1.vue","^su-(.*)":"@/sheep/ui/su-$1/su-$1.vue"}},pages:[{path:"pages/index/login",style:{navigationBarTitleText:"登录"}},{path:"pages/index/register",style:{navigationBarTitleText:"注册企业"}},{path:"pages/index/index",aliasPath:"/",style:{navigationBarTitleText:"首页",enablePullDownRefresh:!0},meta:{auth:!1,sync:!0,title:"首页",group:"商城"}},{path:"pages/index/user",style:{navigationBarTitleText:"个人中心",enablePullDownRefresh:!0},meta:{sync:!0,title:"个人中心",group:"商城"}},{path:"pages/index/contract",style:{navigationBarTitleText:"财税合同"},meta:{sync:!0,title:"财税合同",group:"商城"}},{path:"pages/contractsign/list",style:{navigationBarTitleText:"合同签署方管理"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"pages/contractsign/detail",style:{navigationBarTitleText:"合同签署方详情"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"pages/enterprisepartnerthird/list",style:{navigationBarTitleText:"第三方风险检测管理"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"pages/enterprisepartnerthird/reportList",style:{navigationBarTitleText:"第三方风险检测详情"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"pages/index/ai",style:{navigationBarTitleText:"Q智星AI"},meta:{sync:!0,title:"Q智星AI",group:"商城"}},{path:"pages/index/college",style:{navigationBarTitleText:"商学院"},meta:{sync:!0,title:"商学院",group:"商城"}},{path:"pages/college/detail",style:{navigationBarTitleText:"商学院"},meta:{sync:!0,title:"商学院",group:"商城"}},{path:"pages/tax/list",style:{navigationBarTitleText:"税务风险检测"},meta:{sync:!0,title:"税务风险检测",group:"商城"}},{path:"pages/tax/setting",style:{navigationBarTitleText:"税务风险检测设置"},meta:{sync:!0,title:"税务风险检测",group:"商城"}},{path:"pages/finance/list",style:{navigationBarTitleText:"财务自动检测"},meta:{sync:!0,title:"财务自动检测",group:"商城"}},{path:"pages/finance/create",style:{navigationBarTitleText:"发起财务风险检测"},meta:{sync:!0,title:"财务自动检测",group:"商城"}},{path:"pages/finance/detail",style:{navigationBarTitleText:"财务自动检测详情"},meta:{sync:!0,title:"财务自动检测",group:"商城"}},{path:"pages/finance/setting",style:{navigationBarTitleText:"财务自动检测开通"},meta:{sync:!0,title:"财务自动检测",group:"商城"}},{path:"pages/message/list",style:{navigationBarTitleText:"消息列表"},meta:{sync:!0,title:"消息管理",group:"商城"}},{path:"pages/message/detail",style:{navigationBarTitleText:"消息详情"},meta:{sync:!0,title:"消息管理",group:"商城"}},{path:"pages/index/category",style:{navigationBarTitleText:"商品分类"},meta:{sync:!0,title:"商品分类",group:"商城"}},{path:"pages/index/cart",style:{navigationBarTitleText:"购物车"},meta:{sync:!0,title:"购物车",group:"商城"}},{path:"pages/index/search",style:{navigationBarTitleText:"搜索"},meta:{sync:!0,title:"搜索",group:"商城"}},{path:"pages/index/page",style:{navigationBarTitleText:""},meta:{auth:!1,sync:!0,title:"自定义页面",group:"商城"}}],subPackages:[{root:"pages/contract",pages:[{path:"list",style:{navigationBarTitleText:"合同管理"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"create",style:{navigationBarTitleText:"上传合同"},meta:{sync:!0,title:"财税合同",group:"商城"}},{path:"detail",style:{navigationBarTitleText:"财税合同"},meta:{sync:!0,title:"财税合同",group:"商城"}},{path:"risk-list",style:{navigationBarTitleText:"第三方风险排查"},meta:{sync:!0,title:"财税合同",group:"商城"}}]},{root:"pages/contractdraft",pages:[{path:"list",style:{navigationBarTitleText:"合同稿件管理"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"detail",style:{navigationBarTitleText:"合同稿件详情"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"reportList",style:{navigationBarTitleText:"合同稿件诊断报告管理"},meta:{sync:!0,title:"合同管理",group:"商城"}},{path:"reportDetail",style:{navigationBarTitleText:"合同稿件诊断报告详情"},meta:{sync:!0,title:"合同管理",group:"商城"}}]},{root:"pages/goods",pages:[{path:"index",style:{navigationBarTitleText:"商品详情"},meta:{sync:!0,title:"普通商品",group:"商品"}},{path:"groupon",style:{navigationBarTitleText:"拼团商品"},meta:{sync:!0,title:"拼团商品",group:"商品"}},{path:"seckill",style:{navigationBarTitleText:"秒杀商品"},meta:{sync:!0,title:"秒杀商品",group:"商品"}},{path:"point",style:{navigationBarTitleText:"积分商品"},meta:{sync:!0,title:"积分商品",group:"商品"}},{path:"list",style:{navigationBarTitleText:"商品列表"},meta:{sync:!0,title:"商品列表",group:"商品"}},{path:"comment/add",style:{navigationBarTitleText:"评价商品"},meta:{auth:!0}},{path:"comment/list",style:{navigationBarTitleText:"商品评价"}}]},{root:"pages/order",pages:[{path:"detail",style:{navigationBarTitleText:"订单详情"},meta:{auth:!0,title:"订单详情"}},{path:"confirm",style:{navigationBarTitleText:"确认订单"},meta:{auth:!0,title:"确认订单"}},{path:"list",style:{navigationBarTitleText:"我的订单",enablePullDownRefresh:!0},meta:{auth:!0,sync:!0,title:"用户订单",group:"订单中心"}},{path:"aftersale/apply",style:{navigationBarTitleText:"申请售后"},meta:{auth:!0,title:"申请售后"}},{path:"aftersale/return-delivery",style:{navigationBarTitleText:"退货物流"},meta:{auth:!0,title:"退货物流"}},{path:"aftersale/list",style:{navigationBarTitleText:"售后列表"},meta:{auth:!0,sync:!0,title:"售后订单",group:"订单中心"}},{path:"aftersale/detail",style:{navigationBarTitleText:"售后详情"},meta:{auth:!0,title:"售后详情"}},{path:"aftersale/log",style:{navigationBarTitleText:"售后进度"},meta:{auth:!0,title:"售后进度"}},{path:"express/log",style:{navigationBarTitleText:"物流轨迹"},meta:{auth:!0,title:"物流轨迹"}}]},{root:"pages/user",pages:[{path:"info",style:{navigationBarTitleText:"我的信息"},meta:{auth:!0,sync:!0,title:"用户信息",group:"用户中心"}},{path:"goods-collect",style:{navigationBarTitleText:"我的收藏"},meta:{auth:!0,sync:!0,title:"商品收藏",group:"用户中心"}},{path:"goods-log",style:{navigationBarTitleText:"我的足迹"},meta:{auth:!0,sync:!0,title:"浏览记录",group:"用户中心"}},{path:"address/list",style:{navigationBarTitleText:"收货地址"},meta:{auth:!0,sync:!0,title:"地址管理",group:"用户中心"}},{path:"address/edit",style:{navigationBarTitleText:"编辑地址"},meta:{auth:!0,title:"编辑地址"}},{path:"goods_details_store/index",style:{navigationBarTitleText:"自提门店"},meta:{auth:!0,sync:!0,title:"地址管理",group:"用户中心"}},{path:"wallet/money",style:{navigationBarTitleText:"我的余额"},meta:{auth:!0,sync:!0,title:"用户余额",group:"用户中心"}},{path:"wallet/score",style:{navigationBarTitleText:"我的积分"},meta:{auth:!0,sync:!0,title:"用户积分",group:"用户中心"}},{path:"change-password",style:{navigationBarTitleText:"修改密码"},meta:{auth:!0,sync:!0,title:"修改密码",group:"用户中心"}}]},{root:"pages/commission",pages:[{path:"index",style:{navigationBarTitleText:"分销"},meta:{auth:!0,sync:!0,title:"分销中心",group:"分销商城"}},{path:"wallet",style:{navigationBarTitleText:"我的佣金"},meta:{auth:!0,sync:!0,title:"用户佣金",group:"分销中心"}},{path:"goods",style:{navigationBarTitleText:"推广商品"},meta:{auth:!0,sync:!0,title:"推广商品",group:"分销商城"}},{path:"order",style:{navigationBarTitleText:"分销订单"},meta:{auth:!0,sync:!0,title:"分销订单",group:"分销商城"}},{path:"team",style:{navigationBarTitleText:"我的团队"},meta:{auth:!0,sync:!0,title:"我的团队",group:"分销商城"}},{path:"promoter",style:{navigationBarTitleText:"推广人排行榜"},meta:{auth:!0,sync:!0,title:"推广人排行榜",group:"分销商城"}},{path:"commission-ranking",style:{navigationBarTitleText:"佣金排行榜"},meta:{auth:!0,sync:!0,title:"佣金排行榜",group:"分销商城"}},{path:"withdraw",style:{navigationBarTitleText:"申请提现"},meta:{auth:!0,sync:!0,title:"申请提现",group:"分销商城"}}]},{root:"pages/app",pages:[{path:"sign",style:{navigationBarTitleText:"签到中心"},meta:{auth:!0,sync:!0,title:"签到中心",group:"应用"}}]},{root:"pages/public",pages:[{path:"setting",style:{navigationBarTitleText:"系统设置"},meta:{sync:!0,title:"系统设置",group:"通用"}},{path:"richtext",style:{navigationBarTitleText:"富文本"},meta:{sync:!0,title:"富文本",group:"通用"}},{path:"faq",style:{navigationBarTitleText:"常见问题"},meta:{sync:!0,title:"常见问题",group:"通用"}},{path:"error",style:{navigationBarTitleText:"错误页面"}},{path:"webview",style:{navigationBarTitleText:""}}]},{root:"pages/coupon",pages:[{path:"list",style:{navigationBarTitleText:"领券中心"},meta:{sync:!0,title:"领券中心",group:"优惠券"}},{path:"detail",style:{navigationBarTitleText:"优惠券"},meta:{auth:!1,sync:!0,title:"优惠券详情",group:"优惠券"}}]},{root:"pages/chat",pages:[{path:"index",style:{navigationBarTitleText:"客服"},meta:{auth:!0,sync:!0,title:"客服",group:"客服"}}]},{root:"pages/pay",pages:[{path:"index",style:{navigationBarTitleText:"收银台"}},{path:"result",style:{navigationBarTitleText:"支付结果"}},{path:"recharge",style:{navigationBarTitleText:"充值余额"},meta:{auth:!0,sync:!0,title:"充值余额",group:"支付"}},{path:"recharge-log",style:{navigationBarTitleText:"充值记录"},meta:{auth:!0,sync:!0,title:"充值记录",group:"支付"}}]},{root:"pages/activity",pages:[{path:"groupon/detail",style:{navigationBarTitleText:"拼团详情"}},{path:"groupon/order",style:{navigationBarTitleText:"我的拼团",enablePullDownRefresh:!0},meta:{auth:!0,sync:!0,title:"拼团订单",group:"营销活动"}},{path:"index",style:{navigationBarTitleText:"营销商品"},meta:{sync:!0,title:"营销商品",group:"营销活动"}},{path:"groupon/list",style:{navigationBarTitleText:"拼团活动"},meta:{sync:!0,title:"拼团活动",group:"营销活动"}},{path:"seckill/list",style:{navigationBarTitleText:"秒杀活动"},meta:{sync:!0,title:"秒杀活动",group:"营销活动"}},{path:"point/list",style:{navigationBarTitleText:"积分商城"},meta:{sync:!0,title:"积分商城",group:"营销活动"}}]}],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"税眸",navigationBarBackgroundColor:"#FFFFFF",backgroundColor:"#FFFFFF",navigationStyle:"custom"},tabBar:{list:[{pagePath:"pages/index/index"},{pagePath:"pages/index/contract"},{pagePath:"pages/index/ai"},{pagePath:"pages/index/college"},{pagePath:"pages/index/user"}]}};function mb(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var yb=mb((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},o=r.lib={},i=o.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},s=o.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<o;i++){var s=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=s<<24-(r+i)%4*8}else for(i=0;i<o;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=**********<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],o=function(t){var n=987654321,r=**********;return function(){var o=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var a=o(4294967296*(n||e.random()));n=987654071*a(),r.push(4294967296*a()|0)}return new s.init(r,t)}}),a=r.enc={},c=a.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new s.init(n,t/2)}},u=a.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new s.init(n,t)}},l=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},f=o.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,i=this.blockSize,a=o/(4*i),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*i,u=e.min(4*c,o);if(c){for(var l=0;l<c;l+=i)this._doProcessBlock(r,l);var f=r.splice(0,c);n.sigBytes-=u}return new s.init(f,u)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=r.algo={};return r}(Math),n)})),_b=yb,bb=(mb((function(e,t){var n;e.exports=(n=_b,function(e){var t=n,r=t.lib,o=r.WordArray,i=r.Hasher,s=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=s.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,o=e[r];e[r]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i=this._hash.words,s=e[t+0],c=e[t+1],p=e[t+2],d=e[t+3],g=e[t+4],v=e[t+5],m=e[t+6],y=e[t+7],_=e[t+8],b=e[t+9],w=e[t+10],x=e[t+11],S=e[t+12],O=e[t+13],k=e[t+14],T=e[t+15],A=i[0],I=i[1],P=i[2],j=i[3];A=u(A,I,P,j,s,7,a[0]),j=u(j,A,I,P,c,12,a[1]),P=u(P,j,A,I,p,17,a[2]),I=u(I,P,j,A,d,22,a[3]),A=u(A,I,P,j,g,7,a[4]),j=u(j,A,I,P,v,12,a[5]),P=u(P,j,A,I,m,17,a[6]),I=u(I,P,j,A,y,22,a[7]),A=u(A,I,P,j,_,7,a[8]),j=u(j,A,I,P,b,12,a[9]),P=u(P,j,A,I,w,17,a[10]),I=u(I,P,j,A,x,22,a[11]),A=u(A,I,P,j,S,7,a[12]),j=u(j,A,I,P,O,12,a[13]),P=u(P,j,A,I,k,17,a[14]),A=l(A,I=u(I,P,j,A,T,22,a[15]),P,j,c,5,a[16]),j=l(j,A,I,P,m,9,a[17]),P=l(P,j,A,I,x,14,a[18]),I=l(I,P,j,A,s,20,a[19]),A=l(A,I,P,j,v,5,a[20]),j=l(j,A,I,P,w,9,a[21]),P=l(P,j,A,I,T,14,a[22]),I=l(I,P,j,A,g,20,a[23]),A=l(A,I,P,j,b,5,a[24]),j=l(j,A,I,P,k,9,a[25]),P=l(P,j,A,I,d,14,a[26]),I=l(I,P,j,A,_,20,a[27]),A=l(A,I,P,j,O,5,a[28]),j=l(j,A,I,P,p,9,a[29]),P=l(P,j,A,I,y,14,a[30]),A=f(A,I=l(I,P,j,A,S,20,a[31]),P,j,v,4,a[32]),j=f(j,A,I,P,_,11,a[33]),P=f(P,j,A,I,x,16,a[34]),I=f(I,P,j,A,k,23,a[35]),A=f(A,I,P,j,c,4,a[36]),j=f(j,A,I,P,g,11,a[37]),P=f(P,j,A,I,y,16,a[38]),I=f(I,P,j,A,w,23,a[39]),A=f(A,I,P,j,O,4,a[40]),j=f(j,A,I,P,s,11,a[41]),P=f(P,j,A,I,d,16,a[42]),I=f(I,P,j,A,m,23,a[43]),A=f(A,I,P,j,b,4,a[44]),j=f(j,A,I,P,S,11,a[45]),P=f(P,j,A,I,T,16,a[46]),A=h(A,I=f(I,P,j,A,p,23,a[47]),P,j,s,6,a[48]),j=h(j,A,I,P,y,10,a[49]),P=h(P,j,A,I,k,15,a[50]),I=h(I,P,j,A,v,21,a[51]),A=h(A,I,P,j,S,6,a[52]),j=h(j,A,I,P,d,10,a[53]),P=h(P,j,A,I,w,15,a[54]),I=h(I,P,j,A,c,21,a[55]),A=h(A,I,P,j,_,6,a[56]),j=h(j,A,I,P,T,10,a[57]),P=h(P,j,A,I,m,15,a[58]),I=h(I,P,j,A,O,21,a[59]),A=h(A,I,P,j,g,6,a[60]),j=h(j,A,I,P,x,10,a[61]),P=h(P,j,A,I,p,15,a[62]),I=h(I,P,j,A,b,21,a[63]),i[0]=i[0]+A|0,i[1]=i[1]+I|0,i[2]=i[2]+P|0,i[3]=i[3]+j|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var i=e.floor(r/4294967296),s=r;n[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(o+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,r,o,i,s){var a=e+(t&n|~t&r)+o+s;return(a<<i|a>>>32-i)+t}function l(e,t,n,r,o,i,s){var a=e+(t&r|n&~r)+o+s;return(a<<i|a>>>32-i)+t}function f(e,t,n,r,o,i,s){var a=e+(t^n^r)+o+s;return(a<<i|a>>>32-i)+t}function h(e,t,n,r,o,i,s){var a=e+(n^(t|~r))+o+s;return(a<<i|a>>>32-i)+t}t.MD5=i._createHelper(c),t.HmacMD5=i._createHmacHelper(c)}(Math),n.MD5)})),mb((function(e,t){var n;e.exports=(n=_b,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),s=this._iKey=t.clone(),a=i.words,c=s.words,u=0;u<n;u++)a[u]^=1549556828,c[u]^=909522486;i.sigBytes=s.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),mb((function(e,t){e.exports=_b.HmacMD5}))),wb=mb((function(e,t){e.exports=_b.enc.Utf8})),xb=mb((function(e,t){var n,r,o;e.exports=(o=(r=n=_b).lib.WordArray,r.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var s=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,a=0;a<4&&i+.75*a<n;a++)o.push(r.charAt(s>>>6*(3-a)&63));var c=r.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var i=0;i<n.length;i++)r[n.charCodeAt(i)]=i}var s=n.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return function(e,t,n){for(var r=[],i=0,s=0;s<t;s++)if(s%4){var a=n[e.charCodeAt(s-1)]<<s%4*2,c=n[e.charCodeAt(s)]>>>6-s%4*2;r[i>>>2]|=(a|c)<<24-i%4*8,i++}return o.create(r,i)}(e,t,r)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const Sb="FUNCTION",Ob="pending",kb="rejected";function Tb(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function Ab(e){return"object"===Tb(e)}function Ib(e){return"function"==typeof e}function Pb(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const jb="REJECTED",$b="NOT_PENDING";class Eb{constructor({createPromise:e,retryRule:t=jb}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case jb:return this.status===kb;case $b:return this.status!==Ob}}exec(){return this.needRetry?(this.status=Ob,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=kb,Promise.reject(e)))),this.promise):this.promise}}function Lb(e){return e&&"string"==typeof e?JSON.parse(e):e}const Cb=Lb([]),Mb="mp-weixin";Lb("");const Rb=Lb("[]")||[];let Db="";try{Db="__UNI__4C2B047"}catch(G_){}let Nb={};function Bb(e,t={}){var n,r;return n=Nb,r=e,Object.prototype.hasOwnProperty.call(n,r)||(Nb[e]=t),Nb[e]}const Ub=["invoke","success","fail","complete"],Fb=Bb("_globalUniCloudInterceptor");function qb(e,t){Fb[e]||(Fb[e]={}),Ab(t)&&Object.keys(t).forEach((n=>{Ub.indexOf(n)>-1&&function(e,t,n){let r=Fb[e][t];r||(r=Fb[e][t]=[]),-1===r.indexOf(n)&&Ib(n)&&r.push(n)}(e,n,t[n])}))}function Wb(e,t){Fb[e]||(Fb[e]={}),Ab(t)?Object.keys(t).forEach((n=>{Ub.indexOf(n)>-1&&function(e,t,n){const r=Fb[e][t];if(!r)return;const o=r.indexOf(n);o>-1&&r.splice(o,1)}(e,n,t[n])})):delete Fb[e]}function Hb(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function zb(e,t){return Fb[e]&&Fb[e][t]||[]}function Vb(e){qb("callObject",e)}const Kb=Bb("_globalUniCloudListener"),Jb="response",Yb="needLogin",Gb="refreshToken",Qb="clientdb",Zb="cloudfunction",Xb="cloudobject";function ew(e){return Kb[e]||(Kb[e]=[]),Kb[e]}function tw(e,t){const n=ew(e);n.includes(t)||n.push(t)}function nw(e,t){const n=ew(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function rw(e,t){const n=ew(e);for(let r=0;r<n.length;r++)(0,n[r])(t)}let ow,iw=!1;function sw(){return ow||(ow=new Promise((e=>{iw&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(iw=!0,e())}iw||setTimeout((()=>{t()}),30)}()})),ow)}function aw(e){const t={};for(const n in e){const r=e[n];Ib(r)&&(t[n]=Pb(r))}return t}class cw extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var uw={request:e=>en.request(e),uploadFile:e=>en.uploadFile(e),setStorageSync:(e,t)=>en.setStorageSync(e,t),getStorageSync:e=>en.getStorageSync(e),removeStorageSync:e=>en.removeStorageSync(e),clearStorageSync:()=>en.clearStorageSync(),connectSocket:e=>en.connectSocket(e)};function lw(e){return e&&lw(e.__v_raw)||e}function fw(){return{token:uw.getStorageSync("uni_id_token")||uw.getStorageSync("uniIdToken"),tokenExpired:uw.getStorageSync("uni_id_token_expired")}}function hw({token:e,tokenExpired:t}={}){e&&uw.setStorageSync("uni_id_token",e),t&&uw.setStorageSync("uni_id_token_expired",t)}let pw,dw;function gw(){return pw||(pw=en.getSystemInfoSync()),pw}function vw(){let e,t;try{if(en.getLaunchOptionsSync){if(en.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:r}=en.getLaunchOptionsSync();e=r,t=n}}catch(n){}return{channel:e,scene:t}}let mw={};function yw(){const e=en.getLocale&&en.getLocale()||"en";if(dw)return{...mw,...dw,locale:e,LOCALE:e};const t=gw(),{deviceId:n,osName:r,uniPlatform:o,appId:i}=t,s=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const a in t)Object.hasOwnProperty.call(t,a)&&-1===s.indexOf(a)&&delete t[a];return dw={PLATFORM:o,OS:r,APPID:i,DEVICEID:n,...vw(),...t},{...mw,...dw,locale:e,LOCALE:e}}var _w=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),bb(n,t).toString()},bw=function(e,t){return new Promise(((n,r)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",o=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new cw({code:n,message:o,requestId:t}))}const o=e.data;if(o.error)return r(new cw({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},ww=function(e){return xb.stringify(wb.parse(e))},xw={init(e){const t=new class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=uw,this._getAccessTokenPromiseHub=new Eb({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new cw({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:$b})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return bw(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=_w(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=_w(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:r,fileType:o,onUploadProgress:i}){return new Promise(((s,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:r,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?s(e):a(new cw({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new cw({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:r=!1,onUploadProgress:o,config:i}){if("string"!==Tb(t))throw new cw({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new cw({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new cw({code:"INVALID_PARAM",message:"cloudPath不合法"});const s=i&&i.envType||this.config.envType;if(r&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new cw({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:s,filename:r?t.split("/").pop():t,fileId:r?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:u,accessKeyId:l,signature:f,host:h,ossPath:p,id:d,policy:g,ossCallbackUrl:v}=a,m={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:l,Signature:f,host:h,id:d,key:p,policy:g,success_action_status:200};if(u&&(m["x-oss-security-token"]=u),v){const e=JSON.stringify({callbackUrl:v,callbackBody:JSON.stringify({fileId:d,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});m.callback=ww(e)}const y={url:"https://"+a.host,formData:m,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},y,{onUploadProgress:o})),v)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:d})).success)return{success:!0,filePath:e,fileID:c};throw new cw({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new cw({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new cw({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}}(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const Sw="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Ow;!function(e){e.local="local",e.none="none",e.session="session"}(Ow||(Ow={}));var kw=function(){},Tw=mb((function(e,t){var n;e.exports=(n=_b,function(e){var t=n,r=t.lib,o=r.WordArray,i=r.Hasher,s=t.algo,a=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,o=0;o<64;)t(r)&&(o<8&&(a[o]=n(e.pow(r,.5))),c[o]=n(e.pow(r,1/3)),o++),r++}();var u=[],l=s.SHA256=i.extend({_doReset:function(){this._hash=new o.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],s=n[3],a=n[4],l=n[5],f=n[6],h=n[7],p=0;p<64;p++){if(p<16)u[p]=0|e[t+p];else{var d=u[p-15],g=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,v=u[p-2],m=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;u[p]=g+u[p-7]+m+u[p-16]}var y=r&o^r&i^o&i,_=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),b=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&l^~a&f)+c[p]+u[p];h=f,f=l,l=a,a=s+b|0,s=i,i=o,o=r,r=b+(_+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+a|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(r/4294967296),n[15+(o+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Aw=Tw,Iw=mb((function(e,t){e.exports=_b.HmacSHA256}));const Pw=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new cw({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,r)=>e?n(e):t(r)}));return e.promise=t,e};function jw(e){return void 0===e}function $w(e){return"[object Null]"===Object.prototype.toString.call(e)}function Ew(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Lw(e=32){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=t.length;let r="";for(let o=0;o<e;o++)r+=t.charAt(Math.floor(Math.random()*n));return r}var Cw;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Cw||(Cw={}));const Mw={adapter:null,runtime:void 0},Rw=["anonymousUuidKey"];class Dw extends kw{constructor(){super(),Mw.adapter.root.tcbObject||(Mw.adapter.root.tcbObject={})}setItem(e,t){Mw.adapter.root.tcbObject[e]=t}getItem(e){return Mw.adapter.root.tcbObject[e]}removeItem(e){delete Mw.adapter.root.tcbObject[e]}clear(){delete Mw.adapter.root.tcbObject}}function Nw(e,t){switch(e){case"local":return t.localStorage||new Dw;case"none":return new Dw;default:return t.sessionStorage||new Dw}}class Bw{constructor(e){if(!this._storage){this._persistence=Mw.adapter.primaryStorage||e.persistence,this._storage=Nw(this._persistence,Mw.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,r=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,i=`login_type_${e.env}`,s="device_id",a=`token_type_${e.env}`,c=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:r,anonymousUuidKey:o,loginTypeKey:i,userInfoKey:c,deviceIdKey:s,tokenTypeKey:a}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=Nw(e,Mw.adapter);for(const r in this.keys){const e=this.keys[r];if(t&&Rw.includes(r))continue;const o=this._storage.getItem(e);jw(o)||$w(o)||(n.setItem(e,o),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const r={version:n||"localCachev1",content:t},o=JSON.stringify(r);try{this._storage.setItem(e,o)}catch(i){throw i}}getStore(e,t){try{if(!this._storage)return}catch(r){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const Uw={},Fw={};function qw(e){return Uw[e]}class Ww{constructor(e,t){this.data=t||null,this.name=e}}class Hw extends Ww{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const zw=new class{constructor(){this._listeners={}}on(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}off(e,t){return function(e,t,n){if(n&&n[e]){const r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Hw)return console.error(e.error),this;const n="string"==typeof e?new Ww(e,t||{}):e,r=n.name;if(this._listens(r)){n.target=this;const e=this._listeners[r]?[...this._listeners[r]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Vw(e,t){zw.on(e,t)}function Kw(e,t={}){zw.fire(e,t)}function Jw(e,t){zw.off(e,t)}const Yw="loginStateChanged",Gw="loginStateExpire",Qw="loginTypeChanged",Zw="anonymousConverted",Xw="refreshAccessToken";var ex;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(ex||(ex={}));class tx{constructor(){this._fnPromiseMap=new Map}async run(e,t){let n=this._fnPromiseMap.get(e);return n||(n=new Promise((async(n,r)=>{try{await this._runIdlePromise();const r=t();n(await r)}catch(o){r(o)}finally{this._fnPromiseMap.delete(e)}})),this._fnPromiseMap.set(e,n)),n}_runIdlePromise(){return Promise.resolve()}}class nx{constructor(e){this._singlePromise=new tx,this._cache=qw(e.env),this._baseURL=`https://${e.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new Mw.adapter.reqClass({timeout:e.timeout,timeoutMsg:`请求在${e.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:e}=this._cache.keys;let t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Lw(),this._cache.setStore(e,t)),this._deviceID=t,t}async _request(e,t,n={}){const r={"x-request-id":Lw(),"x-device-id":this._getDeviceId()};if(n.withAccessToken){const{tokenTypeKey:e}=this._cache.keys,t=await this.getAccessToken(),n=this._cache.getStore(e);r.authorization=`${n} ${t}`}return this._reqClass["get"===n.method?"get":"post"]({url:`${this._baseURL}${e}`,data:t,headers:r})}async _fetchAccessToken(){const{loginTypeKey:e,accessTokenKey:t,accessTokenExpireKey:n,tokenTypeKey:r}=this._cache.keys,o=this._cache.getStore(e);if(o&&o!==ex.ANONYMOUS)throw new cw({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const i=await this._singlePromise.run("fetchAccessToken",(async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data)),{access_token:s,expires_in:a,token_type:c}=i;return this._cache.setStore(r,c),this._cache.setStore(t,s),this._cache.setStore(n,Date.now()+1e3*a),s}isAccessTokenExpired(e,t){let n=!0;return e&&t&&(n=t<Date.now()),n}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),r=this._cache.getStore(t);return this.isAccessTokenExpired(n,r)?this._fetchAccessToken():n}async refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,loginTypeKey:n}=this._cache.keys;return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.setStore(n,ex.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",(async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data))}}const rx=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],ox={"X-SDK-Version":"1.3.5"};function ix(e,t,n){const r=e[t];e[t]=function(t){const o={},i={};n.forEach((n=>{const{data:r,headers:s}=n.call(e,t);Object.assign(o,r),Object.assign(i,s)}));const s=t.data;return s&&(()=>{var e;if(e=s,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...s,...o};else for(const t in o)s.append(t,o[t])})(),t.headers={...t.headers||{},...i},r.call(e,t)}}function sx(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...ox,"x-seqid":e}}}class ax{constructor(e={}){var t;this.config=e,this._reqClass=new Mw.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=qw(this.config.env),this._localCache=(t=this.config.env,Fw[t]),this.oauth=new nx(this.config),ix(this._reqClass,"post",[sx]),ix(this._reqClass,"upload",[sx]),ix(this._reqClass,"download",[sx])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:r,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let i=this._cache.getStore(n);if(!i)throw new cw({message:"未登录CloudBase"});const s={refresh_token:i},a=await this.request("auth.fetchAccessTokenWithRefreshToken",s);if(a.data.code){const{code:e}=a.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(r)===ex.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(n),r=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(r.refresh_token),this._refreshAccessToken()}Kw(Gw),this._cache.removeStore(n)}throw new cw({code:a.data.code,message:`刷新access token失败：${a.data.code}`})}if(a.data.access_token)return Kw(Xw),this._cache.setStore(e,a.data.access_token),this._cache.setStore(t,a.data.access_token_expire+Date.now()),{accessToken:a.data.access_token,accessTokenExpire:a.data.access_token_expire};a.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,a.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new cw({message:"refresh token不存在，登录状态异常"});let r=this._cache.getStore(e),o=this._cache.getStore(t),i=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(r,o))&&(i=!1),(!r||!o||o<Date.now())&&i?this.refreshAccessToken():{accessToken:r,accessTokenExpire:o}}async request(e,t,n){const r=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const i={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};let s;if(-1===rx.indexOf(e)&&(this._cache.keys,i.access_token=await this.oauth.getAccessToken()),"storage.uploadFile"===e){s=new FormData;for(let e in s)s.hasOwnProperty(e)&&void 0!==s[e]&&s.append(e,i[e]);o="multipart/form-data"}else{o="application/json",s={};for(let e in i)void 0!==i[e]&&(s[e]=i[e])}let a={headers:{"content-type":o}};n&&n.timeout&&(a.timeout=n.timeout),n&&n.onUploadProgress&&(a.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(r);c&&(a.headers["X-TCB-Trace"]=c);const{parse:u,inQuery:l,search:f}=t;let h={env:this.config.env};u&&(h.parse=!0),l&&(h={...l,...h});let p=function(e,t,n={}){const r=/\?/.test(t);let o="";for(let i in n)""===o?!r&&(t+="?"):o+="&",o+=`${i}=${encodeURIComponent(n[i])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(Sw,"//tcb-api.tencentcloudapi.com/web",h);f&&(p+=f);const d=await this.post({url:p,data:s,...a}),g=d.header&&d.header["x-tcb-trace"];if(g&&this._localCache.setStore(r,g),200!==Number(d.status)&&200!==Number(d.statusCode)||!d.data)throw new cw({code:"NETWORK_ERROR",message:"network request error"});return d}async send(e,t={},n={}){const r=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(("ACCESS_TOKEN_DISABLED"===r.data.code||"ACCESS_TOKEN_EXPIRED"===r.data.code)&&-1===rx.indexOf(e)){await this.oauth.refreshAccessToken();const r=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(r.data.code)throw new cw({code:r.data.code,message:Ew(r.data.message)});return r.data}if(r.data.code)throw new cw({code:r.data.code,message:Ew(r.data.message)});return r.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:r}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(r,e)}}const cx={};function ux(e){return cx[e]}class lx{constructor(e){this.config=e,this._cache=qw(e.env),this._request=ux(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:r}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(r,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:r}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(r,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class fx{constructor(e){if(!e)throw new cw({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=qw(this._envId),this._request=ux(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new cw({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new cw({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:r,province:o,country:i,city:s}=e,{data:a}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:r,province:o,country:i,city:s});this.setLocalUserInfo(a)}async refresh(){const e=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class hx{constructor(e){if(!e)throw new cw({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=qw(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:r}=this._cache.keys,o=this._cache.getStore(t),i=this._cache.getStore(n),s=this._cache.getStore(r);this.credential={refreshToken:o,accessToken:i,accessTokenExpire:s},this.user=new fx(e)}get isAnonymousAuth(){return this.loginType===ex.ANONYMOUS}get isCustomAuth(){return this.loginType===ex.CUSTOM}get isWeixinAuth(){return this.loginType===ex.WECHAT||this.loginType===ex.WECHAT_OPEN||this.loginType===ex.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class px extends lx{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),Kw(Yw),Kw(Qw,{env:this.config.env,loginType:ex.ANONYMOUS,persistence:"local"});const e=new hx(this.config.env);return await e.user.refresh(),e}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,r=this._cache.getStore(t),o=this._cache.getStore(n),i=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:r,refresh_token:o,ticket:e});if(i.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),Kw(Zw,{env:this.config.env}),Kw(Qw,{loginType:ex.CUSTOM,persistence:"local"}),{credential:{refreshToken:i.refresh_token}};throw new cw({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,ex.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class dx extends lx{async signIn(e){if("string"!=typeof e)throw new cw({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Kw(Yw),Kw(Qw,{env:this.config.env,loginType:ex.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new hx(this.config.env);throw new cw({message:"自定义登录失败"})}}class gx extends lx{async signIn(e,t){if("string"!=typeof e)throw new cw({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,r=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token:i,access_token_expire:s}=r;if(o)return this.setRefreshToken(o),i&&s?this.setAccessToken(i,s):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Kw(Yw),Kw(Qw,{env:this.config.env,loginType:ex.EMAIL,persistence:this.config.persistence}),new hx(this.config.env);throw r.code?new cw({code:r.code,message:`邮箱登录失败: ${r.message}`}):new cw({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class vx extends lx{async signIn(e,t){if("string"!=typeof e)throw new cw({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,r=await this._request.send("auth.signIn",{loginType:ex.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:o,access_token_expire:i,access_token:s}=r;if(o)return this.setRefreshToken(o),s&&i?this.setAccessToken(s,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Kw(Yw),Kw(Qw,{env:this.config.env,loginType:ex.USERNAME,persistence:this.config.persistence}),new hx(this.config.env);throw r.code?new cw({code:r.code,message:`用户名密码登录失败: ${r.message}`}):new cw({message:"用户名密码登录失败"})}}class mx{constructor(e){this.config=e,this._cache=qw(e.env),this._request=ux(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Vw(Qw,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new px(this.config)}customAuthProvider(){return new dx(this.config)}emailAuthProvider(){return new gx(this.config)}usernameAuthProvider(){return new vx(this.config)}async signInAnonymously(){return new px(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new gx(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new vx(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new px(this.config)),Vw(Zw,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===ex.ANONYMOUS)throw new cw({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,r=this._cache.getStore(e);if(!r)return;const o=await this._request.send("auth.logout",{refresh_token:r});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),Kw(Yw),Kw(Qw,{env:this.config.env,loginType:ex.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){Vw(Yw,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){Vw(Gw,e.bind(this))}onAccessTokenRefreshed(e){Vw(Xw,e.bind(this))}onAnonymousConverted(e){Vw(Zw,e.bind(this))}onLoginTypeChanged(e){Vw(Qw,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:e,accessTokenExpireKey:t}=this._cache.keys,n=this._cache.getStore(e),r=this._cache.getStore(t);return this._request.oauth.isAccessTokenExpired(n,r)?null:new hx(this.config.env)}async isUsernameRegistered(e){if("string"!=typeof e)throw new cw({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new dx(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:r}=e.data;r===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const yx=function(e,t){t=t||Pw();const n=ux(this.config.env),{cloudPath:r,filePath:o,onUploadProgress:i,fileType:s="image"}=e;return n.send("storage.getUploadMetadata",{path:r}).then((e=>{const{data:{url:a,authorization:c,token:u,fileId:l,cosFileId:f},requestId:h}=e,p={key:r,signature:c,"x-cos-meta-fileid":f,success_action_status:"201","x-cos-security-token":u};n.upload({url:a,data:p,file:o,name:r,fileType:s,onUploadProgress:i}).then((e=>{201===e.statusCode?t(null,{fileID:l,requestId:h}):t(new cw({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},_x=function(e,t){t=t||Pw();const n=ux(this.config.env),{cloudPath:r}=e;return n.send("storage.getUploadMetadata",{path:r}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},bx=function({fileList:e},t){if(t=t||Pw(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let r of e)if(!r||"string"!=typeof r)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return ux(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},xx=function({fileList:e},t){t=t||Pw(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?n.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const r={file_list:n};return ux(this.config.env).send("storage.batchGetDownloadUrl",r).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Sx=async function({fileID:e},t){const n=(await xx.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const r=ux(this.config.env);let o=n.download_url;if(o=encodeURI(o),!t)return r.download({url:o});t(await r.download({url:o}))},Ox=function({name:e,data:t,query:n,parse:r,search:o,timeout:i},s){const a=s||Pw();let c;try{c=t?JSON.stringify(t):""}catch(l){return Promise.reject(l)}if(!e)return Promise.reject(new cw({code:"PARAM_ERROR",message:"函数名不能为空"}));const u={inQuery:n,parse:r,search:o,function_name:e,request_data:c};return ux(this.config.env).send("functions.invokeFunction",u,{timeout:i}).then((e=>{if(e.code)a(null,e);else{let n=e.data.response_data;if(r)a(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),a(null,{result:n,requestId:e.requestId})}catch(t){a(new cw({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},kx={timeout:15e3,persistence:"session"},Tx={};class Ax{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(Mw.adapter||(this.requestClient=new Mw.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...kx,...e},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Ax(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||Mw.adapter.primaryStorage||kx.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Uw[t]=new Bw(e),Fw[t]=new Bw({...e,persistence:"local"})}(this.config),n=this.config,cx[n.env]=new ax(n),this.authObj=new mx(this.config),this.authObj}on(e,t){return Vw.apply(this,[e,t])}off(e,t){return Jw.apply(this,[e,t])}callFunction(e,t){return Ox.apply(this,[e,t])}deleteFile(e,t){return bx.apply(this,[e,t])}getTempFileURL(e,t){return xx.apply(this,[e,t])}downloadFile(e,t){return Sx.apply(this,[e,t])}uploadFile(e,t){return yx.apply(this,[e,t])}getUploadMetadata(e,t){return _x.apply(this,[e,t])}registerExtension(e){Tx[e.name]=e}async invokeExtension(e,t){const n=Tx[e];if(!n)throw new cw({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const r of t){const{isMatch:e,genAdapter:t,runtime:n}=r;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&(Mw.adapter=t),n&&(Mw.runtime=n)}}var Ix=new Ax;function Px(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),o="";for(var i in n)""===o?!r&&(t+="?"):o+="&",o+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class jx{get(e){const{url:t,data:n,headers:r,timeout:o}=e;return new Promise(((e,i)=>{uw.request({url:Px("https:",t),data:n,method:"GET",header:r,timeout:o,success(t){e(t)},fail(e){i(e)}})}))}post(e){const{url:t,data:n,headers:r,timeout:o}=e;return new Promise(((e,i)=>{uw.request({url:Px("https:",t),data:n,method:"POST",header:r,timeout:o,success(t){e(t)},fail(e){i(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:r,file:o,data:i,headers:s,fileType:a}=e,c=uw.uploadFile({url:Px("https:",r),name:"file",formData:Object.assign({},i),filePath:o,fileType:a,header:s,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const $x={setItem(e,t){uw.setStorageSync(e,t)},getItem:e=>uw.getStorageSync(e),removeItem(e){uw.removeStorageSync(e)},clear(){uw.clearStorageSync()}};var Ex={genAdapter:function(){return{root:{},reqClass:jx,localStorage:$x,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Ix.useAdapters(Ex);const Lx=Ix,Cx=Lx.init;Lx.init=function(e){e.env=e.spaceId;const t=Cx.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:r,complete:o}=aw(e);if(!(t||r||o))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{r&&r(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Mx=Lx;async function Rx(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(r={url:n,timeout:500},new Promise(((e,t)=>{uw.request({...r,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(o){return!1}var r}const Dx={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var Nx={init(e){const t=new class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=uw}async request(e,t=!0){return e=this.setupRequest(e),Promise.resolve().then((()=>bw(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",r=e.data&&e.data.message||"request:fail";return n(new cw({code:t,message:r}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=_w(t,this.config.clientSecret);const r=yw();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));const{token:o}=fw();return n["x-client-token"]=o,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=yw(),{token:n}=fw(),r=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:o,servePort:i}=this.__dev__&&this.__dev__.debugInfo||{},{address:s}=await async function(e,t){let n;for(let r=0;r<e.length;r++){const o=e[r];if(await Rx(o,t)){n=o;break}}return{address:n,port:t}}(o,i);return{url:`http://${s}:${i}/${Dx[e.method]}`,method:"POST",data:r,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:r}){if(!t)throw new cw({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:i,formData:s,name:a}=t.result;return o=t.result.fileUrl,new Promise(((t,o)=>{const c=this.adapter.uploadFile({url:i,formData:s,name:a,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):o(new cw({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new cw({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,r)=>{t.success?n({success:!0,filePath:e,fileID:o}):r(new cw({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new cw({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new cw({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new cw({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Bx=mb((function(e,t){e.exports=_b.enc.Hex}));function Ux(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Fx(e="",t={}){const{data:n,functionName:r,method:o,headers:i,signHeaderKeys:s=[],config:a}=t,c=String(Date.now()),u=Ux(),l=Object.assign({},i,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":r,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":u,"x-alipay-callid":u,"x-trace-id":u}),f=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(s),[h="",p=""]=e.split("?")||[],d=function(e){const t=e.signedHeaders.join(";"),n=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),r=Aw(e.body).toString(Bx),o=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${n}\n${t}\n${r}\n`,i=Aw(o).toString(Bx),s=`HMAC-SHA256\n${e.timestamp}\n${i}\n`,a=Iw(s,e.secretKey).toString(Bx);return`HMAC-SHA256 Credential=${e.secretId}, SignedHeaders=${t}, Signature=${a}`}({path:h,query:p,method:o,headers:l,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:f.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},l,{Authorization:d})}}function qx({url:e,data:t,method:n="POST",headers:r={},timeout:o}){return new Promise(((i,s)=>{uw.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:r,dataType:"json",timeout:o,complete:(e={})=>{const t=r["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:r,trace_id:o}=e.data||{};return s(new cw({code:"SYS_ERR",message:n||r||"request:fail",requestId:o||t}))}i({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Wx(e,t){const{path:n,data:r,method:o="GET"}=e,{url:i,headers:s}=Fx(n,{functionName:"",data:r,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return qx({url:i,data:r,method:o,headers:s}).then((e=>{const t=e.data||{};if(!t.success)throw new cw({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new cw({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Hx(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new cw({code:"INVALID_PARAM",message:"fileID不合法"});const r=t.substring(0,n),o=t.substring(n+1);return r!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function zx(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class Vx{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),o=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Ux(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return o[e]?"".concat(e,"=").concat(o[e]):null})).filter(Boolean).join("&"),`host:${r}`].join("\n"),s=["HMAC-SHA256",Aw(i).toString(Bx)].join("\n"),a=Iw(s,this.config.secretKey).toString(Bx),c=Object.keys(o).map((e=>`${e}=${encodeURIComponent(o[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${a}`}}var Kx={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Vx(this.config)}callFunction(e){return function(e,t){const{name:n,data:r,async:o=!1,timeout:i}=e,s="POST",a={"x-to-function-name":n};o&&(a["x-function-invoke-type"]="async");const{url:c,headers:u}=Fx("/functions/invokeFunction",{functionName:n,data:r,method:s,headers:a,signHeaderKeys:["x-to-function-name"],config:t});return qx({url:c,data:r,method:s,headers:u,timeout:i}).then((e=>{let t=0;if(o){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new cw({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new cw({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:r,onUploadProgress:o}){return new Promise(((i,s)=>{const a=uw.uploadFile({url:e,filePath:t,fileType:n,formData:r,name:"file",success(e){e&&e.statusCode<400?i(e):s(new cw({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){s(new cw({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:r}){if("string"!==Tb(t))throw new cw({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new cw({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new cw({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Wx({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:i,upload_url:s,form_data:a}=o,c=a&&a.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:s,filePath:e,fileType:n,formData:c,onUploadProgress:r}).then((()=>({fileID:i})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const r=[];for(const i of e){let e;"string"!==Tb(i)&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{e=Hx.call(this,i)}catch(o){console.warn(o.errCode,o.errMsg),e=i}r.push({file_id:e,expire:600})}Wx({path:"/?download_url",data:{file_list:r},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:zx.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return uw.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Jx({data:e}){let t;t=yw();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=fw();e&&(n.uniIdToken=e)}return n}const Yx=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var Gx=/[\\^$.*+?()[\]{}|]/g,Qx=RegExp(Gx.source);function Zx(e,t,n){return e.replace(new RegExp((r=t)&&Qx.test(r)?r.replace(Gx,"\\$&"):r,"g"),n);var r}const Xx=2e4,eS={code:20101,message:"Invalid client"};function tS(e){const{errSubject:t,subject:n,errCode:r,errMsg:o,code:i,message:s,cause:a}=e||{};return new cw({subject:t||n||"uni-secure-network",code:r||i||Xx,message:o||s,cause:a})}let nS;function rS({secretType:e}={}){return"request"===e||"response"===e||"both"===e}function oS({functionName:e,result:t,logPvd:n}){}function iS(e){const t=e.callFunction,n=function(n){const r=n.name;n.data=Jx.call(e,{data:n.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],i=rS(n)||false;return t.call(this,n).then((e=>(e.errCode=0,!i&&oS.call(this,{functionName:r,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!i&&oS.call(this,{functionName:r,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let r=0;r<n.length;r++){const{rule:o,content:i,mode:s}=n[r],a=e.match(o);if(!a)continue;let c=i;for(let e=1;e<a.length;e++)c=Zx(c,`{$${e}}`,a[e]);for(const e in t)c=Zx(c,`{${e}}`,t[e]);return"replace"===s?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:Yx,extraInfo:{functionName:r}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:r,spaceId:o}=e.config,i=t.name;let s,a;return t.data=t.data||{},s=n,s=s.bind(e),a=function({name:e,data:t={}}){return"uni-id-co"===e&&"secureNetworkHandshakeByWeixin"===t.method}(t)?s.call(e,t):rS(t)?new nS({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:r,uniPlatform:o,osName:i}=gw();let s=o;"app"===o&&(s=i);const a=function({provider:e,spaceId:t}={}){const n=Cb;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const r=n.find((n=>n.provider===e&&n.spaceId===t));return r&&r.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},u=Object.keys(c);if(0===u.length)return!0;const l=function(e,t){let n,r,o;for(let i=0;i<e.length;i++){const s=e[i];s!==t?"*"!==s?s.split(",").map((e=>e.trim())).indexOf(t)>-1&&(r=s):o=s:n=s}return n||r||o}(u,n);if(!l)return!1;if((c[l]||[]).find(((e={})=>e.appId===r&&(e.platform||"").toLowerCase()===s.toLowerCase())))return!0;throw console.error(`此应用[appId: ${r}, platform: ${s}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),tS(eS)}({provider:r,spaceId:o,functionName:i})?new nS({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):s(t),Object.defineProperty(a,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),a.then((e=>("undefined"!=typeof UTSJSONObject&&"undefined"!=typeof UTS&&(e.result=UTS.JSON.parse(JSON.stringify(e.result))),e)))}}nS=class{constructor(){throw tS({message:`Platform ${Mb} is not enabled, please check whether secure network module is enabled in your manifest.json`})}};const sS=Symbol("CLIENT_DB_INTERNAL");function aS(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=sS,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,r){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,r)}})}function cS(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}const uS=["db.Geo","db.command","command.aggregate"];function lS(e,t){return uS.indexOf(`${e}.${t}`)>-1}function fS(e){switch(Tb(e=lw(e))){case"array":return e.map((e=>fS(e)));case"object":return e._internalType===sS||Object.keys(e).forEach((t=>{e[t]=fS(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function hS(e){return e&&e.content&&e.content.$method}class pS{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:fS(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=hS(e),n=hS(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===hS(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=hS(e),n=hS(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return dS({$method:e,$param:fS(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:fS(t)}),this._database._callCloudFunction({action:n,command:r})}}function dS(e,t,n){return aS(new pS(e,t,n),{get(e,t){let r="db";return e&&e.content&&(r=e.content.$method),lS(r,t)?dS({$method:t},e,n):function(){return dS({$method:t,$param:fS(Array.from(arguments))},e,n)}}})}function gS({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function vS(e,t={}){return aS(new e(t),{get:(e,t)=>lS("db",t)?dS({$method:t},null,e):function(){return dS({$method:t,$param:fS(Array.from(arguments))},null,e)}})}class mS extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=Bb("_globalUniCloudDatabaseCallback")),t||(this.auth=cS(this._authCallBacks)),this._isJQL=t,Object.assign(this,cS(this._dbCallBacks)),this.env=aS({},{get:(e,t)=>({$env:t})}),this.Geo=aS({},{get:(e,t)=>gS({path:["Geo"],method:t})}),this.serverDate=gS({path:[],method:"serverDate"}),this.RegExp=gS({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:r}){function o(e,t){if(n&&r)for(let n=0;n<r.length;n++){const o=r[n];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[n]))}}const i=this,s=this._isJQL?"databaseForJQL":"database";function a(e){return i._callback("error",[e]),Hb(zb(s,"fail"),e).then((()=>Hb(zb(s,"complete"),e))).then((()=>(o(null,e),rw(Jb,{type:Qb,content:e}),Promise.reject(e))))}const c=Hb(zb(s,"invoke")),u=this._uniClient;return c.then((()=>u.callFunction({name:"DCloud-clientDB",type:"CLIENT_DB",data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:r,tokenExpired:c,systemInfo:u=[]}=e.result;if(u)for(let o=0;o<u.length;o++){const{level:e,message:t,detail:n}=u[o],r=console[e]||console.log;let i="[System Info]"+t;n&&(i=`${i}\n详细信息：${n}`),r(i)}if(t)return a(new cw({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,r&&c&&(hw({token:r,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:r,tokenExpired:c}]),this._callback("refreshToken",[{token:r,tokenExpired:c}]),rw(Gb,{token:r,tokenExpired:c}));const l=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<l.length;o++){const{prop:t,tips:n}=l[o];if(t in e.result){const r=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),r)})}}return f=e,Hb(zb(s,"success"),f).then((()=>Hb(zb(s,"complete"),f))).then((()=>{o(f,null);const e=i._parseResult(f);return rw(Jb,{type:Qb,content:e}),Promise.resolve(e)}));var f}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),a(new cw({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const yS="token无效，跳转登录页面",_S="token过期，跳转登录页面",bS={TOKEN_INVALID_TOKEN_EXPIRED:_S,TOKEN_INVALID_INVALID_CLIENTID:yS,TOKEN_INVALID:yS,TOKEN_INVALID_WRONG_TOKEN:yS,TOKEN_INVALID_ANONYMOUS_USER:yS},wS={"uni-id-token-expired":_S,"uni-id-check-token-failed":yS,"uni-id-token-not-exist":yS,"uni-id-check-device-feature-failed":yS};function xS(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function SS(e=[],t=""){const n=[],r=[];return e.forEach((e=>{!0===e.needLogin?n.push(xS(t,e.path)):!1===e.needLogin&&r.push(xS(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function OS(e){return e.split("?")[0].replace(/^\//,"")}function kS(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function TS(){return OS(kS())}function AS(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,r=OS(e);return n.some((e=>e.pagePath===r))}const IS=!!vb.uniIdRouter,{loginPage:PS,routerNeedLogin:jS,resToLogin:$S,needLoginPage:ES,notNeedLoginPage:LS,loginPageInTabBar:CS}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:r={}}=vb){const{loginPage:o,needLogin:i=[],resToLogin:s=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=SS(e),{needLoginPage:u,notNeedLoginPage:l}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:r,pages:o=[]}=e,{needLoginPage:i,notNeedLoginPage:s}=SS(o,r);t.push(...i),n.push(...s)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:o,routerNeedLogin:i,resToLogin:s,needLoginPage:[...a,...u],notNeedLoginPage:[...c,...l],loginPageInTabBar:AS(o,r)}}();if(ES.indexOf(PS)>-1)throw new Error(`Login page [${PS}] should not be "needLogin", please check your pages.json`);function MS(e){const t=TS();if("/"===e.charAt(0))return e;const[n,r]=e.split("?"),o=n.replace(/^\//,"").split("/"),i=t.split("/");i.pop();for(let s=0;s<o.length;s++){const e=o[s];".."===e?i.pop():"."!==e&&i.push(e)}return""===i[0]&&i.shift(),"/"+i.join("/")+(r?"?"+r:"")}function RS({redirect:e}){const t=OS(e),n=OS(PS);return TS()!==n&&t!==n}function DS({api:e,redirect:t}={}){if(!t||!RS({redirect:t}))return;const n=(o=t,"/"!==(r=PS).charAt(0)&&(r="/"+r),o?r.indexOf("?")>-1?r+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:r+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:r);var r,o;CS?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const i={navigateTo:en.navigateTo,redirectTo:en.redirectTo,switchTab:en.switchTab,reLaunch:en.reLaunch};setTimeout((()=>{i[e]({url:n})}),0)}function NS({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=fw();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:wS[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:wS[e]}}return n}();if(function(e){const t=OS(MS(e));return!(LS.indexOf(t)>-1)&&(ES.indexOf(t)>-1||jS.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,ew(Yb).length>0)return setTimeout((()=>{rw(Yb,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function BS(){!function(){const e=kS(),{abortLoginPageJump:t,autoToLoginPage:n}=NS({url:e});t||n&&DS({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];en.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:r}=NS({url:e.url});return t?e:r?(DS({api:n,redirect:MS(e.url)}),!1):e}})}}function US(){this.onResponse((e=>{const{type:t,content:n}=e;let r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in wS}(n);break;case"clientdb":r=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in bS}(n)}r&&function(e={}){const t=ew(Yb);sw().then((()=>{const n=kS();if(n&&RS({redirect:n}))return t.length>0?rw(Yb,Object.assign({uniIdRedirectUrl:n},e)):void(PS&&DS({api:"navigateTo",redirect:n}))}))}(n)}))}function FS(e){var t;(t=e).onResponse=function(e){tw(Jb,e)},t.offResponse=function(e){nw(Jb,e)},function(e){e.onNeedLogin=function(e){tw(Yb,e)},e.offNeedLogin=function(e){nw(Yb,e)},IS&&(Bb("_globalUniCloudStatus").needLoginInit||(Bb("_globalUniCloudStatus").needLoginInit=!0,sw().then((()=>{BS.call(e)})),$S&&US.call(e)))}(e),function(e){e.onRefreshToken=function(e){tw(Gb,e)},e.offRefreshToken=function(e){nw(Gb,e)}}(e)}let qS;const WS="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",HS=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function zS(){const e=fw().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((r=t[1],decodeURIComponent(qS(r).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var r;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}qS="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!HS.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,o="",i=0;i<e.length;)t=WS.indexOf(e.charAt(i++))<<18|WS.indexOf(e.charAt(i++))<<12|(n=WS.indexOf(e.charAt(i++)))<<6|(r=WS.indexOf(e.charAt(i++))),o+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var VS=mb((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",r="chooseAndUploadFile:fail";function o(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function i(e,t,{onChooseFile:r,onUploadProgress:o}){return t.then((e=>{if(r){const t=r(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,r=5,o){(t=Object.assign({},t)).errMsg=n;const i=t.tempFiles,s=i.length;let a=0;return new Promise((n=>{for(;a<r;)c();function c(){const r=a++;if(r>=s)return void(!i.find((e=>!e.url&&!e.errMsg))&&n(t));const u=i[r];e.uploadFile({provider:u.provider,filePath:u.path,cloudPath:u.cloudPath,fileType:u.fileType,cloudPathAsRealPath:u.cloudPathAsRealPath,onUploadProgress(e){e.index=r,e.tempFile=u,e.tempFilePath=u.path,o&&o(e)}}).then((e=>{u.url=e.fileID,r<s&&c()})).catch((e=>{u.errMsg=e.errMsg||e.message,r<s&&c()}))}}))}(e,t,5,o)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?i(e,function(e){const{count:t,sizeType:n,sourceType:i=["album","camera"],extension:s}=e;return new Promise(((e,a)=>{en.chooseImage({count:t,sizeType:n,sourceType:i,extension:s,success(t){e(o(t,"image"))},fail(e){a({errMsg:e.errMsg.replace("chooseImage:fail",r)})}})}))}(t),t):"video"===t.type?i(e,function(e){const{camera:t,compressed:n,maxDuration:i,sourceType:s=["album","camera"],extension:a}=e;return new Promise(((e,c)=>{en.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:s,extension:a,success(t){const{tempFilePath:n,duration:r,size:i,height:s,width:a}=t;e(o({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:a,height:s,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",r)})}})}))}(t),t):i(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,i)=>{let s=en.chooseFile;if(void 0!==Xt&&"function"==typeof Xt.chooseMessageFile&&(s=Xt.chooseMessageFile),"function"!=typeof s)return i({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});s({type:"all",count:t,extension:n,success(t){e(o(t))},fail(e){i({errMsg:e.errMsg.replace("chooseFile:fail",r)})}})}))}(t),t)}}})),KS=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(VS);function JS(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if("manual"===this.loadtime)return;let n=!1;const r=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(r.push(e[o]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,r)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:r,count:o}=n.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=r.length<this.pageSize;const i=e?r.length?r[0]:void 0:r;this.mixinDatacomResData=i,t&&t(i)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const r=t.action||this.action;r&&(n=n.action(r));const o=t.collection||this.collection;n=Array.isArray(o)?n.collection(...o):n.collection(o);const i=t.where||this.where;i&&Object.keys(i).length&&(n=n.where(i));const s=t.field||this.field;s&&(n=n.field(s));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const u=t.groupField||this.groupField;u&&(n=n.groupField(u)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const l=t.orderby||this.orderby;l&&(n=n.orderBy(l));const f=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,p=void 0!==t.getcount?t.getcount:this.getcount,d=void 0!==t.gettree?t.gettree:this.gettree,g=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,v={getCount:p},m={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return d&&(v.getTree=m),g&&(v.getTreePath=m),n=n.skip(h*(f-1)).limit(h).get(v),n}}}}function YS(e){return Bb("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function GS({openid:e,callLoginByWeixin:t=!1}={}){const n=YS(this);if(e&&t)throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");if(e)return n.mpWeixinOpenid=e,{};const r=await new Promise(((e,t)=>{en.login({success(t){e(t.code)},fail(e){t(new Error(e.errMsg))}})})),o=this.importObject("uni-id-co",{customUI:!0});return await o.secureNetworkHandshakeByWeixin({code:r,callLoginByWeixin:t}),n.mpWeixinCode=r,{code:r}}async function QS(e){const t=YS(this);return t.initPromise||(t.initPromise=GS.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function ZS(e){mw=e}function XS(e){const t={getSystemInfo:en.getSystemInfo,getPushClientId:en.getPushClientId};return function(n){return new Promise(((r,o)=>{t[e]({...n,success(e){r(e)},fail(e){o(e)}})}))}}class eO extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const r=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let r=0;r<n.length;r++)n[r](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([XS("getSystemInfo")(),XS("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:r,message:o}=t;this._payloadQueue.push({action:n,messageId:r,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:r}=e;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){en.onPushMessage(this._uniPushMessageCallback)}_destroy(){en.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const tO={tcb:Mx,tencent:Mx,aliyun:xw,private:Nx,dcloud:Nx,alipay:Kx};let nO=new class{init(e){let t={};const n=tO[e.provider];if(!n)throw new Error("未提供正确的provider参数");var r;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new Eb({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),iS(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(r=t).database=function(e){if(e&&Object.keys(e).length>0)return r.init(e).database();if(this._database)return this._database;const t=vS(mS,{uniClient:r});return this._database=t,t},r.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return r.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=vS(mS,{uniClient:r,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=zS,e.chooseAndUploadFile=KS.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return JS(e)}}),e.SSEChannel=eO,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return QS.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.setCustomClientInfo=ZS,e.importObject=function(t){return function(n,r={}){r=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},r);const{customUI:o,loadingOptions:i,errorOptions:s,parseSystemError:a}=r,c=!o;return new Proxy({},{get(o,u){switch(u){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...r){const o=n?n({params:r}):{};let i,s;try{return await Hb(zb(t,"invoke"),{...o}),i=await e(...r),await Hb(zb(t,"success"),{...o,result:i}),i}catch(a){throw s=a,await Hb(zb(t,"fail"),{...o,error:s}),s}finally{await Hb(zb(t,"complete"),s?{...o,error:s}:{...o,result:i})}}}({fn:async function o(...l){let f;c&&en.showLoading({title:i.title,mask:i.mask});const h={name:n,type:"OBJECT",data:{method:u,params:l}};"object"==typeof r.secretMethods&&function(e,t){const n=t.data.method,r=e.secretMethods||{},o=r[n]||r["*"];o&&(t.secretType=o)}(r,h);let p=!1;try{f=await t.callFunction(h)}catch(e){p=!0,f={result:new cw(e)}}const{errSubject:d,errCode:g,errMsg:v,newToken:m}=f.result||{};if(c&&en.hideLoading(),m&&m.token&&m.tokenExpired&&(hw(m),rw(Gb,{...m})),g){let e=v;if(p&&a&&(e=(await a({objectName:n,methodName:u,params:l,errSubject:d,errCode:g,errMsg:v})).errMsg||v),c)if("toast"===s.type)en.showToast({title:e,icon:"none"});else{if("modal"!==s.type)throw new Error(`Invalid errorOptions.type: ${s.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:r,confirmText:o}={}){return new Promise(((i,s)=>{en.showModal({title:e,content:t,showCancel:n,cancelText:r,confirmText:o,success(e){i(e)},fail(){i({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:s.retry,cancelText:"取消",confirmText:s.retry?"重试":"确定"});if(s.retry&&t)return o(...l)}}const t=new cw({subject:d,code:g,message:v,requestId:f.requestId});throw t.detail=f.result,rw(Jb,{type:Xb,content:t}),t}return rw(Jb,{type:Xb,content:f.result}),f.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:u,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let r=!1;if("callFunction"===t){const e=n&&n.type||Sb;r=e!==Sb}const o="callFunction"===t&&!r,i=this._initPromiseHub.exec();n=n||{};const{success:s,fail:a,complete:c}=aw(n),u=i.then((()=>r?Promise.resolve():Hb(zb(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>r?Promise.resolve(e):Hb(zb(t,"success"),e).then((()=>Hb(zb(t,"complete"),e))).then((()=>(o&&rw(Jb,{type:Zb,content:e}),Promise.resolve(e))))),(e=>r?Promise.reject(e):Hb(zb(t,"fail"),e).then((()=>Hb(zb(t,"complete"),e))).then((()=>(rw(Jb,{type:Zb,content:e}),Promise.reject(e))))));if(!(s||a||c))return u;u.then((e=>{s&&s(e),c&&c(e),o&&rw(Jb,{type:Zb,content:e})}),(e=>{a&&a(e),c&&c(e),o&&rw(Jb,{type:Zb,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=Rb;let t={};if(e&&1===e.length)t=e[0],nO=nO.init(t),nO._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{nO[e]=function(){return console.error(n),Promise.reject(new cw({code:"SYS_ERR",message:n}))}}))}Object.assign(nO,{get mixinDatacom(){return JS(nO)}}),FS(nO),nO.addInterceptor=qb,nO.removeInterceptor=Wb,nO.interceptObject=Vb})();var rO=nO;exports.Request=class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},console.warn("设置全局参数必须接收一个Object")),this.config=Oa({...Sa,...e}),this.interceptors={request:new wa,response:new wa}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let r={baseURL:t.baseURL||e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:ga(e.header||{},t.header||{})};if(r={...r,...xa(["getTask","validateStatus","paramsSerializer","forcedJSONParsing"],e,t)},"DOWNLOAD"===n){const n=["timeout","filePath"];r={...r,...xa(n,e,t)}}else if("UPLOAD"===n)delete r.header["content-type"],delete r.header["Content-Type"],["filePath","name","timeout","formData"].forEach((e=>{va(t[e])||(r[e]=t[e])})),va(r.timeout)&&!va(e.timeout)&&(r.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","enableHttp2","enableQuic","enableCache","enableHttpDNS","httpDNSServiceId","enableChunked","forceCellularNetwork"];r={...r,...xa(n,e,t)}}return r})(this.config,e);let t=[ba,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}connect(e,t,n={}){return this.middleware({url:e,data:t,method:"CONNECT",...n})}head(e,t,n={}){return this.middleware({url:e,data:t,method:"HEAD",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}trace(e,t,n={}){return this.middleware({url:e,data:t,method:"TRACE",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}get version(){return"3.1.0"}},exports._export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},exports.clone=ep,exports.cloneDeep=tp,exports.computed=_i,exports.createPinia=function(){const e=on(!0),t=e.run((()=>Or({})));let n=[],r=[];const o=mr({install(e){A_(o),o._a=e,e.provide(I_,o),e.config.globalProperties.$pinia=o,r.forEach((e=>n.push(e))),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o},exports.createSSRApp=us,exports.dayjs=k_,exports.defineComponent=
/*! #__NO_SIDE_EFFECTS__ */
function(e,t){return d(e)?(()=>a({name:e.name},t,{setup:e}))():e},exports.defineStore=function(e,t,n){let r,o;const i="function"==typeof t;function s(e,n){(e=e||(!!(ci||no||po)?go(I_,null):null))&&A_(e),(e=T_)._s.has(r)||(i?B_(r,t,o,e):function(e,t,n,r){const{state:o,actions:i,getters:s}=t,a=n.state.value[e];let c;c=B_(e,(function(){a||(n.state.value[e]=o?o():{});const t=Pr(n.state.value[e]);return N_(t,i,Object.keys(s||{}).reduce(((t,r)=>(t[r]=mr(_i((()=>{A_(n);const t=n._s.get(e);return s[r].call(t,t)}))),t)),{}))}),t,n,0,!0)}(r,o,e));return e._s.get(r)}return"string"==typeof e?(r=e,o=i?n:t):(o=e,r=e.id),s.$id=r,s},exports.duration=pb,exports.e=(e,...t)=>a(e,...t),exports.f=(e,t)=>function(e,t){let n;if(f(e)||g(e)){n=new Array(e.length);for(let r=0,o=e.length;r<o;r++)n[r]=t(e[r],r,r)}else if("number"==typeof e){n=new Array(e);for(let r=0;r<e;r++)n[r]=t(r+1,r,r)}else if(m(e))if(e[Symbol.iterator])n=Array.from(e,((e,n)=>t(e,n,n)));else{const r=Object.keys(e);n=new Array(r.length);for(let o=0,i=r.length;o<i;o++){const i=r[o];n[o]=t(e[i],i,o)}}else n=[];return n}(e,t),exports.fetchEventSource=function(e,t){var{signal:n,headers:r,onopen:o,onmessage:i,onclose:s,onerror:a,openWhenHidden:c,fetch:u}=t,l=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(t,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(((t,f)=>{const h=Object.assign({},r);let p;function d(){p.abort(),document.hidden||b()}h.accept||(h.accept="text/event-stream"),c||document.addEventListener("visibilitychange",d);let g=1e3,v=0;function m(){document.removeEventListener("visibilitychange",d),window.clearTimeout(v),p.abort()}null==n||n.addEventListener("abort",(()=>{m(),t()}));const y=null!=u?u:window.fetch,_=null!=o?o:gb;async function b(){var n;p=new AbortController;try{const n=await y(e,Object.assign(Object.assign({},l),{headers:h,signal:p.signal}));await _(n),await async function(e,t){const n=e.getReader();let r;for(;!(r=await n.read()).done;)t(r.value)}(n.body,db(function(e,t,n){let r={data:"",event:"",id:"",retry:void 0};const o=new TextDecoder;return function(i,s){if(0===i.length)null==n||n(r),r={data:"",event:"",id:"",retry:void 0};else if(s>0){const n=o.decode(i.subarray(0,s)),a=s+(32===i[s+1]?2:1),c=o.decode(i.subarray(a));switch(n){case"data":r.data=r.data?r.data+"\n"+c:c;break;case"event":r.event=c;break;case"id":e(r.id=c);break;case"retry":const n=parseInt(c,10);isNaN(n)||t(r.retry=n)}}}}((e=>{e?h["last-event-id"]=e:delete h["last-event-id"]}),(e=>{g=e}),i))),null==s||s(),m(),t()}catch(r){if(!p.signal.aborted)try{const e=null!==(n=null==a?void 0:a(r))&&void 0!==n?n:g;window.clearTimeout(v),v=window.setTimeout(b,e)}catch(o){m(),f(o)}}}b()}))},exports.getCurrentInstance=ui,exports.index=en,exports.index$1=({options:e,store:t})=>{var n,r,o,i,s,a;if(null==(n=e.persist)?void 0:n.enabled){const n=[{key:t.$id,storage:(null==(r=e.persist)?void 0:r.H5Storage)||(null==window?void 0:window.sessionStorage)}],c=(null==(i=null==(o=e.persist)?void 0:o.strategies)?void 0:i.length)?null==(s=e.persist)?void 0:s.strategies:n;c.forEach((n=>{var r,o;const i=n.storage||(null==(r=e.persist)?void 0:r.H5Storage)||(null==window?void 0:window.sessionStorage),s=n.key||t.$id;let a;a=q_||(null==(o=e.persist)?void 0:o.enforceCustomStorage)?i.getItem(s):en.getStorageSync(s),a&&(t.$patch(JSON.parse(a)),W_(n,t,e.persist))})),t.$subscribe((()=>{c.forEach((n=>{W_(n,t,e.persist)}))}),{detached:!!(null==(a=e.persist)?void 0:a.detached)})}},exports.initVueI18n=function(e,t={},n,r){if("string"!=typeof e){const n=[t,e];e=n[0],t=n[1]}"string"!=typeof e&&(e=void 0!==en&&en.getLocale?en.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const o=new le({locale:e,fallbackLocale:n,messages:t,watcher:r});let i=(e,t)=>{if("function"!=typeof getApp)i=function(e,t){return o.t(e,t)};else{let e=!1;i=function(t,n){const r=getApp().$vm;return r&&(r.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(r,o))),o.t(t,n)}}return i(e,t)};return{i18n:o,f:(e,t,n)=>o.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>o.add(e,t,n),watch:e=>o.watchLocale(e),getLocale:()=>o.getLocale(),setLocale:e=>o.setLocale(e)}},exports.isArray=Ua,exports.isEmpty=Fg,exports.isNil=Vg,exports.isObject=Ga,exports.isRef=Sr,exports.isString=fg,exports.lodash=qc,exports.min=gv,exports.n=e=>B(e),exports.nextTick$1=zr,exports.nr=rO,exports.o=(e,t)=>ss(e,t),exports.onBackPress=sa,exports.onBeforeMount=xo,exports.onBeforeUnmount=To,exports.onHide=ta,exports.onLaunch=na,exports.onLoad=ra,exports.onMounted=So,exports.onPageScroll=aa,exports.onPullDownRefresh=ua,exports.onReachBottom=ca,exports.onReady=oa,exports.onShareAppMessage=fa,exports.onShareTimeline=la,exports.onShow=ea,exports.onUnload=ia,exports.p=e=>function(e){const{uid:t,__counter:n}=ui();return t+","+((ts[t]||(ts[t]=[])).push(oi(e))-1)+","+n}(e),exports.reactive=lr,exports.ref=Or,exports.relativeTime=z_,exports.resolveComponent=function(e,t){return function(e,t,n=!0,r=!1){const o=no||ci;if(o){const n=o.type;if("components"===e){const e=function(e,t=!0){return d(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===T(t)||e===P(T(t))))return n}const i=oo(o[e]||n[e],t)||oo(o.appContext[e],t);return!i&&r?n:i}}("components",e,!0,t)||e},exports.s=e=>is(e),exports.sr=(e,t,n)=>function(e,t,n={}){const{$templateRefs:r}=ui();r.push({i:t,r:e,k:n.k,f:n.f})}(e,t,n),exports.startsWith=oy,exports.t=e=>(e=>g(e)?e:null==e?"":f(e)||m(e)&&(e.toString===_||!d(e.toString))?JSON.stringify(e,U,2):String(e))(e),exports.toRefs=Pr,exports.unref=Tr,exports.useCssVars=function(e){const t=ui();t&&function(e,t){e.ctx.__cssVars=()=>{const n=t(e.proxy),r={};for(const e in n)r[`--${e}`]=n[e];return r}}(t,e)},exports.w=(e,t)=>cs(e,t),exports.watch=so,exports.watchPostEffect=function(e,t){return ao(e,null,{flush:"post"})},exports.wx$1=Xt;
