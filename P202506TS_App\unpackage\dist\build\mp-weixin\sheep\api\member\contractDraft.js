"use strict";const t=require("../../request/index.js"),e={getContractDraftPage:e=>t.request({url:"/member/contract-draft/page",method:"GET",params:e}),getContractDraft:e=>t.request({url:"/member/contract-draft/get?id="+e,method:"GET"}),createContractDraft:e=>t.request({url:"/member/contract-draft/create",method:"POST",data:e}),updateContractDraft:e=>t.request({url:"/member/contract-draft/update",method:"PUT",data:e}),updateContractDraftStatus:(e,r)=>t.request({url:"/member/contract-draft/update-status",method:"PUT",params:{id:e,status:r}}),deleteContractDraft:e=>t.request({url:"/member/contract-draft/delete?id="+e,method:"DELETE"}),exportContractDraft:e=>t.request({url:"/member/contract-draft/export-excel",method:"GET",params:e})};exports.ContractDraftApi=e;
