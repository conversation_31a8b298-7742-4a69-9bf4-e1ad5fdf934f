"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js");if(!Array){(e.resolveComponent("su-tabbar-item")+e.resolveComponent("su-tabbar"))()}Math||((()=>"../../ui/su-tabbar-item/su-tabbar-item.js")+(()=>"../../ui/su-tabbar/su-tabbar.js"))();const r={__name:"s-tabbar",props:{path:String,default:""},setup(r){const a=e.computed((()=>{var e;return null==(e=t.sheep.$store("app").template.basic)?void 0:e.tabbar})),o=e.computed((()=>{const e=a.value.style;return"color"===e.bgType?{background:e.bgColor}:"img"===e.bgType?{background:`url(${t.sheep.$url.cdn(e.bgImg)}) no-repeat top center / 100% auto`}:void 0})),u=t=>2===e.unref(a).mode&&(e.unref(a).items%2>0&&Math.ceil(e.unref(a).items.length/2)===t+1);return(l,n)=>{var s,i,c,b;return e.e({a:(null==(i=null==(s=a.value)?void 0:s.items)?void 0:i.length)>0},(null==(b=null==(c=a.value)?void 0:c.items)?void 0:b.length)>0?{b:e.f(a.value.items,((r,a,o)=>({a:20===a?"scale(3)":"unset",b:e.unref(t.sheep).$url.static(r.activeIconUrl),c:20===a?"scale(3)":"unset",d:e.unref(t.sheep).$url.static(r.iconUrl),e:r.text,f:e.o((a=>e.unref(t.sheep).$router.go(r.url)),r.text),g:"57cd8214-1-"+o+",57cd8214-0",h:e.p({text:r.text,name:r.url,isCenter:u(a),centerImage:e.unref(t.sheep).$url.static(r.iconUrl)})}))),c:e.p({value:r.path,fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,inactiveColor:a.value.style.color,activeColor:a.value.style.activeColor,midTabBar:2===a.value.mode,customStyle:o.value})}:{})}}};wx.createComponent(r);
