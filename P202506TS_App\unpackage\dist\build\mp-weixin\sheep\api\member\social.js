"use strict";const e=require("../../request/index.js"),s={getSocialUser:s=>e.request({url:"/member/social-user/get",method:"GET",params:{type:s},custom:{showLoading:!1}}),socialBind:(s,t,o)=>e.request({url:"/member/social-user/bind",method:"POST",data:{type:s,code:t,state:o},custom:{custom:{showSuccess:!0,loadingMsg:"绑定中",successMsg:"绑定成功"}}}),socialUnbind:(s,t)=>e.request({url:"/member/social-user/unbind",method:"DELETE",data:{type:s,openid:t},custom:{showLoading:!1,loadingMsg:"解除绑定",successMsg:"解绑成功"}}),getSubscribeTemplateList:()=>e.request({url:"/member/social-user/get-subscribe-template-list",method:"GET",custom:{showError:!1,showLoading:!1}}),getWxaQrcode:async(s,t)=>await e.request({url:"/member/social-user/wxa-qrcode",method:"POST",data:{scene:t,path:s,checkPath:!1}})};exports.SocialApi=s;
