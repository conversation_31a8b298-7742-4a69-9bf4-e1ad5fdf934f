"use strict";const e=require("../../common/vendor.js").defineStore({id:"modal",state:()=>({auth:"",share:!1,menu:!1,advHistory:[],lastTimer:{smsLogin:0,changeMobile:0,resetPassword:0,changePassword:0},extData:{}}),persist:{enabled:!0,strategies:[{key:"modal-store",paths:["lastTimer","advHistory"]}]}}),t=Object.freeze(Object.defineProperty({__proto__:null,default:e},Symbol.toStringTag,{value:"Module"}));exports.__vite_glob_0_2=t;
