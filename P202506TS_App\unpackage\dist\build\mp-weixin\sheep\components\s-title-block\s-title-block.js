"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),a={__name:"s-title-block",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(a){const o=t.reactive({typeMap:{left:"ss-row-left",center:"ss-row-center"}}),r=a,d=t.computed((()=>{const{bgType:t,bgImg:e,bgColor:a}=r.styles;return{background:"img"===t?`url(${e}) no-repeat top center / 100% 100%`:a}}));return(r,i)=>{var n,s;return t.e({a:a.data.title},a.data.title?{b:t.t(a.data.title),c:`${a.data.titleSize}px`,d:a.data.titleWeight,e:a.data.titleColor,f:a.data.textAlign}:{},{g:a.data.description},a.data.description?{h:t.t(a.data.description),i:`${a.data.descriptionSize}px`,j:a.data.descriptionWeight,k:a.data.descriptionColor,l:a.data.textAlign}:{},{m:null==(n=a.data.more)?void 0:n.show},(null==(s=a.data.more)?void 0:s.show)?t.e({n:"icon"!==a.data.more.type},"icon"!==a.data.more.type?{o:t.t(a.data.more.text)}:{},{p:"text"!==a.data.more.type},(a.data.more.type,{}),{q:t.o((o=>t.unref(e.sheep).$router.go(a.data.more.url))),r:a.data.descriptionColor}):{},{s:t.n(o.typeMap[a.data.textAlign]),t:t.s(d.value),v:t.s({marginLeft:`${a.data.space}px`})})}}},o=t._export_sfc(a,[["__scopeId","data-v-5e45b066"]]);wx.createComponent(o);
