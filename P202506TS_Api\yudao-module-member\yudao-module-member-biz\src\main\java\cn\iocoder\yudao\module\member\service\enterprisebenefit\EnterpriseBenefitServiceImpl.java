package cn.iocoder.yudao.module.member.service.enterprisebenefit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.ENTERPRISE_BENEFIT_NOT_EXISTS;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.ENTERPRISE_BENEFIT_X_NOT_ENOUGH;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.ENTERPRISE_BENEFIT_X_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.member.controller.admin.enterprisebenefit.vo.EnterpriseBenefitPageReqVO;
import cn.iocoder.yudao.module.member.controller.admin.enterprisebenefit.vo.EnterpriseBenefitRespVO;
import cn.iocoder.yudao.module.member.controller.admin.enterprisebenefit.vo.EnterpriseBenefitSaveReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.enterprisebenefit.EnterpriseBenefitDO;
import cn.iocoder.yudao.module.member.dal.dataobject.enterprisebenefitlog.EnterpriseBenefitLogDO;
import cn.iocoder.yudao.module.member.dal.mysql.enterprisebenefit.EnterpriseBenefitMapper;
import cn.iocoder.yudao.module.member.dal.mysql.enterprisebenefitlog.EnterpriseBenefitLogMapper;
import cn.iocoder.yudao.module.product.api.benefitrelation.BenefitRelationApi;
import cn.iocoder.yudao.module.product.enums.benefit.ProductBenefitEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 企业权益 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EnterpriseBenefitServiceImpl implements EnterpriseBenefitService {

    @Resource
    private BenefitRelationApi benefitRelationApi;
    @Resource
    private EnterpriseBenefitMapper enterpriseBenefitMapper;
    @Resource
    private EnterpriseBenefitLogMapper enterpriseBenefitLogMapper;

    @Override
    public Long createEnterpriseBenefit(EnterpriseBenefitSaveReqVO createReqVO) {
        // 插入
        EnterpriseBenefitDO enterpriseBenefit = BeanUtils.toBean(createReqVO,
                EnterpriseBenefitDO.class);
        enterpriseBenefitMapper.insert(enterpriseBenefit);
        // 返回
        return enterpriseBenefit.getId();
    }

    @Override
    public void updateEnterpriseBenefit(EnterpriseBenefitSaveReqVO updateReqVO) {
        // 校验存在
        validateEnterpriseBenefitExists(updateReqVO.getId());
        // 更新
        EnterpriseBenefitDO updateObj = BeanUtils.toBean(updateReqVO, EnterpriseBenefitDO.class);
        enterpriseBenefitMapper.updateById(updateObj);
    }

    @Override
    public void deleteEnterpriseBenefit(Long id) {
        // 校验存在
        validateEnterpriseBenefitExists(id);
        // 删除
        enterpriseBenefitMapper.deleteById(id);
    }

    private void validateEnterpriseBenefitExists(Long id) {
        if (enterpriseBenefitMapper.selectById(id) == null) {
            throw exception(ENTERPRISE_BENEFIT_NOT_EXISTS);
        }
    }

    @Override
    public EnterpriseBenefitDO getEnterpriseBenefit(Long id) {
        return enterpriseBenefitMapper.selectById(id);
    }

    @Override
    public PageResult<EnterpriseBenefitDO> getEnterpriseBenefitPage(
            EnterpriseBenefitPageReqVO pageReqVO) {
        return enterpriseBenefitMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<EnterpriseBenefitRespVO> getEnterpriseBenefitPageByEnterprise(
            EnterpriseBenefitPageReqVO pageReqVO) {
        return enterpriseBenefitMapper.selectPageByEnterprise(pageReqVO);
    }

    @Override
    public void initEnterpriseBenefit(Long enterpriseId, Long spuId) {
        List<EnterpriseBenefitDO> enterpriseBenefitList = BeanUtils.toBean(
                benefitRelationApi.getBenefitRelationList(spuId), EnterpriseBenefitDO.class,
                vo -> vo.setEnterpriseId(enterpriseId).setBenefitBalance(vo.getBenefitTotal()));
        List<EnterpriseBenefitLogDO> enterpriseBenefitLogList = BeanUtils.toBean(
                enterpriseBenefitList, EnterpriseBenefitLogDO.class,
                vo -> vo.setBenefitUsed(0));
        // TODO @me 重复开通的权益如何处理
        if (!enterpriseBenefitList.isEmpty()) {
            enterpriseBenefitMapper.delete(EnterpriseBenefitDO::getEnterpriseId, enterpriseId);
            enterpriseBenefitMapper.insertBatch(enterpriseBenefitList);
            enterpriseBenefitLogMapper.insertBatch(enterpriseBenefitLogList);
        }
    }

    @Override
    public void validateEnterpriseBenefit(Long enterpriseId, String benefitCode) {
        // 校验企业此权益是否存在
        EnterpriseBenefitDO enterpriseBenefitDO = enterpriseBenefitMapper.selectOne(
                EnterpriseBenefitDO::getEnterpriseId, enterpriseId,
                EnterpriseBenefitDO::getBenefitCode, benefitCode);
        String benefitName = ProductBenefitEnum.getNameByCode(benefitCode);
        if (enterpriseBenefitDO == null) {
            throw exception(ENTERPRISE_BENEFIT_X_NOT_EXISTS, benefitName);
        }

        // 无限权益直接通过校验
        if (enterpriseBenefitDO.getBenefitTotal() == -1) {
            return;
        }

        // 根据权益单位判断校验方式
        String unit = enterpriseBenefitDO.getBenefitUnit();
        if (isTimeBenefit(unit)) {
            // 时间类权益：检查是否超过到期时间
            LocalDateTime expiryTime = calculateExpiryTime(
                enterpriseBenefitDO.getCreateTime(),
                enterpriseBenefitDO.getBenefitTotal()
            );
            if (expiryTime != null && LocalDateTime.now().isAfter(expiryTime)) {
                throw exception(ENTERPRISE_BENEFIT_X_NOT_ENOUGH, benefitName);
            }
        } else {
            // 次数类权益：检查余量是否足够（保持原有逻辑）
            if (enterpriseBenefitDO.getBenefitBalance() <= 0) {
                throw exception(ENTERPRISE_BENEFIT_X_NOT_ENOUGH, benefitName);
            }
        }
    }

    @Override
    public void updateEnterpriseBenefitBalance(Long enterpriseId, String benefitCode, Integer used,
            String log) {
        EnterpriseBenefitDO enterpriseBenefitDO = enterpriseBenefitMapper.selectOne(
                EnterpriseBenefitDO::getEnterpriseId, enterpriseId,
                EnterpriseBenefitDO::getBenefitCode, benefitCode);
        if (enterpriseBenefitDO == null) {
            throw exception(ENTERPRISE_BENEFIT_NOT_EXISTS);
        }
        if (enterpriseBenefitDO.getBenefitTotal() != -1) {
            // 实现增减权益
            enterpriseBenefitDO = enterpriseBenefitDO.setBenefitBalance(
                    enterpriseBenefitDO.getBenefitBalance() - used);
            enterpriseBenefitMapper.updateById(enterpriseBenefitDO);
            // 记录消费日志
            EnterpriseBenefitLogDO logDO = BeanUtils.toBean(enterpriseBenefitDO,
                    EnterpriseBenefitLogDO.class);
            logDO.setId(null);
            logDO.setBenefitUsed(used);
            logDO.setLogData(log);
            enterpriseBenefitLogMapper.insert(logDO);
        }
    }

    /**
     * 判断是否为时间类权益
     *
     * @param unit 权益单位
     * @return true-时间类权益，false-次数类权益
     */
    private boolean isTimeBenefit(String unit) {
        return "秒".equals(unit) || "分钟".equals(unit) || "天".equals(unit);
    }

    /**
     * 计算时间类权益的到期时间
     *
     * @param createTime 权益创建时间
     * @param totalSeconds 总时长（秒数）
     * @return 到期时间
     */
    private LocalDateTime calculateExpiryTime(LocalDateTime createTime, Integer totalSeconds) {
        if (createTime == null || totalSeconds == null || totalSeconds <= 0) {
            return null;
        }
        return createTime.plusSeconds(totalSeconds);
    }

}