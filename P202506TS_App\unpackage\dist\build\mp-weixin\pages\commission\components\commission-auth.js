"use strict";const e=require("../../../common/vendor.js"),o=require("../../../sheep/index.js"),r=require("../../../sheep/api/trade/brokerage.js");if(!Array){e.resolveComponent("su-popup")()}Math;const s={__name:"commission-auth",setup(s){const a=e.reactive({show:!1});return e.onShow((async()=>{const{code:e,data:o}=await r.BrokerageApi.getBrokerageUser();0!==e||(null==o?void 0:o.brokerageEnabled)||(a.show=!0)})),(r,s)=>({a:e.unref(o.sheep).$url.static("/static/img/shop/commission/forbidden.png"),b:e.o((r=>e.unref(o.sheep).$router.back())),c:e.o((r=>e.unref(o.sheep).$router.back())),d:e.o((e=>a.show=!1)),e:e.p({show:a.show,type:"center",round:"10",isMaskClick:!1,maskBackgroundColor:"rgba(0, 0, 0, 0.7)"})})}},a=e._export_sfc(s,[["__scopeId","data-v-b5f426d3"]]);wx.createComponent(a);
