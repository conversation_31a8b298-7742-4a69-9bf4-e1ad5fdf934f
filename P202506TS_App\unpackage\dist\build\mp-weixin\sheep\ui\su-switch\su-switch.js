"use strict";const e=require("../../../common/vendor.js"),t={name:"UiSwitch"},a=Object.assign(t,{props:{modelValue:{type:[<PERSON><PERSON><PERSON>,Number],default:!1},ui:{type:String,default:""},bg:{type:String,default:"ui-BG-Main"},text:{type:String,default:""},size:{type:String,default:"sm"},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(t,{emit:a}){const d=t,u=a,i=()=>{u("update:modelValue",!d.modelValue)};return(t,a)=>({a:e.n({"ui-switch-input-checked":d.modelValue}),b:e.n(d.modelValue?d.bg:""),c:e.n(d.text),d:e.n(d.size),e:e.o(i),f:e.n({disabled:d.disabled}),g:e.n(d.ui)})}}),d=e._export_sfc(a,[["__scopeId","data-v-77d22f9f"]]);wx.createComponent(d);
