<s-layout wx:if="{{w}}" class="data-v-36fb4e6d" u-s="{{['d']}}" style="{{v}}" u-i="36fb4e6d-0" bind:__l="__l" u-p="{{w}}"><view class="cart-box ss-flex ss-flex-col ss-row-between data-v-36fb4e6d"><view class="cart-header ss-flex ss-col-center ss-row-between ss-p-x-30 data-v-36fb4e6d"><view class="header-left ss-flex ss-col-center ss-font-26 data-v-36fb4e6d"> 共 <text class="goods-number ui-TC-Main ss-flex data-v-36fb4e6d">{{a}}</text> 件商品 </view><view class="header-right data-v-36fb4e6d"><button wx:if="{{b}}" class="ss-reset-button data-v-36fb4e6d" bindtap="{{c}}"> 取消 </button><button wx:if="{{d}}" class="ss-reset-button ui-TC-Main data-v-36fb4e6d" bindtap="{{e}}"> 编辑 </button></view></view><view class="cart-content data-v-36fb4e6d"><view wx:for="{{f}}" wx:for-item="item" wx:key="g" class="goods-box ss-r-10 ss-m-b-14 data-v-36fb4e6d"><view class="ss-flex ss-col-center data-v-36fb4e6d"><label wx:if="{{g}}" class="check-box ss-flex ss-col-center ss-p-l-10 data-v-36fb4e6d" bindtap="{{item.c}}"><radio class="data-v-36fb4e6d" checked="{{item.a}}" color="var(--ui-BG-Main)" style="transform:scale(0.8)" catchtap="{{item.b}}"/></label><s-goods-item wx:if="{{item.f}}" class="data-v-36fb4e6d" bindtap="{{item.d}}" u-i="{{item.e}}" bind:__l="__l" u-p="{{item.f}}"></s-goods-item></view></view></view><su-fixed wx:if="{{o}}" class="data-v-36fb4e6d" u-s="{{['d']}}" data-c-h="{{!n}}" u-i="36fb4e6d-2,36fb4e6d-0" bind:__l="__l" u-p="{{o}}"><view class="cart-footer ss-flex ss-col-center ss-row-between ss-p-x-30 border-bottom data-v-36fb4e6d"><view class="footer-left ss-flex ss-col-center data-v-36fb4e6d"><label class="check-box ss-flex ss-col-center ss-p-r-30 data-v-36fb4e6d" bindtap="{{j}}"><radio class="data-v-36fb4e6d" checked="{{h}}" color="var(--ui-BG-Main)" style="transform:scale(0.7)" catchtap="{{i}}"/><view class="data-v-36fb4e6d">全选</view></label></view><view class="footer-right ss-flex data-v-36fb4e6d"><button class="{{['data-v-36fb4e6d', 'ss-reset-button  pay-btn ss-font-28 ', k]}}" bindtap="{{l}}"> 删除足迹 </button><button class="ss-reset-button ui-BG-Main-Gradient pay-btn ss-font-28 ui-Shadow-Main ml-2 data-v-36fb4e6d" bindtap="{{m}}"> 清空 </button></view></view></su-fixed></view><uni-load-more wx:if="{{p}}" class="data-v-36fb4e6d" bindtap="{{q}}" u-i="36fb4e6d-3,36fb4e6d-0" bind:__l="__l" u-p="{{r}}"/><s-empty wx:if="{{s}}" class="data-v-36fb4e6d" u-i="36fb4e6d-4,36fb4e6d-0" bind:__l="__l" u-p="{{t}}"/></s-layout>