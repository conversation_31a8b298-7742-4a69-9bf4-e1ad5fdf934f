"use strict";const e=require("../../common/vendor.js"),t=require("./provider/wechat/index.js"),r=require("./provider/apple/index.js"),a=require("./share.js"),o=require("./pay.js"),i=e.index.getSystemInfoSync(),n=i.platform;let s="",c="",p="";s="WechatMiniProgram",p="miniProgram",c="wechat",e.isEmpty(s)&&e.index.showToast({title:"暂不支持该平台",icon:"none"});const h=(e="")=>(""===e&&(e="wechat"),"wechat"===e?t.wechat:"apple"===e?r.apple:void 0);const d=(()=>{let t=e.index.getMenuButtonBoundingClientRect();return t||(t={bottom:56,height:32,left:278,right:365,top:24,width:87}),t})(),l=i.statusBarHeight+44;const m={name:s,device:i,os:n,provider:"wechat",platform:"miniProgram",useProvider:h,checkUpdate:(e=!1)=>{h().checkUpdate(e)},checkNetwork:async function(){return"none"==(await e.index.getNetworkType()).networkType?Promise.resolve(!1):Promise.resolve(!0)},pay:(e,t,r)=>new o.SheepPay(e,t,r),share:a.$share,load:()=>{t.wechat.load()},capsule:d,navbar:l,landingPage:"",isWechatInstalled:!0};exports._platform=m;
