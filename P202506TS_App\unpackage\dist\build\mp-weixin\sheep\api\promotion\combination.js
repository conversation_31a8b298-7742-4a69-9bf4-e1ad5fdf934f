"use strict";const t=require("../../request/index.js"),o={getCombinationActivityPage:o=>t.request({url:"/promotion/combination-activity/page",method:"GET",params:o}),getCombinationActivity:o=>t.request({url:"/promotion/combination-activity/get-detail",method:"GET",params:{id:o}}),getCombinationActivityListByIds:o=>t.request({url:"/promotion/combination-activity/list-by-ids",method:"GET",params:{ids:o}}),getHeadCombinationRecordList:(o,i,e)=>t.request({url:"/promotion/combination-record/get-head-list",method:"GET",params:{activityId:o,status:i,count:e}}),getCombinationRecordPage:o=>t.request({url:"/promotion/combination-record/page",method:"GET",params:o}),getCombinationRecordDetail:o=>t.request({url:"/promotion/combination-record/get-detail",method:"GET",params:{id:o}}),getCombinationRecordSummary:()=>t.request({url:"/promotion/combination-record/get-summary",method:"GET"})};exports.CombinationApi=o;
