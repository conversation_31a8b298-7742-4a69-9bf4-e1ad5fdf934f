<template>
  <el-dialog
    title="产品权益"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    destroy-on-close
  >
    <div class="benefits-header">
      <div class="product-info">
        <div class="info-item">
          <span class="label">产品名称：</span>
          <span class="value">{{ productData.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">产品类型：</span>
          <span class="value">{{ productData.type || '商品' }}</span>
        </div>
      </div>
      <el-button type="primary" @click="openBenefitForm">设置权益</el-button>
    </div>

    <el-table v-loading="loading" :data="benefitsList" border>
      <el-table-column label="权益名称">
        <template #default="scope">
          {{ getBenefitName(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column label="总量" prop="total" align="center">
        <template #default="scope">
          {{ formatTotalDisplay(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column label="单位" prop="unit" align="center">
        <template #default="scope">
          {{ formatUnitDisplay(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template #default="scope">
          <el-button type="danger" link @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 权益设置弹窗 -->
  <BenefitForm 
    ref="benefitFormRef" 
    @success="handleBenefitSuccess" 
    :productName="productData.name" 
    :productType="productData.type || '商品'" 
    :spuId="productData.id"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import BenefitForm from './BenefitForm.vue'
import { BenefitRelationApi } from '@/api/product/benefitrelation'
import { BenefitApi } from '@/api/product/benefit'
import { useMessage } from '@/hooks/web/useMessage'

const message = useMessage()
const dialogVisible = ref(false)
const benefitFormRef = ref()
const loading = ref(false)
const productData = reactive({
  id: 0,
  name: '',
  type: ''
})
const benefitsList = ref([])
const benefitCache = ref({}) // 缓存权益信息
const isComponentMounted = ref(true) // 跟踪组件是否已挂载

/** 打开弹窗 */
const open = async (row: any) => {
  dialogVisible.value = true
  productData.id = row.id
  productData.name = row.name
  productData.type = row.type || '商品'
  
  // 加载权益列表
  await loadBenefits()
}

/** 加载权益列表 */
const loadBenefits = async () => {
  if (!isComponentMounted.value) return
  
  try {
    loading.value = true
    const res = await BenefitRelationApi.getBenefitRelationPage({ 
      pageNo: 1, 
      pageSize: 100,
      productId: productData.id 
    })
    
    if (!isComponentMounted.value) return
    
    // 打印返回数据，查看字段结构
    console.log('权益关系数据:', res.list)
    
    // 检查第一条数据的所有字段
    if (res.list && res.list.length > 0) {
      console.log('第一条权益数据的所有字段:', Object.keys(res.list[0]))
      for (const key in res.list[0]) {
        console.log(`字段 ${key}:`, res.list[0][key])
      }
    }
    
    benefitsList.value = res.list || []
    
    // 如果没有权益名称字段，尝试获取权益详情
    await loadBenefitDetails()
  } catch (error) {
    if (isComponentMounted.value) {
      message.error('获取产品权益失败')
      console.error('获取产品权益失败:', error)
    }
  } finally {
    if (isComponentMounted.value) {
      loading.value = false
    }
  }
}

/** 加载权益详情 */
const loadBenefitDetails = async () => {
  if (!isComponentMounted.value) return
  
  try {
    // 获取所有需要查询详情的权益ID
    const benefitIds = benefitsList.value
      .filter(item => !item.benefitName) // 只查询没有名称的
      .map(item => item.benefitId)
      
    if (benefitIds.length === 0) return
    
    // 查询权益详情
    for (const id of benefitIds) {
      if (!isComponentMounted.value) return
      
      if (!benefitCache.value[id]) {
        try {
          const detail = await BenefitApi.getBenefit(id)
          if (!isComponentMounted.value) return
          benefitCache.value[id] = detail
        } catch (e) {
          console.error(`获取权益 ${id} 详情失败:`, e)
        }
      }
    }
    
    if (!isComponentMounted.value) return
    
    // 更新权益列表中的名称
    benefitsList.value = benefitsList.value.map(item => {
      if (!item.benefitName && benefitCache.value[item.benefitId]) {
        return {
          ...item,
          benefitName: benefitCache.value[item.benefitId].name
        }
      }
      return item
    })
  } catch (error) {
    console.error('加载权益详情失败:', error)
  }
}

/** 获取权益名称 */
const getBenefitName = (row) => {
  // 尝试多种可能的字段名
  if (row.benefitName) return row.benefitName
  if (row.name) return row.name
  if (benefitCache.value[row.benefitId]) return benefitCache.value[row.benefitId].name

  // 如果都没有，返回ID或默认值
  return row.benefitId ? `权益 ${row.benefitId}` : '未知权益'
}

/** 格式化总量显示 */
const formatTotalDisplay = (row) => {
  if (row.total === -1) return '不限'

  // 根据单位进行转换显示
  if (row.unit === '秒') {
    // 秒数单位（新的天数权益）：从秒转换为天数显示
    const days = Math.floor(row.total / (24 * 60 * 60))
    return days
  } else if (row.unit === '天') {
    // 天数单位：从秒转换为天数显示
    const days = Math.floor(row.total / (24 * 60 * 60))
    return days
  } else if (row.unit === '分钟') {
    // 历史分钟数据：从秒转换为天数显示
    const minutes = Math.floor(row.total / 60)
    const days = Math.floor(minutes / (24 * 60))
    return days
  }

  // 次数、份数等直接显示
  return row.total
}

/** 格式化单位显示 */
const formatUnitDisplay = (row) => {
  // 将时间相关的单位都显示为天
  if (row.unit === '分钟' || row.unit === '秒' || row.unit === '天') {
    return '天'
  }
  return row.unit || '次'
}

/** 打开权益设置 */
const openBenefitForm = () => {
  benefitFormRef.value.open(benefitsList.value)
}

/** 处理权益设置成功 */
const handleBenefitSuccess = async () => {
  // 更新权益列表
  await loadBenefits()
}

/** 删除权益 */
const handleDelete = async (id: number) => {
  try {
    await message.confirm('确认要删除该权益吗？')
    await BenefitRelationApi.deleteBenefitRelation(id)
    message.success('删除成功')
    await loadBenefits()
  } catch (error) {
    // 用户取消或删除失败
  }
}

// 初始加载时预加载所有权益，但限制每页最大100条
onMounted(() => {
  isComponentMounted.value = true
  
  // 使用普通的 Promise 处理，而不是 async/await
  // 将pageSize从1000改为100，符合API限制
  BenefitApi.getBenefitPage({ pageNo: 1, pageSize: 100 })
    .then(res => {
      if (!isComponentMounted.value) return
      
      if (res.list) {
        res.list.forEach(item => {
          benefitCache.value[item.id] = item
        })
      }
    })
    .catch(e => {
      console.error('预加载权益失败:', e)
    })
})

// 组件卸载时设置标志
onBeforeUnmount(() => {
  isComponentMounted.value = false
})

defineExpose({
  open
})
</script>

<style scoped>
.benefits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  color: #606266;
  margin-right: 8px;
}

.value {
  font-weight: bold;
}
</style> 