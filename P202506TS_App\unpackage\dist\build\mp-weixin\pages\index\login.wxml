<view class="login-wrap data-v-0c4ea268" style="{{C}}"><view class="ss-flex ss-row-center ss-m-b-50 data-v-0c4ea268" style="width:100%"><image class="data-v-0c4ea268" src="{{a}}" style="width:160rpx" mode="widthFix"></image></view><account-login wx:if="{{b}}" class="data-v-0c4ea268" bindonConfirm="{{c}}" u-i="0c4ea268-0" bind:__l="__l" u-p="{{d}}"/><sms-login wx:if="{{e}}" class="data-v-0c4ea268" bindonConfirm="{{f}}" u-i="0c4ea268-1" bind:__l="__l" u-p="{{g}}"/><reset-password wx:if="{{h}}" class="data-v-0c4ea268" u-i="0c4ea268-2" bind:__l="__l"/><change-mobile wx:if="{{i}}" class="data-v-0c4ea268" u-i="0c4ea268-3" bind:__l="__l"/><change-password wx:if="{{j}}" class="data-v-0c4ea268" u-i="0c4ea268-4" bind:__l="__l"/><mp-authorization wx:if="{{k}}" class="data-v-0c4ea268" u-i="0c4ea268-5" bind:__l="__l"/><view wx:if="{{l}}" class="auto-login-box ss-flex ss-flex-col ss-row-center ss-col-center data-v-0c4ea268"><view wx:if="{{m}}" class="ss-flex register-box data-v-0c4ea268"><view class="register-title data-v-0c4ea268">还没有账号?</view><button class="ss-reset-button login-btn data-v-0c4ea268" open-type="getPhoneNumber" bindgetphonenumber="{{n}}"> 快捷登录 </button><view class="circle data-v-0c4ea268"/></view><button wx:if="{{o}}" bindtap="{{q}}" class="ss-reset-button auto-login-btn data-v-0c4ea268"><image class="auto-login-img data-v-0c4ea268" src="{{p}}"/></button><button wx:if="{{r}}" bindtap="{{t}}" class="ss-reset-button auto-login-btn data-v-0c4ea268"><image class="auto-login-img data-v-0c4ea268" src="{{s}}"/></button></view><view wx:if="{{v}}" class="{{['agreement-box', 'ss-flex', 'ss-row-center', 'data-v-0c4ea268', B && 'shake']}}"><label class="radio ss-flex ss-col-center data-v-0c4ea268" bindtap="{{A}}"><radio class="data-v-0c4ea268" checked="{{w}}" color="#1088F0" style="transform:scale(0.8)" catchtap="{{x}}"/><view class="agreement-text ss-flex ss-col-center ss-m-l-8 data-v-0c4ea268"> 已阅读并同意税眸的 <view class="tcp-text data-v-0c4ea268" catchtap="{{y}}"> 《服务协议》 </view><view class="agreement-text data-v-0c4ea268">和</view><view class="tcp-text data-v-0c4ea268" catchtap="{{z}}"> 《隐私政策》 </view></view></label></view><view class="safe-box data-v-0c4ea268"/></view>