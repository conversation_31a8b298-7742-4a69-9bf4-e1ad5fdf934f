package cn.iocoder.yudao.module.trade.convert.brokerage;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.trade.controller.admin.brokerage.vo.withdraw.BrokerageWithdrawPageReqVO;
import cn.iocoder.yudao.module.trade.controller.admin.brokerage.vo.withdraw.BrokerageWithdrawRespVO;
import cn.iocoder.yudao.module.trade.controller.app.brokerage.vo.withdraw.AppBrokerageWithdrawCreateReqVO;
import cn.iocoder.yudao.module.trade.controller.app.brokerage.vo.withdraw.AppBrokerageWithdrawPageReqVO;
import cn.iocoder.yudao.module.trade.controller.app.brokerage.vo.withdraw.AppBrokerageWithdrawRespVO;
import cn.iocoder.yudao.module.trade.dal.dataobject.brokerage.BrokerageWithdrawDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-31T15:14:22+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
public class BrokerageWithdrawConvertImpl implements BrokerageWithdrawConvert {

    @Override
    public BrokerageWithdrawDO convert(AppBrokerageWithdrawCreateReqVO createReqVO, Long userId, Integer feePrice) {
        if ( createReqVO == null && userId == null && feePrice == null ) {
            return null;
        }

        BrokerageWithdrawDO.BrokerageWithdrawDOBuilder brokerageWithdrawDO = BrokerageWithdrawDO.builder();

        if ( createReqVO != null ) {
            brokerageWithdrawDO.price( createReqVO.getPrice() );
            brokerageWithdrawDO.type( createReqVO.getType() );
            brokerageWithdrawDO.name( createReqVO.getName() );
            brokerageWithdrawDO.accountNo( createReqVO.getAccountNo() );
            brokerageWithdrawDO.bankName( createReqVO.getBankName() );
            brokerageWithdrawDO.bankAddress( createReqVO.getBankAddress() );
            brokerageWithdrawDO.accountQrCodeUrl( createReqVO.getAccountQrCodeUrl() );
        }
        brokerageWithdrawDO.userId( userId );
        brokerageWithdrawDO.feePrice( feePrice );

        return brokerageWithdrawDO.build();
    }

    @Override
    public BrokerageWithdrawRespVO convert(BrokerageWithdrawDO bean) {
        if ( bean == null ) {
            return null;
        }

        BrokerageWithdrawRespVO brokerageWithdrawRespVO = new BrokerageWithdrawRespVO();

        brokerageWithdrawRespVO.setUserId( bean.getUserId() );
        brokerageWithdrawRespVO.setPrice( bean.getPrice() );
        brokerageWithdrawRespVO.setFeePrice( bean.getFeePrice() );
        brokerageWithdrawRespVO.setTotalPrice( bean.getTotalPrice() );
        brokerageWithdrawRespVO.setType( bean.getType() );
        brokerageWithdrawRespVO.setName( bean.getName() );
        brokerageWithdrawRespVO.setAccountNo( bean.getAccountNo() );
        brokerageWithdrawRespVO.setBankName( bean.getBankName() );
        brokerageWithdrawRespVO.setBankAddress( bean.getBankAddress() );
        brokerageWithdrawRespVO.setAccountQrCodeUrl( bean.getAccountQrCodeUrl() );
        brokerageWithdrawRespVO.setStatus( bean.getStatus() );
        brokerageWithdrawRespVO.setAuditReason( bean.getAuditReason() );
        brokerageWithdrawRespVO.setAuditTime( bean.getAuditTime() );
        brokerageWithdrawRespVO.setRemark( bean.getRemark() );
        brokerageWithdrawRespVO.setId( bean.getId() );
        brokerageWithdrawRespVO.setCreateTime( bean.getCreateTime() );

        return brokerageWithdrawRespVO;
    }

    @Override
    public List<BrokerageWithdrawRespVO> convertList(List<BrokerageWithdrawDO> list) {
        if ( list == null ) {
            return null;
        }

        List<BrokerageWithdrawRespVO> list1 = new ArrayList<BrokerageWithdrawRespVO>( list.size() );
        for ( BrokerageWithdrawDO brokerageWithdrawDO : list ) {
            list1.add( convert( brokerageWithdrawDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<BrokerageWithdrawRespVO> convertPage(PageResult<BrokerageWithdrawDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<BrokerageWithdrawRespVO> pageResult = new PageResult<BrokerageWithdrawRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PageResult<AppBrokerageWithdrawRespVO> convertPage02(PageResult<BrokerageWithdrawDO> pageResult) {
        if ( pageResult == null ) {
            return null;
        }

        PageResult<AppBrokerageWithdrawRespVO> pageResult1 = new PageResult<AppBrokerageWithdrawRespVO>();

        pageResult1.setList( brokerageWithdrawDOListToAppBrokerageWithdrawRespVOList( pageResult.getList() ) );
        pageResult1.setTotal( pageResult.getTotal() );

        return pageResult1;
    }

    @Override
    public BrokerageWithdrawPageReqVO convert(AppBrokerageWithdrawPageReqVO pageReqVO, Long userId) {
        if ( pageReqVO == null && userId == null ) {
            return null;
        }

        BrokerageWithdrawPageReqVO brokerageWithdrawPageReqVO = new BrokerageWithdrawPageReqVO();

        if ( pageReqVO != null ) {
            brokerageWithdrawPageReqVO.setPageNo( pageReqVO.getPageNo() );
            brokerageWithdrawPageReqVO.setPageSize( pageReqVO.getPageSize() );
            brokerageWithdrawPageReqVO.setType( pageReqVO.getType() );
            brokerageWithdrawPageReqVO.setStatus( pageReqVO.getStatus() );
        }
        brokerageWithdrawPageReqVO.setUserId( userId );

        return brokerageWithdrawPageReqVO;
    }

    protected AppBrokerageWithdrawRespVO brokerageWithdrawDOToAppBrokerageWithdrawRespVO(BrokerageWithdrawDO brokerageWithdrawDO) {
        if ( brokerageWithdrawDO == null ) {
            return null;
        }

        AppBrokerageWithdrawRespVO appBrokerageWithdrawRespVO = new AppBrokerageWithdrawRespVO();

        appBrokerageWithdrawRespVO.setId( brokerageWithdrawDO.getId() );
        appBrokerageWithdrawRespVO.setStatus( brokerageWithdrawDO.getStatus() );
        appBrokerageWithdrawRespVO.setPrice( brokerageWithdrawDO.getPrice() );
        appBrokerageWithdrawRespVO.setCreateTime( brokerageWithdrawDO.getCreateTime() );

        return appBrokerageWithdrawRespVO;
    }

    protected List<AppBrokerageWithdrawRespVO> brokerageWithdrawDOListToAppBrokerageWithdrawRespVOList(List<BrokerageWithdrawDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppBrokerageWithdrawRespVO> list1 = new ArrayList<AppBrokerageWithdrawRespVO>( list.size() );
        for ( BrokerageWithdrawDO brokerageWithdrawDO : list ) {
            list1.add( brokerageWithdrawDOToAppBrokerageWithdrawRespVO( brokerageWithdrawDO ) );
        }

        return list1;
    }
}
