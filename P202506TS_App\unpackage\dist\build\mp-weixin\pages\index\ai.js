"use strict";const e=require("../../common/vendor.js"),a=require("../../sheep/api/ai/chat/message.js"),t=require("../../sheep/api/ai/chat/conversation.js"),s=require("../../sheep/index.js");if(!Array){(e.resolveComponent("Icon")+e.resolveComponent("su-switch")+e.resolveComponent("ConversationUpdateForm")+e.resolveComponent("s-layout"))()}Math||(l+o+v+u+n+(()=>"../../sheep/ui/su-switch/su-switch.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const l=()=>"./components/ai/conversation/ConversationList.js",n=()=>"./components/ai/message/MessageList.js",u=()=>"./components/ai/message/MessageListEmpty.js",o=()=>"./components/ai/message/MessageLoading.js",v=()=>"./components/ai/message/MessageNewConversation.js",r={__name:"ai",setup(l){e.useCssVars((a=>({"03911408":e.unref(r)})));const{safeAreaInsets:n,safeArea:u}=s.sheep.$platform.device;s.sheep.$platform.navbar;const o=0*s.sheep.$platform.device.statusBarHeight*2,v=2*(u.height+n.bottom)+o-s.sheep.$platform.navbar-350,r=s.sheep.$url.css("/assets/mp/index/bg_header.png"),i=e.computed((()=>{var e;return null==(e=s.sheep.$store("app").template)?void 0:e.home})),c=e.ref(),p=e.ref(null),h=e.ref(null),m=e.ref(!1),d=e.ref(),f=e.ref([]),g=e.ref(!1),y=e.ref(),w=e.ref(50),C=e.ref(!1),b=e.ref(!1),T=e.ref(),M=e.ref(),$=e.ref(),I=e.ref(!0),j=e.ref(""),_=e.ref(""),x=async e=>m.value?(s.sheep.$helper.toast("对话中，不允许切换!"),!1):(p.value=e.id,h.value=e,await q(),$.value="",!0),A=async e=>{p.value===e.id&&await L()},L=async()=>{if(m.value)return s.sheep.$helper.toast("对话中，不允许切换!"),!1;p.value=null,h.value=null,f.value=[]},k=e.ref(),D=async()=>{await(async e=>{if(!e)return;const{data:a}=await t.ChatConversationApi.getChatConversationMy(e),s=a;s&&(h.value=s,p.value=s.id)})(p.value)},S=async()=>{await c.value.createConversation()},B=async()=>{console.log("handleConversationCreateSuccess"),$.value=""},q=async()=>{try{if(null===p.value)return;y.value=setTimeout((()=>{g.value=!0}),60);const{data:e}=await a.ChatMessageApi.getChatMessageListByConversationId(p.value);f.value=e}finally{y.value&&clearTimeout(y.value),g.value=!1,setTimeout((()=>{z()}),600)}},N=e.computed((()=>{var e;return f.value.length>0?f.value:(null==(e=h.value)?void 0:e.systemMessage)?[{id:0,type:"system",content:h.value.systemMessage}]:[]})),R=()=>{m.value?s.sheep.$helper.toast("回答中，不能删除!"):q()},V=async e=>{var a;if(b.value)return;if(m.value)return;const t=null==(a=$.value)?void 0:a.trim();"Enter"===e.key&&(e.shiftKey?($.value+="\r\n",e.preventDefault()):(await J(t),e.preventDefault()))},E=()=>{var e;J(null==(e=$.value)?void 0:e.trim())},F=e=>{if(!b.value){if(null==e.data)return;b.value=!0}M.value&&clearTimeout(M.value),M.value=setTimeout((()=>{b.value=!1}),400)},H=()=>{b.value=!0},U=()=>{setTimeout((()=>{b.value=!1}),200)},J=async e=>{e.length<1?s.sheep.$helper.toast("发送失败，原因：内容为空！"):null!=p.value?($.value="",await K({conversationId:p.value,content:e})):s.sheep.$helper.toast("还没创建对话，不能发送!")},K=async t=>{T.value=new AbortController,m.value=!0,j.value="";try{f.value.push({id:-1,conversationId:p.value,type:"user",content:t.content,createTime:new Date}),f.value.push({id:-2,conversationId:p.value,type:"assistant",content:"思考中...",createTime:new Date}),await e.nextTick$1(),G();let s=!0;await a.ChatMessageApi.sendChatMessageStreamCaishui(t.conversationId,t.content,T.value,I.value,(async e=>{const{code:a,data:t,msg:l}=JSON.parse(e.trim());if(0!==a)return console.log("对话异常",a,t,l),f.value.pop(),void f.value.push({id:-2,conversationId:p.value,type:"assistant",content:"服务器忙，请稍后再试",createTime:new Date});""!==t.receive.content&&(s&&(s=!1,f.value.pop(),f.value.pop(),f.value.push(t.send),f.value.push(t.receive)),j.value=j.value+t.receive.content,await z())}),(e=>{console.log("对话异常-error: ",e),f.value.pop(),f.value.push({id:-2,conversationId:p.value,type:"assistant",content:"服务器忙，请稍后再试",createTime:new Date}),O()}),(()=>{O()}))}catch{}},O=async()=>{T.value&&T.value.abort(),m.value=!1},P=e=>{$.value=e.content},Q=e=>{J(e.content)},z=async a=>{e.nextTick$1((()=>{d.value&&d.value.scrollToBottom(a)}))},G=async()=>{let e=0;try{if(C.value)return;C.value=!0,_.value="";const a=async()=>{const s=(j.value.length-_.value.length)/10;if(w.value=s>5?10:s>2?30:s>1.5?50:100,m.value||(w.value=10),e<j.value.length){_.value+=j.value[e],e++;f.value[f.value.length-1].content=_.value,await z(),t=setTimeout(a,w.value)}else m.value?t=setTimeout(a,w.value):(C.value=!1,clearTimeout(t))};let t=setTimeout(a,w.value)}catch{}};return e.onLoad((async e=>{g.value=!0,await q()})),(a,t)=>e.e({a:e.s({marginTop:Number(o)+"rpx"}),b:e.sr(c,"12e40b1c-1,12e40b1c-0",{k:"conversationListRef"}),c:e.o(B),d:e.o(x),e:e.o(L),f:e.o(A),g:e.p({"active-id":p.value})},{},{t:g.value},(g.value,{}),{v:!h.value},h.value?{}:{w:e.o(S)},{x:!g.value&&0===N.value.length&&h.value},!g.value&&0===N.value.length&&h.value?{y:e.o(J)}:{},{z:!g.value&&N.value.length>0},!g.value&&N.value.length>0?{A:e.sr(d,"12e40b1c-9,12e40b1c-0",{k:"messageRef"}),B:e.o(R),C:e.o(P),D:e.o(Q),E:e.p({conversation:h.value,list:N.value})}:{},{F:e.o(V),G:e.o([e=>$.value=e.detail.value,F]),H:e.o(H),I:e.o(U),J:$.value,K:e.o((e=>I.value=e)),L:e.p({modelValue:I.value}),M:0==m.value},0==m.value?{N:e.t(m.value?"进行中":"发送"),O:e.o(E),P:m.value}:{},{Q:1==m.value},1==m.value?{R:e.o((e=>O()))}:{},{S:e.sr(k,"12e40b1c-11,12e40b1c-0",{k:"conversationUpdateFormRef"}),T:e.o(D),U:v+"rpx",V:e.s(a.__cssVars()),W:e.p({title:"Q智星AI",tabbar:"/pages/index/ai",navbar:"custom",bgStyle:i.value.page,navbarStyle:i.value.navigationBar,onShareAppMessage:!0})})}},i=e._export_sfc(r,[["__scopeId","data-v-12e40b1c"]]);r.__runtimeHooks=2,wx.createPage(i);
