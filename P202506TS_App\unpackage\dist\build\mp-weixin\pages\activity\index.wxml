<s-layout wx:if="{{j}}" u-s="{{['d']}}" class="activity-wrap data-v-19929d6c" u-i="19929d6c-0" bind:__l="__l" u-p="{{j}}"><su-sticky wx:if="{{d}}" class="data-v-19929d6c" u-s="{{['d']}}" u-i="19929d6c-1,19929d6c-0" bind:__l="__l" u-p="{{d}}"><view class="ss-flex ss-col-top tip-box data-v-19929d6c"><view class="type-text ss-flex ss-row-center data-v-19929d6c">满减：</view><view class="ss-flex-1 data-v-19929d6c"><view wx:for="{{a}}" wx:for-item="item" wx:key="b" class="tip-content data-v-19929d6c">{{item.a}}</view></view><image class="activity-left-image data-v-19929d6c" src="{{b}}"/><image class="activity-right-image data-v-19929d6c" src="{{c}}"/></view></su-sticky><view class="ss-flex ss-flex-wrap ss-p-x-20 ss-m-t-20 ss-col-top data-v-19929d6c"><view class="goods-list-box data-v-19929d6c"><view wx:for="{{e}}" wx:for-item="item" wx:key="e" class="left-list data-v-19929d6c"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-19929d6c" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-19929d6c" slot="cart"></button></s-goods-column></view></view><view class="goods-list-box data-v-19929d6c"><view wx:for="{{f}}" wx:for-item="item" wx:key="e" class="right-list data-v-19929d6c"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-19929d6c" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-19929d6c" slot="cart"/></s-goods-column></view></view></view><uni-load-more wx:if="{{g}}" class="data-v-19929d6c" bindtap="{{h}}" u-i="19929d6c-4,19929d6c-0" bind:__l="__l" u-p="{{i}}"/></s-layout>