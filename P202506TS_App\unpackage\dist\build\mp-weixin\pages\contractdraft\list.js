"use strict";const t=require("../../common/vendor.js"),e=require("../../sheep/index.js"),o=require("../../sheep/util/index.js"),a=require("../../sheep/api/member/contractDraft.js");if(require("../../sheep/config/index.js"),!Array){(t.resolveComponent("su-tabs")+t.resolveComponent("su-sticky")+t.resolveComponent("uni-list-item")+t.resolveComponent("uni-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-empty")+t.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const n={__name:"list",setup(n){const s=t.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:6},contractId:"",status:"",keyword:"",tabMaps:[{name:"全部",value:""},{name:"有效",value:"有效"},{name:"废弃",value:"废弃"}],loadStatus:""});function i(){o.resetPagination(s.pagination)}function r(t){s.keyword=t,i(),u()}function c(t){i(),s.currentTab=t.index,s.status=s.tabMaps[t.index].value,u()}async function u(){s.loadStatus="loading";const{code:e,data:o}=await a.ContractDraftApi.getContractDraftPage({pageNo:s.pagination.pageNo,pageSize:s.pagination.pageSize,status:s.status,contractId:s.contractId,draftName:s.keyword});0===e&&(s.pagination.list=t.lodash.concat(s.pagination.list,o.list),s.pagination.total=o.total,s.loadStatus=s.pagination.list.length<s.pagination.total?"more":"noMore")}function p(){"noMore"!==s.loadStatus&&(s.pagination.pageNo++,u())}const l=async(o,n)=>{t.index.showModal({title:"提示",content:`点击确定更新稿件为${n}`,success:async function(t){if(!t.confirm)return;const{code:s}=await a.ContractDraftApi.updateContractDraftStatus(o,n);0===s&&(e.sheep.$helper.toast(`更新为${n}成功`),await r())}})};return t.onLoad((t=>{s.contractId=t.id,s.keyword=t.keyword,u()})),t.onReachBottom((()=>{p()})),t.onPullDownRefresh((()=>{u(),setTimeout((function(){t.index.stopPullDownRefresh()}),800)})),(n,i)=>t.e({a:t.o(c),b:t.p({list:s.tabMaps,scrollable:!1,current:s.currentTab}),c:t.p({bgColor:"#fff"}),d:s.pagination.total>0},s.pagination.total>0?{e:t.f(s.pagination.list,((n,i,c)=>({a:t.t(n.draftType||"草稿"),b:t.n("正式稿"===n.draftType?"orange":"gray"),c:t.t(n.status||"有效"),d:t.n("有效"===n.status?"orange":"gray"),e:t.t(n.draftName||"未命名稿件"),f:t.t(t.unref(o.formatDate)(n.createTime,"YYYY.MM.DD")),g:t.o((t=>{return o=n.id,void e.sheep.$router.go("/pages/contractdraft/detail",{id:o});var o}),i),h:t.o((t=>(async t=>{e.sheep.$helper.toast("编辑")})(n.id)),i),i:t.o((t=>l(n.id,"正式稿")),i),j:t.o((e=>(n.id,void t.index.showModal({content:"请前往电脑端进行操作",showCancel:!1}))),i),k:t.o((t=>{return o=n.id,void e.sheep.$router.go("/pages/contractdraft/reportList",{contractId:s.contractId,draftId:o});var o}),i),l:t.o((t=>l(n.id,"废弃")),i),m:t.o((o=>(async o=>{t.index.showModal({title:"提示",content:"确认删除此稿件吗？",success:async function(t){if(!t.confirm)return;const{code:n}=await a.ContractDraftApi.deleteContractDraft(o);0===n&&(e.sheep.$helper.toast("删除成功"),await r())}})})(n.id)),i),n:i,o:"0cedbb53-4-"+c+",0cedbb53-3"})))}:{},{f:s.pagination.total>0},s.pagination.total>0?{g:t.o(p),h:t.p({status:s.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{i:0===s.pagination.total},0===s.pagination.total?{j:t.p({icon:t.unref(e.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无稿件"})}:{},{k:t.p({title:"稿件管理"})})}},s=t._export_sfc(n,[["__scopeId","data-v-0cedbb53"]]);wx.createPage(s);
