"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),s=require("../../sheep/hooks/useGoods.js"),t=require("../../sheep/api/promotion/seckill.js"),n=require("../../sheep/api/product/spu.js"),i=require("../../sheep/util/const.js");if(!Array){(e.resolveComponent("s-empty")+e.resolveComponent("su-swiper")+e.resolveComponent("s-select-seckill-sku")+e.resolveComponent("s-layout"))()}Math||(a+c+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/ui/su-swiper/su-swiper.js")+p+l+(()=>"../../sheep/components/s-select-seckill-sku/s-select-seckill-sku.js")+r+d+u+(()=>"../../sheep/components/s-layout/s-layout.js"))();const a=()=>"./components/detail/detail-navbar.js",l=()=>"./components/detail/detail-cell-sku.js",u=()=>"./components/detail/detail-tabbar.js",c=()=>"./components/detail/detail-skeleton.js",r=()=>"./components/detail/detail-comment-card.js",d=()=>"./components/detail/detail-content-card.js",p=()=>"./components/detail/detail-progress.js",g={__name:"seckill",setup(a){e.useCssVars((o=>({"73d00b06":e.unref(l),"7c504cd6":e.unref(u),"21972a8c":e.unref(c),"5c668b2e":e.unref(r),"9d73d7d0":e.unref(d)})));const l=o.sheep.$url.css("/static/img/shop/goods/seckill-bg.png"),u=o.sheep.$url.css("/static/img/shop/goods/seckill-btn.png"),c=o.sheep.$url.css("/static/img/shop/goods/activity-btn-disabled.png"),r=o.sheep.$url.css("/static/img/shop/goods/seckill-tip-bg.png"),d=o.sheep.$url.css("/static/img/shop/goods/groupon-tip-bg.png");e.onPageScroll((()=>{}));const p=e.reactive({skeletonLoading:!0,goodsInfo:{},showSelectSku:!1,goodsSwiper:[],selectedSku:{},showModel:!1,total:0,percent:0,price:""}),g=e.computed((()=>s.useDurationTime(v.value.endTime)));function m(e){p.selectedSku=e}function f(e){o.sheep.$router.go("/pages/order/confirm",{data:JSON.stringify({order_type:"goods",buy_type:"seckill",seckillActivityId:v.value.id,items:[{skuId:e.id,count:e.count}]})})}const k=e.computed((()=>e.isEmpty(e.unref(v))?{}:o.sheep.$platform.share.getShareInfo({title:v.value.name,image:o.sheep.$url.cdn(p.goodsInfo.picUrl),params:{page:i.SharePageEnum.SECKILL.value,query:v.value.id}},{type:"goods",title:v.value.name,image:o.sheep.$url.cdn(p.goodsInfo.picUrl),price:s.fen2yuan(p.goodsInfo.price),marketPrice:s.fen2yuan(p.goodsInfo.marketPrice)}))),v=e.ref(),h=e.ref(""),I=async o=>{const{data:t}=await n.SpuApi.getSpuDetail(o);t.activity_type="seckill",p.goodsInfo=t,p.goodsSwiper=s.formatGoodsSwiper(p.goodsInfo.sliderPicUrls),p.goodsInfo.price=e.min([p.goodsInfo.price,...v.value.products.map((e=>e.seckillPrice))]),t.skus.forEach((e=>{const o=v.value.products.find((o=>o.skuId===e.id));o?(e.price=o.seckillPrice,e.stock=Math.min(e.stock,o.stock)):e.stock=0,v.value.totalLimitCount>0&&v.value.singleLimitCount>0?e.limitCount=Math.min(v.value.totalLimitCount,v.value.singleLimitCount):v.value.totalLimitCount>0?e.limitCount=v.value.totalLimitCount:v.value.singleLimitCount>0&&(e.limitCount=v.value.singleLimitCount)})),p.skeletonLoading=!1};return e.onLoad((e=>{e.id?(async e=>{const{data:o}=await t.SeckillApi.getSeckillActivity(e);v.value=o,h.value=i.getTimeStatusEnum(v.value.startTime,v.value.endTime),p.percent=100-o.stock/o.totalStock*100,await I(o.spuId)})(e.id):p.goodsInfo=null})),(t,n)=>e.e({a:p.skeletonLoading},p.skeletonLoading?{}:null===p.goodsInfo||"seckill"!==p.goodsInfo.activity_type||g.value.ms<=0?{c:e.p({text:"活动不存在或已结束",icon:"/static/soldout-empty.png",showAction:!0,actionText:"再逛逛",actionUrl:"/pages/goods/list"})}:e.e({d:e.p({isPreview:!0,list:p.goodsSwiper,dotStyle:"tag",imageMode:"widthFix",dotCur:"bg-mask-40",seizeHeight:750}),e:e.t(e.unref(s.fen2yuan)(p.selectedSku.price||p.goodsInfo.price)),f:g.value.ms>0},g.value.ms>0?{g:e.t(g.value.h),h:e.t(g.value.m),i:e.t(g.value.s)}:{},{j:p.goodsInfo.marketPrice},p.goodsInfo.marketPrice?{k:e.t(e.unref(s.fen2yuan)(p.selectedSku.marketPrice||p.goodsInfo.marketPrice))}:{},{l:e.p({percent:p.percent}),m:e.t(p.goodsInfo.name||""),n:e.t(p.goodsInfo.introduction),o:e.o((e=>p.showSelectSku=!0)),p:e.p({sku:p.selectedSku}),q:e.o(f),r:e.o(m),s:e.o((e=>p.showSelectSku=!1)),t:e.o((e=>p.goodsInfo=e)),v:e.p({show:p.showSelectSku,"single-limit-count":v.value.singleLimitCount,modelValue:p.goodsInfo}),w:e.p({goodsId:p.goodsInfo.id}),x:e.p({content:p.goodsInfo.description}),y:p.goodsInfo.marketPrice},p.goodsInfo.marketPrice?{z:e.t(e.unref(s.fen2yuan)(p.goodsInfo.marketPrice)),A:e.o((s=>e.unref(o.sheep).$router.go("/pages/goods/index",{id:p.goodsInfo.id})))}:{B:e.n((0===p.goodsInfo.stock||(h.value,e.unref(i.TimeStatusEnum).STARTED),""))},{C:e.t(e.unref(s.fen2yuan)(p.goodsInfo.price)),D:h.value===e.unref(i.TimeStatusEnum).STARTED},h.value===e.unref(i.TimeStatusEnum).STARTED?e.e({E:0===p.goodsInfo.stock},(p.goodsInfo.stock,{})):{F:e.t(h.value)},{G:e.o((e=>p.showSelectSku=!0)),H:e.n(h.value===e.unref(i.TimeStatusEnum).STARTED&&0!=p.goodsInfo.stock?"check-btn-box":"disabled-btn-box"),I:0===p.goodsInfo.stock||h.value!==e.unref(i.TimeStatusEnum).STARTED,J:e.o((e=>p.goodsInfo=e)),K:e.p({modelValue:p.goodsInfo})}),{b:null===p.goodsInfo||"seckill"!==p.goodsInfo.activity_type||g.value.ms<=0,L:e.s(t.__cssVars()),M:e.p({onShareAppMessage:k.value,navbar:"goods"})})}},m=e._export_sfc(g,[["__scopeId","data-v-8e9189f5"]]);g.__runtimeHooks=3,wx.createPage(m);
