"use strict";const e=require("../../../common/vendor.js"),t=require("../../../sheep/index.js"),a=require("../../../sheep/api/member/point.js"),o=require("../../../sheep/util/index.js");if(!Array){(e.resolveComponent("uni-datetime-picker")+e.resolveComponent("su-tabs")+e.resolveComponent("su-sticky")+e.resolveComponent("s-empty")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.js")+(()=>"../../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const n={__name:"score",setup(n){const i=2*t.sheep.$platform.device.statusBarHeight,s=e.computed((()=>t.sheep.$store("user").userInfo)),p=t.sheep.$platform.navbar,r=e.reactive({currentTab:0,pagination:{list:0,total:0,pageSize:6,pageNo:1},loadStatus:"",date:[],today:""}),u=[{name:"全部",value:"all"},{name:"收入",value:"true"},{name:"支出",value:"false"}],d=e.computed((()=>r.date[0]===r.date[1]?r.date[0]:r.date.join("~")));async function l(){r.loadStatus="loading";let{code:t,data:o}=await a.PointApi.getPointRecordPage({pageNo:r.pagination.pageNo,pageSize:r.pagination.pageSize,addStatus:r.currentTab>0?u[r.currentTab].value:void 0,"createTime[0]":r.date[0]+" 00:00:00","createTime[1]":r.date[1]+" 23:59:59"});0===t&&(r.pagination.list=e.lodash.concat(r.pagination.list,o.list),r.pagination.total=o.total,r.loadStatus=r.pagination.list.length<r.pagination.total?"more":"noMore")}function c(e){r.currentTab=e.index,o.resetPagination(r.pagination),l()}function m(e){r.date[0]=e[0],r.date[1]=e[e.length-1],o.resetPagination(r.pagination),l()}function g(){"noMore"!==r.loadStatus&&(r.pagination.pageNo++,l())}return e.onLoad((()=>{r.today=e.dayjs().format("YYYY-MM-DD"),r.date=[r.today,r.today],l()})),e.onReachBottom((()=>{g()})),(a,o)=>e.e({a:e.t(s.value.point||0),b:e.s({marginTop:"-"+Number(i+88)+"rpx",paddingTop:Number(i+88)+"rpx"}),c:e.t(d.value),d:e.o(m),e:e.o((e=>r.date=e)),f:e.p({type:"daterange",end:r.today,modelValue:r.date}),g:e.o(c),h:e.p({list:u,scrollable:!1,current:r.currentTab}),i:e.p({customNavHeight:e.unref(p)}),j:r.pagination.total>0},r.pagination.total>0?{k:e.f(r.pagination.list,((a,o,n)=>e.e({a:e.t(a.title),b:e.t(a.description?" - "+a.description:""),c:e.t(e.unref(t.sheep).$helper.timeFormat(a.createTime,"yyyy-mm-dd hh:MM:ss")),d:a.point>0},a.point>0?{e:e.t(a.point)}:{f:e.t(a.point)},{g:a.id})))}:{l:e.p({text:"暂无数据",icon:"/static/data-empty.png"})},{m:r.pagination.total>0},r.pagination.total>0?{n:e.o(g),o:e.p({status:r.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{p:e.p({title:"我的积分",navbar:"inner"})})}},i=e._export_sfc(n,[["__scopeId","data-v-6494e4ec"]]);wx.createPage(i);
