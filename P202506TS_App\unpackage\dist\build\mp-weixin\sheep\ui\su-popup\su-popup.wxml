<view wx:if="{{a}}" class="{{['uni-popup', p, q]}}" style="{{r + ';' + s}}" catchtouchmove="{{t}}"><view bindtouchstart="{{o}}"><uni-transition wx:if="{{b}}" key="1" bindclick="{{c}}" u-i="3986499c-0" bind:__l="__l" u-p="{{d}}"/><uni-transition wx:if="{{n}}" u-s="{{['d']}}" key="2" bindclick="{{m}}" u-i="3986499c-1" bind:__l="__l" u-p="{{n}}"><view wx:if="{{e}}" style="{{i + ';' + j}}" class="{{['uni-popup__wrapper', k]}}" bindtap="{{l}}"><uni-icons wx:if="{{f}}" class="close-icon" bindclick="{{g}}" u-i="3986499c-2,3986499c-1" bind:__l="__l" u-p="{{h}}"></uni-icons><slot/></view></uni-transition></view></view><view wx:else style="{{'display:none' + ';' + v}}"><slot></slot></view>