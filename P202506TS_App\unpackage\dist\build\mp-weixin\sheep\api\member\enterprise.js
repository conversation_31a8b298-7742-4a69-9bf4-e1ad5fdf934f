"use strict";const e=require("../../request/index.js"),r={getByTianYanCha:r=>e.request({url:"/member/enterprise/get-by-tianyancha",method:"GET",params:{name:r},custom:{showLoading:!1}}),enterpriseRegister:r=>e.request({url:"/member/enterprise/enterprise-register",method:"POST",data:r,custom:{showSuccess:!0,loadingMsg:"注册中"}}),getEnterpriseList:()=>e.request({url:"/member/enterprise/list",method:"GET"})};exports.EnterpriseApi=r;
