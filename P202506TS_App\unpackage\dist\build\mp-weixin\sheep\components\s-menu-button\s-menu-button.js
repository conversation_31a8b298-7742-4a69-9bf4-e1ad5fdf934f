"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),a={__name:"s-menu-button",props:{data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})},circular:{type:Boolean,default:!0},autoplay:{type:Boolean,default:!1},interval:{type:Number,default:5e3},duration:{type:Number,default:500},ui:{type:String,default:""},mode:{type:String,default:"default"},dotStyle:{type:String,default:"long"},dotCur:{type:String,default:"ui-BG-Main"},height:{type:Number,default:300},hasBorder:{type:Boolean,default:!0},borderColor:{type:String,default:"red"},background:{type:String,default:"blue"},hoverClass:{type:String,default:"ss-hover-class"},col:{type:[Number,String],default:3},iconSize:{type:Number,default:80},color:{type:String,default:"#000"}},setup(a){const o=e.reactive({cur:0}),r=a,l=e.computed((()=>{const{bgType:e,bgImg:t,bgColor:a}=r.styles;return{background:"img"===e?`url(${t}) no-repeat top center / 100% 100%`:a}})),u=e.computed((()=>c(r.data.list,r.data.row*r.data.column))),d=e.computed((()=>r.data.row*("iconText"===r.data.layout?200:180)));t.sheep.$platform.device.windowWidth;const n=e=>{o.cur=e.detail.current},c=(e=[],t=1)=>{let a=[],o=[];return e.forEach((e=>{o.length===t&&(o=[]),0===o.length&&a.push(o),o.push(e)})),a};return(c,i)=>e.e({a:e.f(u.value,((l,u,d)=>({a:e.f(l,((o,l,u)=>e.e({a:o.badge.show},o.badge.show?{b:e.t(o.badge.text),c:e.s({background:o.badge.bgColor,color:o.badge.textColor})}:{},{d:o.iconUrl},o.iconUrl?{e:e.s({width:r.iconSize+"rpx",height:r.iconSize+"rpx"}),f:e.unref(t.sheep).$url.cdn(o.iconUrl)}:{},"iconText"===a.data.layout?{g:e.t(o.title),h:e.s({color:o.titleColor})}:{},{i:l,j:e.o((a=>e.unref(t.sheep).$router.go(o.url)),l)}))),b:u,c:o.cur==u?1:""}))),b:"iconText"===a.data.layout,c:e.s({width:1/a.data.column*100+"%",height:"200rpx"}),d:r.circular,e:o.cur,f:r.autoplay,g:r.interval,h:r.duration,i:e.s({height:d.value+"rpx"}),j:e.o(n),k:u.value.length>1},u.value.length>1?e.e({l:"tag"!=r.dotStyle},"tag"!=r.dotStyle?{m:e.f(u.value.length,((t,a,r)=>({a:a,b:e.n(o.cur==a?"cur":"")}))),n:e.n(r.dotCur),o:e.n(r.dotStyle)}:{},{p:"tag"==r.dotStyle},"tag"==r.dotStyle?{q:e.t(o.cur+1),r:e.t(u.value.length),s:e.n(r.dotCur),t:e.n(r.dotStyle)}:{}):{},{v:e.n(r.mode),w:e.n(r.ui),x:e.s(l.value),y:e.s({height:d.value+(u.value.length>1?50:0)+"rpx"})})}},o=e._export_sfc(a,[["__scopeId","data-v-9f5543a4"]]);wx.createComponent(o);
