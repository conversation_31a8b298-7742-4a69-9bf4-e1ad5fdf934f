"use strict";const t=require("../../../common/vendor.js"),e=require("../../../common/assets.js"),o=require("../../index.js"),i=require("../../hooks/useGoods.js"),a=require("../../util/const.js"),d={__name:"s-goods-column",props:{goodsFields:{type:[Array,Object],default:()=>({price:{show:!0},stock:{show:!0},name:{show:!0},introduction:{show:!0},marketPrice:{show:!0},salesCount:{show:!0}})},tagStyle:{type:Object,default:()=>({})},data:{type:Object,default:()=>({})},size:{type:String,default:"sl"},background:{type:String,default:""},topRadius:{type:Number,default:0},bottomRadius:{type:Number,default:0},titleWidth:{type:Number,default:0},titleColor:{type:String,default:"#333"},priceColor:{type:String,default:""},originPriceColor:{type:String,default:"#C4C4C4"},priceUnit:{type:String,default:"￥"},subTitleColor:{type:String,default:"#999999"},subTitleBackground:{type:String,default:""},buttonShow:{type:Boolean,default:!0},seckillTag:{type:Boolean,default:!1},grouponTag:{type:Boolean,default:!1}},emits:["click","getHeight"],setup(d,{emit:r}){const l=d,s=t.computed((()=>{const t=l.data.promotionType;return 4===t?"限时优惠":6===t?"会员价":void 0})),n=t.computed((()=>({background:l.background,"border-top-left-radius":l.topRadius+"px","border-top-right-radius":l.topRadius+"px","border-bottom-left-radius":l.bottomRadius+"px","border-bottom-right-radius":l.bottomRadius+"px"}))),u=t.computed((()=>{var t,e;let o=[];return(null==(t=l.goodsFields.salesCount)?void 0:t.show)&&(l.data.activityType&&l.data.activityType===a.PromotionActivityTypeEnum.POINT.type?o.push(i.formatExchange(l.data.sales_show_type,(l.data.pointTotalStock||0)-(l.data.pointStock||0))):o.push(i.formatSales(l.data.sales_show_type,l.data.salesCount))),(null==(e=l.goodsFields.stock)?void 0:e.show)&&(l.data.activityType&&l.data.activityType===a.PromotionActivityTypeEnum.POINT.type?o.push(i.formatStock(l.data.stock_show_type,l.data.pointTotalStock)):o.push(i.formatStock(l.data.stock_show_type,l.data.stock))),o.join(" | ")})),c=r,p=()=>{c("click")},{proxy:g}=t.getCurrentInstance(),y=`sheep_${Math.ceil(1e6*Math.random()).toString(36)}`;return t.onMounted((()=>{t.nextTick$1((()=>{!function(){if("md"===l.size){const e=t.index.createSelectorQuery().in(g);e.select(`#${y}`).fields({size:!0,scrollOffset:!0}),e.exec((t=>{let e=0;const o=t[0];e=l.data.image_wh?o.width/l.data.image_wh.w*l.data.image_wh.h+o.height:o.width,c("getHeight",e)}))}}()}))})),(r,l)=>{var c,g,v,m,h,w,f,b,T,F,P,k,S,A,_,$,U,C,I,x,O,R,N,z,E,j,B,W,q,D,M,H,G,Q,J,K,L,V,X,Y,Z,tt,et,ot,it,at,dt,rt,lt,st,nt,ut,ct,pt,gt,yt,vt,mt,ht,wt,ft,bt,Tt,Ft,Pt,kt,St,At,_t,$t,Ut,Ct;return t.e({a:"xs"===d.size},"xs"===d.size?t.e({b:d.tagStyle.show},d.tagStyle.show?{c:t.unref(o.sheep).$url.cdn(d.tagStyle.src||d.tagStyle.imgUrl)}:{},{d:t.unref(o.sheep).$url.cdn(d.data.image||d.data.picUrl),e:(null==(c=d.goodsFields.title)?void 0:c.show)||(null==(g=d.goodsFields.name)?void 0:g.show)||(null==(v=d.goodsFields.price)?void 0:v.show)},(null==(m=d.goodsFields.title)?void 0:m.show)||(null==(h=d.goodsFields.name)?void 0:h.show)||(null==(w=d.goodsFields.price)?void 0:w.show)?t.e({f:(null==(f=d.goodsFields.title)?void 0:f.show)||(null==(b=d.goodsFields.name)?void 0:b.show)},(null==(T=d.goodsFields.title)?void 0:T.show)||(null==(F=d.goodsFields.name)?void 0:F.show)?{g:t.t(d.data.title||d.data.name),h:t.s({color:d.titleColor,width:d.titleWidth?d.titleWidth+"rpx":""})}:{},{i:d.data.promotionType>0||d.data.rewardActivity},d.data.promotionType>0||d.data.rewardActivity?t.e({j:s.value},s.value?{k:t.t(s.value)}:{},{l:t.f(t.unref(i.getRewardActivityRuleItemDescriptions)(d.data.rewardActivity).slice(0,1),((e,o,i)=>({a:t.t(e),b:e})))}):{},{m:null==(P=d.goodsFields.price)?void 0:P.show},(null==(k=d.goodsFields.price)?void 0:k.show)?t.e({n:d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type},d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type?{o:t.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),p:t.t(d.data.point),q:t.t(d.data.pointPrice&&0!==d.data.pointPrice?`+${d.priceUnit}${t.unref(i.fen2yuan)(d.data.pointPrice)}`:"")}:t.e({r:t.t(d.priceUnit),s:d.data.promotionPrice>0},d.data.promotionPrice>0?{t:t.t(t.unref(i.fen2yuan)(d.data.promotionPrice))}:{v:t.t(t.unref(t.isArray)(d.data.price)?t.unref(i.fen2yuan)(d.data.price[0]):t.unref(i.fen2yuan)(d.data.price))}),{w:t.s({color:d.goodsFields.price.color})}):{}):{},{x:t.s(n.value),y:t.o(p)}):{},{z:"sm"===d.size},"sm"===d.size?t.e({A:d.tagStyle.show},d.tagStyle.show?{B:t.unref(o.sheep).$url.cdn(d.tagStyle.src||d.tagStyle.imgUrl)}:{},{C:t.unref(o.sheep).$url.cdn(d.data.image||d.data.picUrl),D:(null==(S=d.goodsFields.title)?void 0:S.show)||(null==(A=d.goodsFields.name)?void 0:A.show)||(null==(_=d.goodsFields.price)?void 0:_.show)},(null==($=d.goodsFields.title)?void 0:$.show)||(null==(U=d.goodsFields.name)?void 0:U.show)||(null==(C=d.goodsFields.price)?void 0:C.show)?t.e({E:(null==(I=d.goodsFields.title)?void 0:I.show)||(null==(x=d.goodsFields.name)?void 0:x.show)},(null==(O=d.goodsFields.title)?void 0:O.show)||(null==(R=d.goodsFields.name)?void 0:R.show)?{F:t.t(d.data.title||d.data.name)}:{},{G:d.data.promotionType>0||d.data.rewardActivity},d.data.promotionType>0||d.data.rewardActivity?t.e({H:s.value},s.value?{I:t.t(s.value)}:{},{J:t.f(t.unref(i.getRewardActivityRuleItemDescriptions)(d.data.rewardActivity).slice(0,1),((e,o,i)=>({a:t.t(e),b:e})))}):{},{K:null==(N=d.goodsFields.price)?void 0:N.show},(null==(z=d.goodsFields.price)?void 0:z.show)?t.e({L:d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type},d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type?{M:t.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),N:t.t(d.data.point),O:t.t(d.data.pointPrice&&0!==d.data.pointPrice?`+${d.priceUnit}${t.unref(i.fen2yuan)(d.data.pointPrice)}`:"")}:t.e({P:t.t(d.priceUnit),Q:d.data.promotionPrice>0},d.data.promotionPrice>0?{R:t.t(t.unref(i.fen2yuan)(d.data.promotionPrice))}:{S:t.t(t.unref(t.isArray)(d.data.price)?t.unref(i.fen2yuan)(d.data.price[0]):t.unref(i.fen2yuan)(d.data.price))}),{T:t.s({color:d.goodsFields.price.color})}):{},{U:t.s({color:d.titleColor,width:d.titleWidth?d.titleWidth+"rpx":""})}):{},{V:t.s(n.value),W:t.o(p)}):{},{X:"md"===d.size},"md"===d.size?t.e({Y:d.tagStyle.show},d.tagStyle.show?{Z:t.unref(o.sheep).$url.cdn(d.tagStyle.src||d.tagStyle.imgUrl)}:{},{aa:t.unref(o.sheep).$url.cdn(d.data.image||d.data.picUrl),ab:(null==(E=d.goodsFields.title)?void 0:E.show)||(null==(j=d.goodsFields.name)?void 0:j.show)},(null==(B=d.goodsFields.title)?void 0:B.show)||(null==(W=d.goodsFields.name)?void 0:W.show)?{ac:t.t(d.data.title||d.data.name),ad:t.s({color:d.titleColor,width:d.titleWidth?d.titleWidth+"rpx":""})}:{},{ae:(null==(q=d.goodsFields.subtitle)?void 0:q.show)||(null==(D=d.goodsFields.introduction)?void 0:D.show)},(null==(M=d.goodsFields.subtitle)?void 0:M.show)||(null==(H=d.goodsFields.introduction)?void 0:H.show)?{af:t.t(d.data.subtitle||d.data.introduction),ag:t.s({color:d.subTitleColor,background:d.subTitleBackground})}:{},{ah:null==(G=d.data.promos)?void 0:G.length},(null==(Q=d.data.promos)?void 0:Q.length)?{ai:t.f(d.data.promos,((e,o,i)=>({a:t.t(e.title),b:e.id})))}:{},{aj:d.data.promotionType>0||d.data.rewardActivity},d.data.promotionType>0||d.data.rewardActivity?t.e({ak:s.value},s.value?{al:t.t(s.value)}:{},{am:t.f(t.unref(i.getRewardActivityRuleItemDescriptions)(d.data.rewardActivity).slice(0,1),((e,o,i)=>({a:t.t(e),b:e})))}):{},{an:null==(J=d.goodsFields.price)?void 0:J.show},(null==(K=d.goodsFields.price)?void 0:K.show)?t.e({ao:d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type},d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type?{ap:t.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),aq:t.t(d.data.point),ar:t.t(d.data.pointPrice&&0!==d.data.pointPrice?`+${d.priceUnit}${t.unref(i.fen2yuan)(d.data.pointPrice)}`:"")}:t.e({as:t.t(d.priceUnit),at:d.data.promotionPrice>0},d.data.promotionPrice>0?{av:t.t(t.unref(i.fen2yuan)(d.data.promotionPrice))}:{aw:t.t(t.unref(t.isArray)(d.data.price)?t.unref(i.fen2yuan)(d.data.price[0]):t.unref(i.fen2yuan)(d.data.price))}),{ax:t.s({color:d.goodsFields.price.color})}):{},{ay:((null==(L=d.goodsFields.original_price)?void 0:L.show)||(null==(V=d.goodsFields.marketPrice)?void 0:V.show))&&(d.data.original_price>0||d.data.marketPrice>0)},((null==(X=d.goodsFields.original_price)?void 0:X.show)||(null==(Y=d.goodsFields.marketPrice)?void 0:Y.show))&&(d.data.original_price>0||d.data.marketPrice>0)?{az:t.t(d.priceUnit),aA:t.t(t.unref(i.fen2yuan)(d.data.marketPrice)),aB:t.s({color:d.originPriceColor})}:{},{aC:t.t(u.value),aD:y,aE:e._imports_0$2,aF:t.s(n.value),aG:t.o(p)}):{},{aH:"lg"===d.size},"lg"===d.size?t.e({aI:d.tagStyle.show},d.tagStyle.show?{aJ:t.unref(o.sheep).$url.cdn(d.tagStyle.src||d.tagStyle.imgUrl)}:{},{aK:d.seckillTag},(d.seckillTag,{}),{aL:d.grouponTag},(d.grouponTag,{}),{aM:t.unref(o.sheep).$url.cdn(d.data.image||d.data.picUrl),aN:(null==(Z=d.goodsFields.title)?void 0:Z.show)||(null==(tt=d.goodsFields.name)?void 0:tt.show)},(null==(et=d.goodsFields.title)?void 0:et.show)||(null==(ot=d.goodsFields.name)?void 0:ot.show)?{aO:t.t(d.data.title||d.data.name),aP:t.s({color:d.titleColor})}:{},{aQ:(null==(it=d.goodsFields.subtitle)?void 0:it.show)||(null==(at=d.goodsFields.introduction)?void 0:at.show)},(null==(dt=d.goodsFields.subtitle)?void 0:dt.show)||(null==(rt=d.goodsFields.introduction)?void 0:rt.show)?{aR:t.t(d.data.subtitle||d.data.introduction),aS:t.s({color:d.subTitleColor,background:d.subTitleBackground})}:{},{aT:null==(lt=d.data.promos)?void 0:lt.length},(null==(st=d.data.promos)?void 0:st.length)?{aU:t.f(d.data.promos,((e,o,i)=>({a:t.t(e.title),b:e.id})))}:{},{aV:d.data.promotionType>0||d.data.rewardActivity},d.data.promotionType>0||d.data.rewardActivity?t.e({aW:s.value},s.value?{aX:t.t(s.value)}:{},{aY:t.f(t.unref(i.getRewardActivityRuleItemDescriptions)(d.data.rewardActivity).slice(0,1),((e,o,i)=>({a:t.t(e),b:e})))}):{},{aZ:null==(nt=d.goodsFields.price)?void 0:nt.show},(null==(ut=d.goodsFields.price)?void 0:ut.show)?t.e({ba:d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type},d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type?{bb:t.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),bc:t.t(d.data.point),bd:t.t(d.data.pointPrice&&0!==d.data.pointPrice?`+${d.priceUnit}${t.unref(i.fen2yuan)(d.data.pointPrice)}`:"")}:t.e({be:t.t(d.priceUnit),bf:d.data.promotionPrice>0},d.data.promotionPrice>0?{bg:t.t(t.unref(i.fen2yuan)(d.data.promotionPrice))}:{bh:t.t(t.unref(t.isArray)(d.data.price)?t.unref(i.fen2yuan)(d.data.price[0]):t.unref(i.fen2yuan)(d.data.price))}),{bi:t.s({color:d.goodsFields.price.color}),bj:((null==(ct=d.goodsFields.original_price)?void 0:ct.show)||(null==(pt=d.goodsFields.marketPrice)?void 0:pt.show))&&(d.data.original_price>0||d.data.marketPrice>0)},((null==(gt=d.goodsFields.original_price)?void 0:gt.show)||(null==(yt=d.goodsFields.marketPrice)?void 0:yt.show))&&(d.data.original_price>0||d.data.marketPrice>0)?{bk:t.t(d.priceUnit),bl:t.t(t.unref(i.fen2yuan)(d.data.marketPrice)),bm:t.s({color:d.originPriceColor})}:{}):{},{bn:t.t(u.value),bo:d.buttonShow},(d.buttonShow,{}),{bp:t.s(n.value),bq:t.o(p)}):{},{br:"sl"===d.size},"sl"===d.size?t.e({bs:d.tagStyle.show},d.tagStyle.show?{bt:t.unref(o.sheep).$url.cdn(d.tagStyle.src||d.tagStyle.imgUrl)}:{},{bv:t.unref(o.sheep).$url.cdn(d.data.image||d.data.picUrl),bw:(null==(vt=d.goodsFields.title)?void 0:vt.show)||(null==(mt=d.goodsFields.name)?void 0:mt.show)},(null==(ht=d.goodsFields.title)?void 0:ht.show)||(null==(wt=d.goodsFields.name)?void 0:wt.show)?{bx:t.t(d.data.title||d.data.name),by:t.s({color:d.titleColor})}:{},{bz:(null==(ft=d.goodsFields.subtitle)?void 0:ft.show)||(null==(bt=d.goodsFields.introduction)?void 0:bt.show)},(null==(Tt=d.goodsFields.subtitle)?void 0:Tt.show)||(null==(Ft=d.goodsFields.introduction)?void 0:Ft.show)?{bA:t.t(d.data.subtitle||d.data.introduction),bB:t.s({color:d.subTitleColor,background:d.subTitleBackground})}:{},{bC:null==(Pt=d.data.promos)?void 0:Pt.length},(null==(kt=d.data.promos)?void 0:kt.length)?{bD:t.f(d.data.promos,((e,o,i)=>({a:t.t(e.title),b:e.id})))}:{},{bE:d.data.promotionType>0||d.data.rewardActivity},d.data.promotionType>0||d.data.rewardActivity?t.e({bF:s.value},s.value?{bG:t.t(s.value)}:{},{bH:t.f(t.unref(i.getRewardActivityRuleItemDescriptions)(d.data.rewardActivity).slice(0,1),((e,o,i)=>({a:t.t(e),b:e})))}):{},{bI:null==(St=d.goodsFields.price)?void 0:St.show},(null==(At=d.goodsFields.price)?void 0:At.show)?t.e({bJ:d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type},d.data.activityType&&d.data.activityType===t.unref(a.PromotionActivityTypeEnum).POINT.type?{bK:t.unref(o.sheep).$url.static("/static/img/shop/goods/score1.svg"),bL:t.t(d.data.point),bM:t.t(d.data.pointPrice&&0!==d.data.pointPrice?`+${d.priceUnit}${t.unref(i.fen2yuan)(d.data.pointPrice)}`:"")}:t.e({bN:t.t(d.priceUnit),bO:d.data.promotionPrice>0},d.data.promotionPrice>0?{bP:t.t(t.unref(i.fen2yuan)(d.data.promotionPrice))}:{bQ:t.t(t.unref(t.isArray)(d.data.price)?t.unref(i.fen2yuan)(d.data.price[0]):t.unref(i.fen2yuan)(d.data.price))}),{bR:t.s({color:d.goodsFields.price.color}),bS:((null==(_t=d.goodsFields.original_price)?void 0:_t.show)||(null==($t=d.goodsFields.marketPrice)?void 0:$t.show))&&(d.data.original_price>0||d.data.marketPrice>0)},((null==(Ut=d.goodsFields.original_price)?void 0:Ut.show)||(null==(Ct=d.goodsFields.marketPrice)?void 0:Ct.show))&&(d.data.original_price>0||d.data.marketPrice>0)?{bT:t.t(d.priceUnit),bU:t.t(t.unref(i.fen2yuan)(d.data.marketPrice)),bV:t.s({color:d.originPriceColor})}:{}):{},{bW:t.t(u.value),bX:t.s(n.value),bY:t.o(p)}):{})}}},r=t._export_sfc(d,[["__scopeId","data-v-9b817759"]]);wx.createComponent(r);
