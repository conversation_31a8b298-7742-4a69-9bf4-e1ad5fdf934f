"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),n=require("../../sheep/hooks/useGoods.js"),t=require("../../sheep/api/trade/config.js"),a=require("../../sheep/api/trade/brokerage.js"),c=require("../../sheep/api/system/dict.js");if(!Array){(e.resolveComponent("uni-easyinput")+e.resolveComponent("s-uploader"))()}Math||((()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js")+(()=>"../../sheep/components/s-uploader/s-uploader.js")+r+s)();const r=()=>"./components/account-type-select.js",s=()=>"../../sheep/components/s-layout/s-layout.js",u={__name:"withdraw",setup(r){e.useCssVars((o=>({"6bd45142":e.unref(s)})));const s=o.sheep.$url.css("/static/img/shop/user/withdraw_bg.png"),u=2*o.sheep.$platform.device.statusBarHeight,i=o.sheep.$store("user");e.computed((()=>i.userInfo));const p=e.reactive({accountInfo:{type:void 0,accountNo:void 0,accountQrCodeUrl:void 0,name:void 0,bankName:void 0,bankAddress:void 0},accountSelect:!1,brokerageInfo:{},frozenDays:0,minPrice:0,withdrawTypes:[],bankList:[],bankListSelectedIndex:""}),d=e=>{p.accountSelect=e},f=async()=>{if(!p.accountInfo.price||p.accountInfo.price>p.brokerageInfo.price||p.accountInfo.price<=0)return void o.sheep.$helper.toast("请输入正确的提现金额");if(!p.accountInfo.type)return void o.sheep.$helper.toast("请选择提现方式");let{code:n}=await a.BrokerageApi.createBrokerageWithdraw({...p.accountInfo,price:100*p.accountInfo.price});0===n&&e.index.showModal({title:"操作成功",content:"您的提现申请已成功提交",cancelText:"继续提现",confirmText:"查看记录",success:e=>{e.confirm?o.sheep.$router.go("/pages/commission/wallet",{type:2}):(l(),p.accountInfo={})}})};async function l(){const{data:e,code:o}=await a.BrokerageApi.getBrokerageUser();0===o&&(p.brokerageInfo=e)}function y(e){const o=e.detail.value;p.bankListSelectedIndex=o,p.accountInfo.bankName=p.bankList[o].label}return e.onBeforeMount((()=>{!async function(){let{code:e,data:o}=await t.TradeConfigApi.getTradeConfig();0===e&&o&&(p.minPrice=o.brokerageWithdrawMinPrice||0,p.frozenDays=o.brokerageFrozenDays||0,p.withdrawTypes=o.brokerageWithdrawTypes)}(),l(),async function(){let{code:e,data:o}=await c.DictApi.getDictDataListByType("brokerage_bank_name");0===e&&o&&o.length>0&&(p.bankList=o)}()})),(t,a)=>e.e({a:e.t(e.unref(n.fen2yuan)(p.brokerageInfo.brokeragePrice)),b:e.o((n=>e.unref(o.sheep).$router.go("/pages/commission/wallet",{type:2}))),c:e.s({marginTop:"-"+Number(u+88)+"rpx",paddingTop:Number(u+108)+"rpx"}),d:!p.accountInfo.type},(p.accountInfo.type,{}),{e:"1"===p.accountInfo.type},(p.accountInfo.type,{}),{f:"2"===p.accountInfo.type},(p.accountInfo.type,{}),{g:"3"===p.accountInfo.type},(p.accountInfo.type,{}),{h:"4"===p.accountInfo.type},(p.accountInfo.type,{}),{i:"5"===p.accountInfo.type},(p.accountInfo.type,{}),{j:e.o((e=>d(!0))),k:e.o((e=>p.accountInfo.price=e)),l:e.p({inputBorder:!1,type:"number",placeholder:"请输入提现金额",modelValue:p.accountInfo.price}),m:["2","3","4","5"].includes(p.accountInfo.type),n:e.o((e=>p.accountInfo.accountNo=e)),o:e.p({inputBorder:!1,placeholder:"请输入提现账号",modelValue:p.accountInfo.accountNo}),p:["2","3","4"].includes(p.accountInfo.type),q:["3","4"].includes(p.accountInfo.type),r:e.o((e=>p.accountInfo.accountQrCodeUrl=e.tempFilePaths[0])),s:e.o((e=>p.accountInfo.accountQrCodeUrl=e)),t:e.p({fileMediatype:"image",limit:"1",mode:"grid",imageStyles:{width:"168rpx",height:"168rpx"},url:p.accountInfo.accountQrCodeUrl}),v:["3","4"].includes(p.accountInfo.type),w:"2"===p.accountInfo.type,x:e.o((e=>p.accountInfo.name=e)),y:e.p({inputBorder:!1,placeholder:"请输入持卡人姓名",modelValue:p.accountInfo.name}),z:"2"===p.accountInfo.type,A:"2"===p.accountInfo.type,B:e.p({inputBorder:!1,value:p.accountInfo.bankName,placeholder:"请选择银行",suffixIcon:"right",disabled:!0,styles:{disableColor:"#fff",borderColor:"#fff",color:"#333!important"}}),C:e.o(y),D:p.bankListSelectedIndex,E:p.bankList,F:"2"===p.accountInfo.type,G:"2"===p.accountInfo.type,H:e.o((e=>p.accountInfo.bankAddress=e)),I:e.p({inputBorder:!1,placeholder:"请输入开户地址",modelValue:p.accountInfo.bankAddress}),J:"2"===p.accountInfo.type,K:e.o(f),L:e.t(e.unref(n.fen2yuan)(p.minPrice)),M:e.t(e.unref(n.fen2yuan)(p.brokerageInfo.frozenPrice)),N:e.t(p.frozenDays),O:e.o((e=>d(!1))),P:e.o((e=>p.accountInfo=e)),Q:e.p({show:p.accountSelect,round:"10",methods:p.withdrawTypes,modelValue:p.accountInfo}),R:e.s(t.__cssVars()),S:e.p({title:"申请提现",navbar:"inner"})})}},i=e._export_sfc(u,[["__scopeId","data-v-c1250a1f"]]);wx.createPage(i);
