"use strict";const e=require("../../../common/vendor.js"),t=require("../../../sheep/index.js"),o=require("../../../sheep/hooks/useGoods.js"),a=require("../../../sheep/util/index.js"),n=require("../../../sheep/api/promotion/combination.js");if(!Array){(e.resolveComponent("su-tabs")+e.resolveComponent("su-sticky")+e.resolveComponent("s-empty")+e.resolveComponent("s-goods-item")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-layout"))()}Math||((()=>"../../../sheep/ui/su-tabs/su-tabs.js")+(()=>"../../../sheep/ui/su-sticky/su-sticky.js")+(()=>"../../../sheep/components/s-empty/s-empty.js")+(()=>"../../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../../sheep/components/s-layout/s-layout.js"))();const i={__name:"order",setup(i){const s=e.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:5},loadStatus:"",deleteOrderId:0}),r=[{name:"全部"},{name:"进行中",value:0},{name:"拼团成功",value:1},{name:"拼团失败",value:2}];function p(e){a.resetPagination(s.pagination),s.currentTab=e.index,u()}async function u(){s.loadStatus="loading";const{code:t,data:o}=await n.CombinationApi.getCombinationRecordPage({pageNo:s.pagination.pageNo,pageSize:s.pagination.pageSize,status:r[s.currentTab].value});0===t&&(s.pagination.list=e.lodash.concat(s.pagination.list,o.list),s.pagination.total=o.total,s.loadStatus=s.pagination.list.length<s.pagination.total?"more":"noMore")}function l(){"noMore"!==s.loadStatus&&(s.pagination.pageNo++,u())}return e.onLoad((e=>{e.type&&(s.currentTab=e.type),u()})),e.onReachBottom((()=>{l()})),e.onPullDownRefresh((()=>{u(),setTimeout((function(){e.index.stopPullDownRefresh()}),800)})),(a,n)=>e.e({a:e.o(p),b:e.p({list:r,scrollable:!1,current:s.currentTab}),c:e.p({bgColor:"#fff"}),d:0===s.pagination.total},0===s.pagination.total?{e:e.p({icon:"/static/goods-empty.png"})}:{},{f:s.pagination.total>0},s.pagination.total>0?{g:e.f(s.pagination.list,((a,n,i)=>({a:e.t(a.id),b:e.t(r.find((e=>e.value===a.status)).name),c:e.n(e.unref(o.formatOrderColor)(a)),d:e.t(a.userSize),e:"0284b1d7-4-"+i+",0284b1d7-0",f:e.p({img:a.picUrl,title:a.spuName,price:a.combinationPrice}),g:e.o((o=>e.unref(t.sheep).$router.go("/pages/order/detail",{id:a.orderId})),a.id),h:e.t(0===a.status?"邀请拼团":"拼团详情"),i:0===a.status?1:"",j:e.o((o=>e.unref(t.sheep).$router.go("/pages/activity/groupon/detail",{id:a.id})),a.id),k:a.id})))}:{},{h:s.pagination.total>0},s.pagination.total>0?{i:e.o(l),j:e.p({status:s.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{k:e.p({title:"我的拼团"})})}},s=e._export_sfc(i,[["__scopeId","data-v-0284b1d7"]]);wx.createPage(s);
