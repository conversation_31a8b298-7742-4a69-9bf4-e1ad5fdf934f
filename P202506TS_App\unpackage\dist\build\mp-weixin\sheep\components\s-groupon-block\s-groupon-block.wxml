<view class="data-v-6c5851a7"><view wx:if="{{a}}" class="goods-sl-box data-v-6c5851a7"><view wx:for="{{b}}" wx:for-item="item" wx:key="d" class="goods-box data-v-6c5851a7" style="{{e}}"><s-goods-column wx:if="{{item.c}}" u-s="{{['cart']}}" class=" data-v-6c5851a7" bindclick="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"><button class="ss-reset-button cart-btn data-v-6c5851a7" style="{{d}}" slot="cart">{{c}}</button></s-goods-column></view></view><view wx:if="{{f}}" class="goods-lg-box data-v-6c5851a7"><view wx:for="{{g}}" wx:for-item="item" wx:key="d" class="goods-box data-v-6c5851a7" style="{{j}}"><s-goods-column wx:if="{{item.c}}" u-s="{{['cart']}}" class="goods-card data-v-6c5851a7" bindtap="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"><button class="ss-reset-button cart-btn data-v-6c5851a7" style="{{i}}" slot="cart">{{h}}</button></s-goods-column></view></view><view wx:if="{{k}}" class="goods-md-wrap ss-flex ss-flex-wrap ss-col-top data-v-6c5851a7"><view class="goods-list-box data-v-6c5851a7"><view wx:for="{{l}}" wx:for-item="item" wx:key="e" class="left-list data-v-6c5851a7" style="{{o}}"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-6c5851a7" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-6c5851a7" style="{{n}}" slot="cart">{{m}}</button></s-goods-column></view></view><view class="goods-list-box data-v-6c5851a7"><view wx:for="{{p}}" wx:for-item="item" wx:key="e" class="right-list data-v-6c5851a7" style="{{s}}"><s-goods-column wx:if="{{item.d}}" u-s="{{['cart']}}" class="goods-md-box data-v-6c5851a7" bindclick="{{item.a}}" bindgetHeight="{{item.b}}" u-i="{{item.c}}" bind:__l="__l" u-p="{{item.d}}"><button class="ss-reset-button cart-btn data-v-6c5851a7" style="{{r}}" slot="cart">{{q}}</button></s-goods-column></view></view></view></view>