"use strict";const t=require("../../common/vendor.js"),e=require("../../sheep/index.js"),o=require("../../sheep/util/index.js"),n=require("../../sheep/api/member/contractSign.js");if(require("../../sheep/config/index.js"),!Array){(t.resolveComponent("uni-list-item")+t.resolveComponent("uni-list")+t.resolveComponent("uni-load-more")+t.resolveComponent("s-empty")+t.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js")+(()=>"../../uni_modules/uni-list/components/uni-list/uni-list.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const a={__name:"list",setup(a){const i=t.reactive({currentTab:0,pagination:{list:[],total:0,pageNo:1,pageSize:6},contractId:"",keyword:"",loadStatus:""});function s(t){i.keyword=t,o.resetPagination(i.pagination),r()}async function r(){i.loadStatus="loading";const{code:e,data:o}=await n.ContractSignApi.getContractSignPage({pageNo:i.pagination.pageNo,pageSize:i.pagination.pageSize,contractId:i.contractId,keyword:i.keyword});0===e&&(i.pagination.list=t.lodash.concat(i.pagination.list,o.list),i.pagination.total=o.total,i.loadStatus=i.pagination.list.length<i.pagination.total?"more":"noMore")}function p(){"noMore"!==i.loadStatus&&(i.pagination.pageNo++,r())}return t.onLoad((t=>{i.contractId=t.id,i.keyword=t.keyword,r()})),t.onReachBottom((()=>{p()})),t.onPullDownRefresh((()=>{r(),setTimeout((function(){t.index.stopPullDownRefresh()}),800)})),(a,r)=>t.e({a:i.pagination.total>0},i.pagination.total>0?{b:t.f(i.pagination.list,((a,i,r)=>({a:t.t(a.seqLabel),b:t.t(a.signType),c:t.t(a.fullName),d:t.t(a.userName),e:t.t(a.userMobile),f:t.t(a.certType),g:t.t(a.certNo),h:t.t(t.unref(o.formatDate)(a.createTime,"YYYY.MM.DD")),i:t.o((t=>{return o=a.id,void e.sheep.$router.go("/pages/contractsign/detail",{id:o,type:"detail"});var o}),i),j:t.o((t=>(async t=>{e.sheep.$helper.toast("编辑")})(a.id)),i),k:t.o((t=>{return o=a.id,void e.sheep.$router.go("/pages/enterprisepartnerthird/list",{partnerId:o});var o}),i),l:t.o((o=>(async o=>{t.index.showModal({title:"提示",content:"确认删除此签署方吗？",success:async function(t){if(!t.confirm)return;const{code:a}=await n.ContractSignApi.deleteContractSign(o);0===a&&(e.sheep.$helper.toast("删除成功"),await s())}})})(a.id)),i),m:i,n:"e28a1cea-2-"+r+",e28a1cea-1"})))}:{},{c:i.pagination.total>0},i.pagination.total>0?{d:t.o(p),e:t.p({status:i.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{f:0===i.pagination.total},0===i.pagination.total?{g:t.p({icon:t.unref(e.sheep).$url.static("/assets/mp/order/empty.png"),text:"暂无签署方"})}:{},{h:t.p({title:"签署方"})})}},i=t._export_sfc(a,[["__scopeId","data-v-e28a1cea"]]);wx.createPage(i);
