"use strict";const o=require("../../request/index.js"),e={getCouponTemplateListByIds:e=>o.request({url:"/promotion/coupon-template/list-by-ids",method:"GET",params:{ids:e},custom:{showLoading:!1,showError:!1}}),getCouponTemplateList:(e,t,u)=>o.request({url:"/promotion/coupon-template/list",method:"GET",params:{spuId:e,productScope:t,count:u}}),getCouponTemplatePage:e=>o.request({url:"/promotion/coupon-template/page",method:"GET",params:e}),getCouponTemplate:e=>o.request({url:"/promotion/coupon-template/get",method:"GET",params:{id:e}}),getCouponPage:e=>o.request({url:"/promotion/coupon/page",method:"GET",params:e}),takeCoupon:e=>o.request({url:"/promotion/coupon/take",method:"POST",data:{templateId:e},custom:{auth:!0,showLoading:!0,loadingMsg:"领取中",showSuccess:!0,successMsg:"领取成功"}}),getCoupon:e=>o.request({url:"/promotion/coupon/get",method:"GET",params:{id:e}}),getUnusedCouponCount:()=>o.request({url:"/promotion/coupon/get-unused-count",method:"GET",custom:{showLoading:!1,auth:!0}})};exports.CouponApi=e;
