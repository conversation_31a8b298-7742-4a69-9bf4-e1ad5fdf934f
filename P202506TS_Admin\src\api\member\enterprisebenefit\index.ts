import request from '@/config/axios'

// 企业权益 VO
export interface EnterpriseBenefitVO {
  id: number // 主键
  enterpriseId: number // 企业编号
  benefitId: number // 权益编号
  benefitCode: string // 权益代码
  benefitName: string // 权益名称
  benefitTotal: number // 总量
  benefitBalance: number // 余量
  benefitUnit: string // 单位
  createTime: string // 创建时间
}

// 企业权益 API
export const EnterpriseBenefitApi = {
  // 查询企业权益分页
  getEnterpriseBenefitPage: async (params: any) => {
    return await request.get({ url: `/member/enterprise-benefit/page`, params })
  },

  // 查询企业权益详情
  getEnterpriseBenefit: async (id: number) => {
    return await request.get({ url: `/member/enterprise-benefit/get?id=` + id })
  },

  // 初始化企业权益
  initEnterpriseBenefit: async (enterpriseId: number, spuId: number) => {
    return await request.post({
      url: `/member/enterprise-benefit/init-enterprise-benefit?enterpriseId=${enterpriseId}&spuId=${spuId}`
    })
  },

  // 新增企业权益
  createEnterpriseBenefit: async (data: EnterpriseBenefitVO) => {
    return await request.post({ url: `/member/enterprise-benefit/create`, data })
  },

  // 修改企业权益
  updateEnterpriseBenefit: async (data: EnterpriseBenefitVO) => {
    return await request.put({ url: `/member/enterprise-benefit/update`, data })
  },

  // 删除企业权益
  deleteEnterpriseBenefit: async (id: number) => {
    return await request.delete({ url: `/member/enterprise-benefit/delete?id=` + id })
  },

  // 导出企业权益 Excel
  exportEnterpriseBenefit: async (params) => {
    return await request.download({ url: `/member/enterprise-benefit/export-excel`, params })
  }
}
