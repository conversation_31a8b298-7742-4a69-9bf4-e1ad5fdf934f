"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js"),o=require("../../sheep/api/product/favorite.js"),i=require("../../sheep/util/index.js");if(!Array){(e.resolveComponent("s-goods-item")+e.resolveComponent("su-fixed")+e.resolveComponent("uni-load-more")+e.resolveComponent("s-empty")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-goods-item/s-goods-item.js")+(()=>"../../sheep/ui/su-fixed/su-fixed.js")+(()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js")+(()=>"../../sheep/components/s-empty/s-empty.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const a={__name:"goods-collect",setup(a){e.useCssVars((t=>({"2658b38c":e.unref(s)})));const s=t.sheep.$platform.navbar,l=e.reactive({pagination:{list:[],total:0,pageNo:1,pageSize:6},loadStatus:"",editMode:!1,selectedCollectList:[],selectAll:!1});async function n(){l.loadStatus="loading";const{code:t,data:i}=await o.FavoriteApi.getFavoritePage({pageNo:l.pagination.pageNo,pageSize:l.pagination.pageSize});0===t&&(l.pagination.list=e.lodash.concat(l.pagination.list,i.list),l.pagination.total=i.total,l.loadStatus=l.pagination.list.length<l.pagination.total?"more":"noMore")}const d=e=>{l.selectedCollectList.includes(e)?l.selectedCollectList.splice(l.selectedCollectList.indexOf(e),1):l.selectedCollectList.push(e),l.selectAll=l.selectedCollectList.length===l.pagination.list.length},p=()=>{l.selectAll=!l.selectAll,l.selectAll?l.selectedCollectList=l.pagination.list.map((e=>e.spuId)):l.selectedCollectList=[]};async function c(){if(l.selectedCollectList){for(const e of l.selectedCollectList)await o.FavoriteApi.deleteFavorite(e);l.editMode=!1,l.selectedCollectList=[],l.selectAll=!1,i.resetPagination(l.pagination),await n()}}function r(){"noMore"!==l.loadStatus&&(l.pagination.pageNo++,n())}return e.onReachBottom((()=>{r()})),e.onLoad((()=>{n()})),(o,i)=>e.e({a:e.t(l.pagination.total),b:l.editMode&&l.pagination.total},l.editMode&&l.pagination.total?{c:e.o((e=>l.editMode=!1))}:{},{d:!l.editMode&&l.pagination.total},!l.editMode&&l.pagination.total?{e:e.o((e=>l.editMode=!0))}:{},{f:e.f(l.pagination.list,((o,i,a)=>e.e(l.editMode?{a:l.selectedCollectList.includes(o.spuId),b:e.o((e=>d(o.spuId)),o.id),c:e.o((e=>d(o.spuId)),o.id)}:{},{d:e.o((i=>e.unref(t.sheep).$router.go("/pages/goods/index",{id:o.spuId})),o.id),e:"00fb8a85-1-"+a+",00fb8a85-0",f:e.p({title:o.spuName,img:o.picUrl,price:o.price,priceColor:"#FF3000",titleWidth:400}),g:o.id}))),g:l.editMode,h:l.selectAll,i:e.o(p),j:e.o(p),k:e.o(c),l:l.editMode,m:e.p({bottom:!0,val:0,placeholder:!0}),n:l.pagination.total>0},l.pagination.total>0?{o:e.o(r),p:e.p({status:l.loadStatus,"content-text":{contentdown:"上拉加载更多"}})}:{},{q:0===l.pagination.total},0===l.pagination.total?{r:e.p({text:"暂无收藏",icon:"/static/collect-empty.png"})}:{},{s:e.s(o.__cssVars()),t:e.p({title:"商品收藏"})})}},s=e._export_sfc(a,[["__scopeId","data-v-00fb8a85"]]);wx.createPage(s);
