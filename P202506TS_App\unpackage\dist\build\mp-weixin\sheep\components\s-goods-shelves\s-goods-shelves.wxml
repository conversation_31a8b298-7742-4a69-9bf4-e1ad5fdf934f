<view class="data-v-32be2126"><view wx:if="{{a}}" class="goods-xs-box ss-flex ss-flex-wrap data-v-32be2126" style="{{d}}"><view wx:for="{{b}}" wx:for-item="item" wx:key="d" class="goods-xs-list data-v-32be2126" style="{{c}}"><s-goods-column wx:if="{{item.c}}" class="goods-card data-v-32be2126" bindclick="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"/></view></view><view wx:if="{{e}}" class="goods-sm-box ss-flex ss-flex-wrap data-v-32be2126" style="{{h}}"><view wx:for="{{f}}" wx:for-item="item" wx:key="d" class="goods-card-box data-v-32be2126" style="{{g}}"><s-goods-column wx:if="{{item.c}}" class="goods-card data-v-32be2126" bindclick="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"/></view></view><view wx:if="{{i}}" class=" data-v-32be2126"><scroll-view class="scroll-box goods-scroll-box data-v-32be2126" scroll-x scroll-anchoring><view class="goods-box ss-flex data-v-32be2126"><view wx:for="{{j}}" wx:for-item="item" wx:key="d" class="goods-card-box data-v-32be2126" style="{{k}}"><s-goods-column wx:if="{{item.c}}" class="goods-card data-v-32be2126" bindclick="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"/></view></view></scroll-view></view></view>