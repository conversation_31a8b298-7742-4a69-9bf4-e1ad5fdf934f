"use strict";const e=require("../../../common/vendor.js"),r=require("../../config/index.js"),t=require("../../request/index.js"),o={getReportCwfxPage:e=>t.request({url:"/member/report-cwfx/page",method:"GET",params:e}),getReportCwfx:e=>t.request({url:"/member/report-cwfx/get?id="+e,method:"GET"}),createReportCwfx:e=>t.request({url:"/member/report-cwfx/create",method:"POST",data:e}),getStatus:e=>t.request({url:"/member/report-cwfx/get-status?enterpriseId="+e,method:"GET"}),updateStatus:e=>t.request({url:"/member/report-cwfx/update-status",method:"PUT",data:e,custom:{loadingMsg:"开通中",showSuccess:!0,successMsg:"开通成功"}}),postOrder:t=>(e.index.getStorageSync("token"),e.index.showLoading({title:"上传中"}),new Promise(((o,s)=>{e.index.uploadFile({url:r.baseUrl+r.apiPath+"/member/report-cwfx/post-order",filePath:t.file,name:"file",formData:t,header:{Accept:"*/*","tenant-id":r.tenantId,Authorization:e.index.getStorageSync("token")},success:r=>{let t=JSON.parse(r.data);if(1!==t.error)return o(t);e.index.showToast({icon:"none",title:t.msg})},fail:e=>(console.log("上传失败：",e),o(!1)),complete:()=>{e.index.hideLoading()}})}))),syncOrder:e=>t.request({url:"/member/report-cwfx/sync?enterpriseId="+e,method:"POST"}),getFileUrl:(e,r)=>t.request({url:"/member/report-cwfx/download-bg",method:"GET",params:{enterpriseId:e,id:r}}),updateReportCwfx:e=>t.request({url:"/member/report-cwfx/update",method:"PUT",data:e}),deleteReportCwfx:e=>t.request({url:"/member/report-cwfx/delete?id="+e,method:"DELETE"}),exportReportCwfx:e=>t.request({url:"/member/report-cwfx/export-excel",method:"GET",params:e})};exports.ReportCwfxApi=o;
