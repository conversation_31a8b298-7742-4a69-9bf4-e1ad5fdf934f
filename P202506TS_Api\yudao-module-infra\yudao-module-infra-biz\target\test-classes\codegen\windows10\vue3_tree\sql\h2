-- 将该建表 SQL 语句，添加到 yudao-module-infra-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "infra_category" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar NOT NULL,
    "description" bigint NOT NULL,
    PRIMARY KEY ("id")
) COMMENT '分类表';

-- 将该删表 SQL 语句，添加到 yudao-module-infra-biz 模块的 test/resources/sql/clean.sql 文件里
DELETE FROM "infra_category";