"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js");if(!Array){e.resolveComponent("uni-tag")()}Math;const r={__name:"s-address-item",props:{item:{type:Object,default(){}},hasBorderBottom:{type:Boolean,defult:!0}},setup(r){const o=r,s=()=>{t.sheep.$router.go("/pages/user/address/edit",{id:o.item.id})};return(r,i)=>e.e({a:!e.unref(e.isEmpty)(o.item)},e.unref(e.isEmpty)(o.item)?{}:e.e({b:o.item.defaultStatus},o.item.defaultStatus?{c:e.p({size:"small","custom-style":"background-color: var(--ui-BG-Main); border-color: var(--ui-BG-Main); color: #fff;",text:"默认"})}:{},{d:e.t(o.item.areaName),e:e.t(o.item.detailAddress),f:e.t(o.item.name),g:e.t(o.item.mobile)}),{h:e.unref(t.sheep).$url.static("/static/img/shop/user/address/edit.png"),i:e.o(s),j:e.n({"border-bottom":o.hasBorderBottom})})}},o=e._export_sfc(r,[["__scopeId","data-v-b80e6bc0"]]);wx.createComponent(o);
