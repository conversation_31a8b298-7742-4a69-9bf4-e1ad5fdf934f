"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),o={__name:"s-coupon-card",props:{list:{type:Array,default:()=>[{title:"已领取",value:"0",icon:"/static/img/shop/order/nouse_coupon.png",path:"/pages/coupon/list",type:"geted"},{title:"已使用",value:"0",icon:"/static/img/shop/order/useend_coupon.png",path:"/pages/coupon/list",type:"used"},{title:"已失效",value:"0",icon:"/static/img/shop/order/out_coupon.png",path:"/pages/coupon/list",type:"expired"},{title:"领券中心",value:"0",icon:"/static/img/shop/order/all_coupon.png",path:"/pages/coupon/list",type:"all"}]},data:{type:Object,default:()=>({})},styles:{type:Object,default:()=>({})}},setup(o){const p=o,s=e.computed((()=>{const{bgType:e,bgImg:t,bgColor:o}=p.styles;return{background:"img"===e?`url(${t}) no-repeat top center / 100% 100%`:o}}));return(a,n)=>({a:e.f(p.list,((o,p,s)=>({a:e.unref(t.sheep).$url.static(o.icon),b:e.t(o.title),c:o.title,d:e.o((p=>e.unref(t.sheep).$router.go(o.path,{type:o.type})),o.title),e:e.n("all"===o.type?"menu-wallet":"ss-flex-1")}))),b:e.s(s.value),c:e.s({marginLeft:`${o.data.space}px`})})}},p=e._export_sfc(o,[["__scopeId","data-v-702d5d4e"]]);wx.createComponent(p);
