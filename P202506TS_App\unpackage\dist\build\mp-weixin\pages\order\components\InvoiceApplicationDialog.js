"use strict";const e=require("../../../common/vendor.js"),r=require("../../../sheep/api/trade/order.js"),a=require("../../../sheep/index.js"),t={name:"InvoiceApplicationDialog",props:{show:{type:Boolean,default:!1},orderId:{type:Number,default:0}},emits:["update:show","success"],setup(t,{emit:o}){const n=e.ref(!1),i=e.reactive({subject:"技术服务",buyerName:"",buyerTaxNo:"",buyerPhone:"",buyerAddress:"",buyerBankAccount:"",buyerBankName:"",invType:!0,titleType:1});e.watch((()=>t.show),(e=>{e&&p()})),e.watch((()=>i.invType),(e=>{e?p():s()})),e.watch((()=>i.titleType),(e=>{i.invType||(1===e?p():s())}));const p=()=>{const e=a.sheep.$store("user").userInfo;e&&(i.buyerName=e.enterpriseName||"",i.buyerPhone=e.mobile||"",e.enterprise&&(i.buyerTaxNo=e.enterprise.taxNo||"",i.buyerAddress=e.enterprise.address||"",i.buyerBankAccount=e.enterprise.bankAccount||"",i.buyerBankName=e.enterprise.bankName||""))},s=()=>{i.buyerName="",i.buyerTaxNo="",i.buyerPhone="",i.buyerAddress="",i.buyerBankAccount="",i.buyerBankName=""},y=()=>{o("update:show",!1),u()},u=()=>{Object.assign(i,{subject:"技术服务",buyerName:"",buyerTaxNo:"",buyerPhone:"",buyerAddress:"",buyerBankAccount:"",buyerBankName:"",invType:!0,titleType:1})};return{formData:i,loading:n,onClose:y,onSubmit:async()=>{if((()=>{if(!i.buyerName.trim())return a.sheep.$helper.toast("请输入购方名称"),!1;if(!i.invType&&!i.titleType)return a.sheep.$helper.toast("请选择发票抬头类型"),!1;if(i.invType){if(!i.buyerTaxNo.trim())return a.sheep.$helper.toast("请输入购方税号"),!1;if(!i.buyerPhone.trim())return a.sheep.$helper.toast("请输入购方电话"),!1;if(!i.buyerAddress.trim())return a.sheep.$helper.toast("请输入购方地址"),!1;if(!i.buyerBankAccount.trim())return a.sheep.$helper.toast("请输入购方银行账号"),!1;if(!i.buyerBankName.trim())return a.sheep.$helper.toast("请输入购方银行名称"),!1}else if(!i.invType&&1===i.titleType&&!i.buyerTaxNo.trim())return a.sheep.$helper.toast("请输入购方税号"),!1;return!0})())if(t.orderId){n.value=!0;try{const e={orderId:t.orderId,...i},{code:p}=await r.OrderApi.applyOrderInvoice(e);0===p&&(a.sheep.$helper.toast("发票申请提交成功"),o("success"),y())}catch(e){console.error("申请发票失败:",e),a.sheep.$helper.toast("申请发票失败，请重试")}finally{n.value=!1}}else a.sheep.$helper.toast("订单ID不能为空")}}}};if(!Array){e.resolveComponent("su-popup")()}Math;const o=e._export_sfc(t,[["render",function(r,a,t,o,n,i){return e.e({a:e.o(((...e)=>o.onClose&&o.onClose(...e))),b:!0===o.formData.invType?1:"",c:!0===o.formData.invType?1:"",d:e.o((e=>o.formData.invType=!0)),e:!1===o.formData.invType?1:"",f:!1===o.formData.invType?1:"",g:e.o((e=>o.formData.invType=!1)),h:!o.formData.invType},o.formData.invType?{}:{i:1===o.formData.titleType?1:"",j:1===o.formData.titleType?1:"",k:e.o((e=>o.formData.titleType=1)),l:2===o.formData.titleType?1:"",m:2===o.formData.titleType?1:"",n:e.o((e=>o.formData.titleType=2))},{o:o.formData.invType?"请输入购方名称":1===o.formData.titleType?"请输入公司名称":"请输入个人姓名",p:o.formData.buyerName,q:e.o((e=>o.formData.buyerName=e.detail.value)),r:o.formData.invType||!o.formData.invType&&1===o.formData.titleType},(o.formData.invType||!o.formData.invType&&o.formData.titleType,{}),{s:o.formData.buyerTaxNo,t:e.o((e=>o.formData.buyerTaxNo=e.detail.value)),v:!o.formData.invType&&2===o.formData.titleType},o.formData.invType||2!==o.formData.titleType?(o.formData.invType,{}):{},{w:o.formData.invType,x:o.formData.invType},o.formData.invType?{y:o.formData.buyerPhone,z:e.o((e=>o.formData.buyerPhone=e.detail.value)),A:o.formData.buyerAddress,B:e.o((e=>o.formData.buyerAddress=e.detail.value)),C:o.formData.buyerBankAccount,D:e.o((e=>o.formData.buyerBankAccount=e.detail.value)),E:o.formData.buyerBankName,F:e.o((e=>o.formData.buyerBankName=e.detail.value))}:{},{G:e.o(((...e)=>o.onClose&&o.onClose(...e))),H:e.t(o.loading?"提交中...":"提交申请"),I:e.o(((...e)=>o.onSubmit&&o.onSubmit(...e))),J:o.loading,K:e.o(o.onClose),L:e.o(o.onClose),M:e.p({show:t.show,type:"bottom",round:20})})}],["__scopeId","data-v-b23612a2"]]);wx.createComponent(o);
