<s-layout wx:if="{{s}}" class="data-v-0a844931" u-s="{{['d']}}" style="{{r}}" u-i="0a844931-0" bind:__l="__l" u-p="{{s}}"><s-empty wx:if="{{a}}" class="data-v-0a844931" u-i="0a844931-1,0a844931-0" bind:__l="__l" u-p="{{b}}"/><view wx:if="{{c}}" class="data-v-0a844931"/><view wx:elif="{{d}}" class="sign-wrap data-v-0a844931"><view class="content-box calendar data-v-0a844931"><view class="sign-everyday ss-flex ss-col-center ss-row-between ss-p-x-30 data-v-0a844931"><text class="sign-everyday-title data-v-0a844931">签到日历</text><view class="sign-num-box data-v-0a844931"> 已连续签到 <text class="sign-num data-v-0a844931">{{e}}</text> 天 </view></view><view class="list acea-row row-between-wrapper data-v-0a844931" style="padding:0 30rpx;height:240rpx;display:flex;justify-content:space-between;align-items:center"><view wx:for="{{f}}" wx:for-item="item" wx:key="f" class="item data-v-0a844931"><view class="{{['data-v-0a844931', item.b]}}"> 第{{item.a}}天 </view><view class="{{['venus', 'data-v-0a844931', item.c]}}"></view><view class="{{['num', 'data-v-0a844931', item.e]}}"> + {{item.d}}</view></view></view><view class="myDateTable data-v-0a844931"><view class="ss-flex ss-col-center ss-row-center sign-box ss-m-y-40 data-v-0a844931"><button wx:if="{{g}}" class="ss-reset-button sign-btn data-v-0a844931" bindtap="{{h}}"> 签到 </button><button wx:else class="ss-reset-button already-btn data-v-0a844931" disabled> 已签到 </button></view></view></view><view class="bg-white ss-m-t-16 ss-p-t-30 ss-p-b-60 ss-p-x-40 data-v-0a844931"><view class="activity-title ss-m-b-30 data-v-0a844931">签到说明</view><view class="activity-des data-v-0a844931">1.已累计签到{{i}}天</view><view class="activity-des data-v-0a844931"> 2.据说连续签到第 {{j}} 天可获得超额积分，要坚持签到哦~~ </view><view class="activity-des data-v-0a844931"> 3.积分可以在购物时抵现金结算的哦 ~~</view></view></view><su-popup wx:if="{{q}}" class="data-v-0a844931" u-s="{{['d']}}" u-i="0a844931-2,0a844931-0" bind:__l="__l" u-p="{{q}}"><view class="model-box ss-flex-col data-v-0a844931"><view class="ss-m-t-56 ss-flex-col ss-col-center data-v-0a844931"><text class="cicon-check-round data-v-0a844931"></text><view class="score-title data-v-0a844931"><text wx:if="{{k}}" class="data-v-0a844931">{{l}} 积分 </text><text wx:if="{{m}}" class="data-v-0a844931">{{n}} 经验</text></view><view class="model-title ss-flex ss-col-center ss-m-t-22 ss-m-b-30 data-v-0a844931"> 已连续打卡 {{o}} 天 </view></view><view class="model-bg ss-flex-col ss-col-center ss-row-right data-v-0a844931"><view class="title ss-m-b-64 data-v-0a844931">签到成功</view><view class="ss-m-b-40 data-v-0a844931"><button class="ss-reset-button confirm-btn data-v-0a844931" bindtap="{{p}}">确认</button></view></view></view></su-popup></s-layout>