<template>
  <ContentWrap v-if="false">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="企业编号" prop="enterpriseId">
        <el-input
          v-model="queryParams.enterpriseId"
          placeholder="请输入企业编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="权益编号" prop="benefitId">
        <el-input
          v-model="queryParams.benefitId"
          placeholder="请输入权益编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="权益代码" prop="benefitCode">
        <el-input
          v-model="queryParams.benefitCode"
          placeholder="请输入权益代码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="权益名称" prop="benefitName">
        <el-input
          v-model="queryParams.benefitName"
          placeholder="请输入权益名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="总量" prop="benefitTotal">
        <el-input
          v-model="queryParams.benefitTotal"
          placeholder="请输入总量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="余量" prop="benefitBalance">
        <el-input
          v-model="queryParams.benefitBalance"
          placeholder="请输入余量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="单位" prop="benefitUnit">
        <el-input
          v-model="queryParams.benefitUnit"
          placeholder="请输入单位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['member:enterprise-benefit:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['member:enterprise-benefit:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column v-if="false" label="企业编号" align="center" prop="enterpriseId" />
      <el-table-column v-if="false" label="权益编号" align="center" prop="benefitId" />
      <el-table-column v-if="false" label="权益代码" align="center" prop="benefitCode" />
      <el-table-column label="权益名称" align="center" prop="benefitName" />
      <el-table-column label="总量" align="center" prop="benefitTotal">
        <template #default="{ row }">
          {{ formatBenefitDisplay(row) }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column v-if="false" label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['member:enterprise-benefit:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['member:enterprise-benefit:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { EnterpriseBenefitApi, EnterpriseBenefitVO } from '@/api/member/enterprisebenefit'
import { getEnterprise } from '@/utils/auth'

defineOptions({ name: 'BenefitIndex' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<EnterpriseBenefitVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  enterpriseId: undefined,
  benefitId: undefined,
  benefitCode: undefined,
  benefitName: undefined,
  benefitTotal: undefined,
  benefitBalance: undefined,
  benefitUnit: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 权益显示相关工具函数
/** 判断是否为时间类权益 */
const isTimeBenefit = (unit: string): boolean => {
  return unit === '秒' || unit === '分钟' || unit === '天'
}

/** 计算到期时间 */
const calculateExpiryTime = (createTime: string, totalSeconds: number): Date => {
  const createDate = new Date(createTime)
  return new Date(createDate.getTime() + totalSeconds * 1000)
}

/** 格式化时间值显示 */
const formatTimeValue = (value: number, unit: string): string => {
  if (unit === '天') {
    const days = Math.floor(value / (24 * 60 * 60))
    return `${days} 天`
  } else if (unit === '分钟') {
    const minutes = Math.floor(value / 60)
    return `${minutes} 分钟`
  } else if (unit === '秒') {
    return `${value} 秒`
  }
  return `${value} ${unit}`
}

/** 格式化权益显示内容 */
const formatBenefitDisplay = (row: EnterpriseBenefitVO): string => {
  if (row.benefitTotal === -1) return '无限制'

  if (isTimeBenefit(row.benefitUnit)) {
    // 时间类权益：显示总量和到期时间
    const expiryTime = calculateExpiryTime(row.createTime, row.benefitTotal)
    const formattedExpiry = expiryTime.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    return `${formatTimeValue(row.benefitTotal, row.benefitUnit)} / 到期: ${formattedExpiry}`
  } else {
    // 次数类权益：显示余量/总量
    return `${row.benefitBalance} ${row.benefitUnit}/${row.benefitTotal} ${row.benefitUnit}`
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.enterpriseId = getEnterprise().id
    const data = await EnterpriseBenefitApi.getEnterpriseBenefitPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await EnterpriseBenefitApi.deleteEnterpriseBenefit(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await EnterpriseBenefitApi.exportEnterpriseBenefit(queryParams)
    download.excel(data, '企业权益.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
