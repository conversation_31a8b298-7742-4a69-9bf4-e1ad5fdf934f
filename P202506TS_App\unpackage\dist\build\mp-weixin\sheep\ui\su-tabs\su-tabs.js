"use strict";const t=require("../../../common/vendor.js"),e=require("../../helper/index.js"),i={name:"su-tabs",data:()=>({addStyle:e.addStyle,addUnit:e.addUnit,firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),props:{duration:{type:Number,default:300},list:{type:Array,default:[]},lineColor:{type:String,default:""},activeStyle:{type:[String,Object],default:()=>({color:"#303133"})},inactiveStyle:{type:[String,Object],default:()=>({color:"#606266"})},lineWidth:{type:[String,Number],default:20},lineHeight:{type:[String,Number],default:3},lineBgSize:{type:String,default:"cover"},itemStyle:{type:[String,Object],default:()=>({height:"44px"})},scrollable:{type:Boolean,default:!0},current:{type:[Number,String],default:0},keyName:{type:String,default:"name"}},watch:{current:{immediate:!0,handler(t,e){t!==this.innerCurrent&&(this.innerCurrent=t,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return t=>{const i={},r=t===this.innerCurrent?e.addStyle(this.activeStyle):e.addStyle(this.inactiveStyle);return this.list[t].disabled&&(i.color="#c8c9cc"),e.deepMerge(r,i)}}},async mounted(){this.init()},methods:{$uGetRect(e,i){return new Promise((r=>{t.index.createSelectorQuery().in(this)[i?"selectAll":"select"](e).boundingClientRect((t=>{i&&Array.isArray(t)&&t.length&&r(t),!i&&t&&r(t)})).exec()}))},setLineLeft(){const t=this.list[this.innerCurrent];if(!t)return;let i=this.list.slice(0,this.innerCurrent).reduce(((t,e)=>t+e.rect.width),0);const r=e.getPx(this.lineWidth);this.lineOffsetLeft=i+(t.rect.width-r)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),10)},animation(t,e=0){},clickHandler(t,e){this.$emit("click",{...t,index:e}),t.disabled||(this.innerCurrent=e,this.resize(),this.$emit("change",{...t,index:e}))},init(){e.sleep().then((()=>{this.resize()}))},setScrollLeft(){const t=this.list[this.innerCurrent],i=this.list.slice(0,this.innerCurrent).reduce(((t,e)=>t+e.rect.width),0),r=e.sys().windowWidth;let s=i-(this.tabsRect.width-t.rect.width)/2-(r-this.tabsRect.right)/2+this.tabsRect.left/2;s=Math.min(s,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,s)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([t,e=[]])=>{this.tabsRect=t,this.scrollViewWidth=0,e.map(((t,e)=>{this.scrollViewWidth+=t.width,this.list[e].rect=t})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((t=>{this.queryRect("u-tabs__wrapper__scroll-view").then((e=>t(e)))}))},getAllItemRect(){return new Promise((t=>{const e=this.list.map(((t,e)=>this.queryRect(`u-tabs__wrapper__nav__item-${e}`,!0)));Promise.all(e).then((e=>t(e)))}))},queryRect(t,e){return new Promise((e=>{this.$uGetRect(`.${t}`).then((t=>{e(t)}))}))}}};const r=t._export_sfc(i,[["render",function(e,i,r,s,n,l){return{a:t.f(r.list,((e,i,s)=>({a:t.t(e[r.keyName]),b:t.n(e.disabled&&"u-tabs__wrapper__nav__item__text--disabled"),c:t.s(l.textStyle(i)),d:i,e:t.o((t=>l.clickHandler(e,i)),i),f:`u-tabs__wrapper__nav__item-${i}`,g:t.n(`u-tabs__wrapper__nav__item-${i}`),h:t.n(e.disabled&&"u-tabs__wrapper__nav__item--disabled")}))),b:t.s(n.addStyle(r.itemStyle)),c:t.s({flex:r.scrollable?"":1}),d:t.s({width:n.addUnit(r.lineWidth),transform:`translate(${n.lineOffsetLeft}px)`,transitionDuration:`${n.firstTime?0:r.duration}ms`,height:n.addUnit(r.lineHeight),background:r.lineColor?r.lineColor:"var(--ui-BG-Main)",backgroundSize:r.lineBgSize}),e:r.scrollable,f:n.scrollLeft}}],["__scopeId","data-v-289e62c6"]]);wx.createComponent(r);
