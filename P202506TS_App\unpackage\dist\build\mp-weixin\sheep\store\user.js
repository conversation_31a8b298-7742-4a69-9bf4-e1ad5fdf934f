"use strict";const e=require("../../common/vendor.js"),t=require("../platform/share.js"),r=require("./cart.js"),s=require("./app.js"),o=require("../hooks/useModal.js"),n=require("../api/member/user.js"),a=require("../api/pay/wallet.js"),i=require("../api/trade/order.js"),u=require("../api/promotion/coupon.js"),l={avatar:"",nickname:"",gender:0,mobile:"",point:0},d={balance:0},p={unusedCouponCount:0,orderCount:{allCount:0,unpaidCount:0,undeliveredCount:0,deliveredCount:0,uncommentedCount:0,afterSaleCount:0}},c=e.defineStore({id:"user",state:()=>({userInfo:e.clone(l),userWallet:e.clone(d),isLogin:!!e.index.getStorageSync("token"),numData:e.cloneDeep(p),lastUpdateTime:0}),actions:{async getInfo(){const{code:e,data:t}=await n.UserApi.getUserInfo();if(0===e)return this.userInfo=t,Promise.resolve(t)},async getWallet(){const{code:e,data:t}=await a.PayWalletApi.getPayWallet();0===e&&(this.userWallet=t)},getNumData(){i.OrderApi.getOrderCount().then((e=>{0===e.code&&(this.numData.orderCount=e.data)})),u.CouponApi.getUnusedCouponCount().then((e=>{0===e.code&&(this.numData.unusedCouponCount=e.data)}))},setToken(t="",r=""){return""===t?(this.isLogin=!1,e.index.removeStorageSync("token"),e.index.removeStorageSync("refresh-token")):(this.isLogin=!0,e.index.setStorageSync("token",t),e.index.setStorageSync("refresh-token",r),this.loginAfter()),this.isLogin},setEnterprise(t){e.index.setStorageSync("enterprise",t)},async updateUserData(){if(!this.isLogin)return void this.resetUserData();const e=(new Date).getTime();return this.lastUpdateTime+5e3>e?void 0:(this.lastUpdateTime=e,await this.getInfo(),this.getWallet(),this.getNumData(),this.userInfo)},resetUserData(){this.setToken(),this.setEnterprise(),this.userInfo=e.clone(l),this.userWallet=e.clone(d),this.numData=e.cloneDeep(p),r.cart().emptyList()},async loginAfter(){await this.updateUserData(),r.cart().getList(),t.$share.getShareInfo(),s.app().platform.bind_mobile&&!this.userInfo.mobile&&o.showAuthModal("changeMobile"),t.$share.bindBrokerageUser()},async logout(){return this.resetUserData(),!this.isLogin}},persist:{enabled:!0,strategies:[{key:"user-store"}]}}),h=Object.freeze(Object.defineProperty({__proto__:null,default:c},Symbol.toStringTag,{value:"Module"}));exports.__vite_glob_0_4=h,exports.user=c;
