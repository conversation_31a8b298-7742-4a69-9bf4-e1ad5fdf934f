<view class="{{['ui-swiper', 'data-v-9f5543a4', v, w]}}" style="{{x + ';' + y}}"><swiper class="data-v-9f5543a4" circular="{{d}}" current="{{e}}" autoplay="{{f}}" interval="{{g}}" duration="{{h}}" style="{{i}}" bindchange="{{j}}"><swiper-item wx:for="{{a}}" wx:for-item="arr" wx:key="b" class="{{['data-v-9f5543a4', arr.c && 'cur']}}"><view class="grid-wrap data-v-9f5543a4"><view wx:for="{{arr.a}}" wx:for-item="item" wx:key="i" class="grid-item ss-flex ss-flex-col ss-col-center ss-row-center data-v-9f5543a4" style="{{c}}" hover-class="ss-hover-btn" bindtap="{{item.j}}"><view class="menu-box ss-flex ss-flex-col ss-col-center ss-row-center data-v-9f5543a4"><view wx:if="{{item.a}}" class="tag-box data-v-9f5543a4" style="{{item.c}}">{{item.b}}</view><image wx:if="{{item.d}}" class="menu-icon data-v-9f5543a4" style="{{item.e}}" src="{{item.f}}" mode="aspectFill"></image><view wx:if="{{b}}" class="menu-title data-v-9f5543a4" style="{{item.h}}">{{item.g}}</view></view></view></view></swiper-item></swiper><block wx:if="{{k}}"><view wx:if="{{l}}" class="{{['ui-swiper-dot', 'data-v-9f5543a4', o]}}"><view wx:for="{{m}}" wx:for-item="item" wx:key="a" class="{{['line-box', 'data-v-9f5543a4', item.b, n]}}"></view></view><view wx:if="{{p}}" class="{{['ui-swiper-dot', 'data-v-9f5543a4', t]}}"><view class="{{['ui-tag', 'radius', 'data-v-9f5543a4', s]}}" style="pointer-events:none"><view class="data-v-9f5543a4" style="transform:scale(0.7)">{{q}} / {{r}}</view></view></view></block></view>