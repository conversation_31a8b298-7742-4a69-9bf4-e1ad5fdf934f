"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/api/promotion/diy.js");if(!Array){(e.resolveComponent("s-block-item")+e.resolveComponent("s-block")+e.resolveComponent("s-layout"))()}Math||((()=>"../../sheep/components/s-block-item/s-block-item.js")+(()=>"../../sheep/components/s-block/s-block.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const t={__name:"page",setup(t){const n=e.reactive({name:"",components:[],navigationBar:{},page:{}});return e.onLoad((async e=>{var t,a,s;let p=e.id;if(e.scene){p=decodeURIComponent(e.scene).split("=")[1]}const{code:r,data:c}=await o.DiyApi.getDiyPage(p);0===r&&(n.name=c.name,n.components=null==(t=c.property)?void 0:t.components,n.navigationBar=null==(a=c.property)?void 0:a.navigationBar,n.page=null==(s=c.property)?void 0:s.page)})),e.onPageScroll((()=>{})),(o,t)=>({a:e.f(n.components,((o,t,n)=>({a:"c739597e-2-"+n+",c739597e-1-"+n,b:e.p({type:o.id,data:o.property,styles:o.property.style}),c:t,d:"c739597e-1-"+n+",c739597e-0",e:e.p({styles:o.property.style})}))),b:e.p({title:n.name,navbar:"custom",bgStyle:n.page,navbarStyle:n.navigationBar,onShareAppMessage:!0,showLeftButton:!0})})},__runtimeHooks:3};wx.createPage(t);
