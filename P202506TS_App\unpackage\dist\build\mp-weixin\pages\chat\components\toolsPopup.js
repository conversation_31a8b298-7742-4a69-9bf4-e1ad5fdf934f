"use strict";const e=require("../../../common/vendor.js"),o=require("../util/emoji.js"),t=require("../../../sheep/index.js");if(!Array){(e.resolveComponent("s-uploader")+e.resolveComponent("su-popup"))()}Math||((()=>"../../../sheep/components/s-uploader/s-uploader.js")+(()=>"../../../sheep/ui/su-popup/su-popup.js"))();const s={__name:"toolsPopup",props:{toolsMode:{type:String,default:""},showTools:{type:Boolean,default:()=>!1}},emits:["onEmoji","imageSelect","onShowSelect","close"],setup(s,{emit:i}){const p=i;function n(){p("close")}function a(e){p("onShowSelect",e)}return(i,r)=>e.e({a:"emoji"===s.toolsMode},"emoji"===s.toolsMode?{b:e.f(e.unref(o.emojiPage),((o,s,i)=>({a:e.f(o,((o,s,i)=>({a:o,b:e.unref(t.sheep).$url.cdn(`/static/img/chat/emoji/${o.file}`),c:e.o((e=>function(e){p("onEmoji",e)}(o)),o)}))),b:o})))}:{c:e.unref(t.sheep).$url.static("/static/img/shop/chat/image.png"),d:e.o((e=>{p("imageSelect",{type:"image",data:e})})),e:e.p({"file-mediatype":"image",imageStyles:{width:50,height:50,border:!1}}),f:e.unref(t.sheep).$url.static("/static/img/shop/chat/goods.png"),g:e.o((e=>a("goods"))),h:e.unref(t.sheep).$url.static("/static/img/shop/chat/order.png"),i:e.o((e=>a("order")))},{j:e.o(n),k:e.p({show:s.showTools})})}},i=e._export_sfc(s,[["__scopeId","data-v-754073fc"]]);wx.createComponent(i);
