"use strict";const e=require("../../common/vendor.js");require("../../sheep/index.js");const t=require("../../sheep/util/index.js"),a=require("../../sheep/api/system/notifyMessage.js");if(!Array){(e.resolveComponent("uni-forms-item")+e.resolveComponent("uni-forms")+e.resolveComponent("s-layout"))()}Math||((()=>"../../uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js")+(()=>"../../uni_modules/uni-forms/components/uni-forms/uni-forms.js")+(()=>"../../sheep/components/s-layout/s-layout.js"))();const o={__name:"detail",setup(o){const m=e.reactive({id:"",model:{},content:""});e.computed((()=>e.index.getStorageSync("enterprise")));return e.onLoad((t=>{m.id=t.id,(async()=>{const{data:t}=await a.NotifyMessageApi.getNotifyMessage(m.id);m.model=e.clone(t)})()})),(a,o)=>{var n,s;return{a:e.t(m.model.templateNickname),b:e.p({name:"templateNickname",label:"发送人"}),c:e.t(e.unref(t.formatDate)(m.model.createTime)),d:e.p({name:"createTime",label:"发送时间"}),e:e.t(["","通知公告","系统消息"][m.model.templateType]),f:e.p({name:"templateType",label:"消息类型"}),g:e.t(m.model.readStatus?"已读":"未读"),h:e.p({name:"readStatus",label:"是否已读"}),i:e.t(e.unref(t.formatDate)(m.model.readTime)),j:e.p({name:"readTime",label:"阅读时间"}),k:e.t(null==(n=m.model.templateParams)?void 0:n.title),l:e.p({name:"templateParams",label:"标题"}),m:e.t(null==(s=m.model.templateParams)?void 0:s.content),n:e.p({name:"templateParams",label:"内容"}),o:e.p({model:m.model,rules:m.rules,labelPosition:"left",border:!0}),p:e.p({title:"消息详情"})}}}},m=e._export_sfc(o,[["__scopeId","data-v-9907564c"]]);wx.createPage(m);
