{"pages": ["pages/index/login", "pages/index/register", "pages/index/index", "pages/index/user", "pages/index/contract", "pages/contractsign/list", "pages/contractsign/detail", "pages/enterprisepartnerthird/list", "pages/enterprisepartnerthird/reportList", "pages/index/ai", "pages/index/college", "pages/college/detail", "pages/tax/list", "pages/tax/setting", "pages/finance/list", "pages/finance/create", "pages/finance/detail", "pages/finance/setting", "pages/message/list", "pages/message/detail", "pages/index/category", "pages/index/cart", "pages/index/search", "pages/index/page"], "subPackages": [{"root": "pages/contract", "pages": ["list", "create", "detail", "risk-list"]}, {"root": "pages/contractdraft", "pages": ["list", "detail", "reportList", "reportDetail"]}, {"root": "pages/goods", "pages": ["index", "groupon", "seckill", "point", "list", "comment/add", "comment/list"]}, {"root": "pages/order", "pages": ["detail", "confirm", "list", "aftersale/apply", "aftersale/return-delivery", "aftersale/list", "aftersale/detail", "aftersale/log", "express/log"]}, {"root": "pages/user", "pages": ["info", "goods-collect", "goods-log", "address/list", "address/edit", "goods_details_store/index", "wallet/money", "wallet/score", "change-password"]}, {"root": "pages/commission", "pages": ["index", "wallet", "goods", "order", "team", "promoter", "commission-ranking", "withdraw"]}, {"root": "pages/app", "pages": ["sign"]}, {"root": "pages/public", "pages": ["setting", "richtext", "faq", "error", "webview"]}, {"root": "pages/coupon", "pages": ["list", "detail"]}, {"root": "pages/chat", "pages": ["index"]}, {"root": "pages/pay", "pages": ["index", "result", "recharge", "recharge-log"]}, {"root": "pages/activity", "pages": ["groupon/detail", "groupon/order", "index", "groupon/list", "seckill/list", "point/list"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "税眸", "navigationBarBackgroundColor": "#FFFFFF", "backgroundColor": "#FFFFFF", "navigationStyle": "custom"}, "tabBar": {"list": [{"pagePath": "pages/index/index"}, {"pagePath": "pages/index/contract"}, {"pagePath": "pages/index/ai"}, {"pagePath": "pages/index/college"}, {"pagePath": "pages/index/user"}]}, "plugins": {}, "lazyCodeLoading": "requiredComponents", "permission": {}, "requiredPrivateInfos": ["<PERSON><PERSON><PERSON><PERSON>"], "usingComponents": {}}