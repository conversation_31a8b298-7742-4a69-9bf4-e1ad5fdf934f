"use strict";function t(t,n=15){return+parseFloat(Number(t).toPrecision(n))}function n(t){const n=t.toString().split(/[eE]/),r=(n[0].split(".")[1]||"").length-+(n[1]||0);return r>0?r:0}function r(r){if(-1===r.toString().indexOf("e"))return Number(r.toString().replace(".",""));const e=n(r);return e>0?t(Number(r)*Math.pow(10,e)):Number(r)}function e(t){(t>Number.MAX_SAFE_INTEGER||t<Number.MIN_SAFE_INTEGER)&&console.warn(`${t} 超出了精度限制，结果可能不正确`)}function o(t,n){const[r,e,...o]=t;let u=n(r,e);return o.forEach((t=>{u=n(u,t)})),u}function u(...t){if(t.length>2)return o(t,u);const[i,c]=t,s=r(i),f=r(c),a=n(i)+n(c),h=s*f;return e(h),h/Math.pow(10,a)}function i(...c){if(c.length>2)return o(c,i);const[s,f]=c,a=r(s),h=r(f);return e(a),e(h),u(a/h,t(Math.pow(10,n(f)-n(s))))}exports.round=function(t,n){const r=Math.pow(10,n);let e=i(Math.round(Math.abs(u(t,r))),r);return t<0&&0!==e&&(e=u(e,-1)),e};
