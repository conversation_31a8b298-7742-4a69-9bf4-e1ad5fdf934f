"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js");if(require("../../store/index.js"),require("../../helper/index.js"),require("../../request/index.js"),!Array){(e.resolveComponent("su-status-bar")+e.resolveComponent("su-fixed"))()}Math||((()=>"../su-status-bar/su-status-bar.js")+(()=>"../su-fixed/su-fixed.js"))();const a={__name:"su-inner-navbar",props:{zIndex:{type:Number,default:100},title:{type:String,default:""},bg:{type:String,default:"bg-white"},alway:{type:Boolean,default:!0},opacity:{type:Boolean,default:!0},noFixed:{type:Boolean,default:!0},ui:{type:String,default:""},capsule:{type:Boolean,default:!1},stopBack:{type:Boolean,default:!1},placeholder:{type:[<PERSON><PERSON>an],default:!1},bgStyles:{type:Object,default(){}}},emits:["navback","clickLeft"],setup(a,{emit:s}){const r=e.reactive({statusCur:"",capsuleStyle:{},capsuleBack:{},isDark:!0}),l=t.sheep.$platform.device.statusBarHeight,o=t.sheep.$platform.navbar,p=a,u=s,n=t.sheep.$router.hasHistory();function i(){n?t.sheep.$router.back():t.sheep.$router.go("/pages/index/index"),u("clickLeft")}e.onBeforeMount((()=>{c()})),e.onPageScroll((e=>{let a=e.scrollTop;r.isDark=a<t.sheep.$platform.navbar}));const c=()=>{r.capsuleStyle={width:t.sheep.$platform.capsule.width+"px",height:t.sheep.$platform.capsule.height+"px"},r.capsuleBack=r.capsuleStyle};return(t,s)=>e.e({a:e.unref(n)},(e.unref(n),{}),{b:e.o(i)},{},{},{d:e.t(a.title),e:e.s(r.capsuleStyle),f:e.n(r.isDark?"text-white":"text-black"),g:e.s({height:e.unref(o)-e.unref(l)+"px"}),h:e.p({noFixed:p.noFixed,alway:p.alway,bgStyles:p.bgStyles,val:0,index:p.zIndex,noNav:!0,bg:p.bg,ui:p.ui,opacity:p.opacity,placeholder:p.placeholder})})}},s=e._export_sfc(a,[["__scopeId","data-v-db739fd9"]]);wx.createComponent(s);
