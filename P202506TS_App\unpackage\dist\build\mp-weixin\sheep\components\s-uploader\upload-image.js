"use strict";const e=require("../../../common/vendor.js"),t=require("../../index.js"),i={name:"uploadImage",emits:["uploadFiles","choose","delFile"],props:{filesList:{type:[Array,String],default:()=>[]},disabled:{type:Boolean,default:!1},disablePreview:{type:Boolean,default:!1},limit:{type:[Number,String],default:9},imageStyles:{type:Object,default:()=>({width:"auto",height:"auto",border:{}})},delIcon:{type:<PERSON>olean,default:!0},readonly:{type:Boolean,default:!1}},computed:{list(){return"string"==typeof this.filesList?this.filesList?[this.filesList]:[]:this.filesList},styles(){return Object.assign({width:"auto",height:"auto",border:{}},this.imageStyles)},boxStyle(){const{width:e="auto",height:t="auto"}=this.styles;let i={};"auto"===t?"auto"!==e?(i.height=this.value2px(e),i["padding-top"]=0):i.height=0:(i.height=this.value2px(t),i["padding-top"]=0),i.width="auto"===e?"auto"!==t?this.value2px(t):"33.3%":this.value2px(e);let l="";for(let o in i)l+=`${o}:${i[o]};`;return l},borderStyle(){let{border:e}=this.styles,t={};if("boolean"==typeof e)t.border=e?"1px #eee solid":"none";else{let i=e&&e.width||1;i=this.value2px(i);let l=e&&e.radius||3;l=this.value2px(l),t={"border-width":i,"border-style":e&&e.style||"solid","border-color":e&&e.color||"#eee","border-radius":l}}let i="";for(let l in t)i+=`${l}:${t[l]};`;return i}},methods:{getImageUrl:e=>"blob:http:"===e.substr(0,10)?e:t.sheep.$url.cdn(e),uploadFiles(e,t){this.$emit("uploadFiles",e)},choose(){this.$emit("choose")},delFile(e){this.$emit("delFile",e)},previewImage(t,i){let l=[];1===Number(this.limit)&&this.disablePreview&&!this.disabled&&this.$emit("choose"),this.disablePreview||(this.list.forEach((e=>{l.push(this.getImageUrl(e))})),e.index.previewImage({urls:l,current:i}))},value2px:e=>("number"==typeof e?e+="px":-1===e.indexOf("%")&&(e=-1!==e.indexOf("px")?e:e+"px"),e)}};const l=e._export_sfc(i,[["render",function(t,i,l,o,s,r){return e.e({a:e.f(r.list,((t,i,o)=>e.e({a:r.getImageUrl(t),b:e.o((e=>r.previewImage(t,i)),i)},l.delIcon&&!l.readonly?{c:e.o((e=>r.delFile(i)),i)}:{},{d:i}))),b:l.delIcon&&!l.readonly,c:e.s(r.borderStyle),d:e.s(r.boxStyle),e:r.list.length<l.limit&&!l.readonly},r.list.length<l.limit&&!l.readonly?{f:e.s(r.borderStyle),g:e.o(((...e)=>r.choose&&r.choose(...e))),h:e.s(r.boxStyle)}:{})}]]);wx.createComponent(l);
