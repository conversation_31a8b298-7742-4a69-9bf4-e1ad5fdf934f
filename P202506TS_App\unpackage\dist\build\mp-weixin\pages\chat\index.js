"use strict";const e=require("../../common/vendor.js"),o=require("../../sheep/index.js"),s=require("./util/constants.js"),t=require("../../sheep/api/infra/file.js"),n=require("../../sheep/api/promotion/kefu.js"),a=require("../../sheep/hooks/useWebSocket.js"),c=require("../../sheep/util/index.js");if(!Array){e.resolveComponent("s-layout")()}Math||(r+i+l+p+(()=>"../../sheep/components/s-layout/s-layout.js"))();const i=()=>"./components/messageList.js",l=()=>"./components/toolsPopup.js",r=()=>"./components/messageInput.js",p=()=>"./components/select-popup.js",u={__name:"index",setup(i){const l=o.sheep.$platform.navbar,r=e.reactive({msg:"",scrollInto:"",showTools:!1,toolsMode:"",showSelect:!1,selectMode:""});async function p(){if(r.msg)try{const e={contentType:s.KeFuMessageContentTypeEnum.TEXT,content:JSON.stringify({text:r.msg})};await n.KeFuApi.sendKefuMessage(e),await u.value.refreshMessageList(),r.msg=""}finally{r.showTools=!1}}const u=e.ref();function m(){r.showTools=!1,r.toolsMode=""}function g(e){r.msg+=e.name}function h(e){y.value?o.sheep.$helper.toast("您已掉线！请返回重试"):(r.toolsMode&&r.toolsMode!==e||(r.showTools=!r.showTools),r.toolsMode=e,r.showTools||(r.toolsMode=""))}function d(e){r.showTools=!1,r.showSelect=!0,r.selectMode=e}async function f({type:e,data:o}){let a;switch(e){case"image":const e=await t.FileApi.uploadFile(o.tempFiles[0].path);a={contentType:s.KeFuMessageContentTypeEnum.IMAGE,content:JSON.stringify({picUrl:e.data})};break;case"goods":a={contentType:s.KeFuMessageContentTypeEnum.PRODUCT,content:JSON.stringify(o)};break;case"order":a={contentType:s.KeFuMessageContentTypeEnum.ORDER,content:JSON.stringify(o)}}a&&(await n.KeFuApi.sendKefuMessage(a),await u.value.refreshMessageList(),r.showTools=!1,r.showSelect=!1,r.selectMode="")}const{options:M}=a.useWebSocket({onConnected:async()=>{},onMessage:async e=>{const o=e.type;o?o!==s.WebSocketMessageTypeConstants.KEFU_MESSAGE_TYPE?o===s.WebSocketMessageTypeConstants.KEFU_MESSAGE_ADMIN_READ&&console.log("管理员已读消息"):await u.value.refreshMessageList(c.jsonParse(e.content)):console.error("未知的消息类型："+e)}}),y=e.toRefs(M).isReconnecting;return(o,s)=>({a:e.unref(l)+"px",b:e.o(h),c:e.o(p),d:e.o((e=>r.msg=e)),e:e.p({modelValue:r.msg}),f:e.sr(u,"9ee391b2-1,9ee391b2-0",{k:"messageListRef"}),g:e.o(h),h:e.o(p),i:e.o((e=>r.msg=e)),j:e.p({modelValue:r.msg}),k:e.o(m),l:e.o(g),m:e.o(f),n:e.o(d),o:e.p({"show-tools":r.showTools,"tools-mode":r.toolsMode}),p:e.o(f),q:e.o((e=>r.showSelect=!1)),r:e.p({mode:r.selectMode,show:r.showSelect}),s:e.p({title:e.unref(y)?"会话重连中":"连接客服成功",navbar:"inner"})})}},m=e._export_sfc(u,[["__scopeId","data-v-9ee391b2"]]);wx.createPage(m);
