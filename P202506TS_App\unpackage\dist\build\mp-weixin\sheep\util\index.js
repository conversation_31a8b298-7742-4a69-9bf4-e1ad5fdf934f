"use strict";const t=require("../../common/vendor.js");exports.copyValueToTarget=(t,e)=>{const r=Object.assign({},t,e);Object.keys(r).forEach((e=>{-1===Object.keys(t).indexOf(e)&&delete r[e]})),Object.assign(t,r)},exports.floatToFixed2=t=>{let e="0.00";if(void 0===t)return e;const r=(t=>{if(void 0===t)return 0;const e="string"==typeof t?parseFloat(t):t;return parseFloat((e/100).toFixed(2))})(t),n=r.toString().split(".")[1];switch(n?n.length:0){case 0:e=r.toString()+".00";break;case 1:e=r.toString()+".0";break;case 2:e=r.toString()}return e},exports.formatDate=function(e,r="YYYY-MM-DD HH:mm:ss"){return e?(void 0===r&&(r="YYYY-MM-DD HH:mm:ss"),t.dayjs(e).format(r)):""},exports.handleTree=function(t,e="id",r="parentId",n="children",o=0){const s=JSON.parse(JSON.stringify(t)),i=s.filter((t=>{let n=s.filter((n=>t[e]===n[r]));return n.length>0&&(t.children=n),t[r]===o}));return""!==i?i:t},exports.jsonParse=function(t){try{return JSON.parse(t)}catch(e){return console.error(`str[${t}] 不是一个 JSON 字符串`),""}},exports.resetPagination=function(t){t.list=[],t.total=0,t.pageNo=1};
