<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="queryParams.enterpriseName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="权益编号" prop="benefitId">
        <el-select
          v-model="queryParams.benefitId"
          filterable
          placeholder="请选择权益"
          class="!w-240px"
          clearable
        >
          <el-option
            v-for="item in benefitOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['member:enterprise-benefit:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['member:enterprise-benefit:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border>
      <el-table-column label="主键" align="center" prop="id" min-width="80" />
      <el-table-column label="企业名称" align="center" prop="enterpriseName" min-width="120" />
      <el-table-column label="权益编号" align="center" prop="benefitId" min-width="120" />
      <el-table-column label="权益代码" align="center" prop="benefitCode" min-width="120" />
      <el-table-column label="权益名称" align="center" prop="benefitName" min-width="180" />
      <el-table-column label="总量" align="center" prop="benefitTotal" min-width="120">
        <template #default="{ row }">
          {{ formatTotalDisplay(row) }}
        </template>
      </el-table-column>
      <el-table-column label="余量" align="center" prop="benefitBalance" min-width="180">
        <template #default="{ row }">
          {{ formatBalanceDisplay(row) }}
        </template>
      </el-table-column>
      <el-table-column label="单位" align="center" prop="benefitUnit" min-width="100" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        min-width="180"
      />
      <el-table-column label="操作" align="center" fixed="right" width="150">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['member:enterprise-benefit:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['member:enterprise-benefit:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <EnterpriseBenefitForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { EnterpriseBenefitApi, EnterpriseBenefitVO } from '@/api/member/enterprisebenefit'
import { BenefitApi } from '@/api/product/benefit'
import EnterpriseBenefitForm from './EnterpriseBenefitForm.vue'

/** 企业权益 列表 */
defineOptions({ name: 'EnterpriseBenefit' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<EnterpriseBenefitVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  enterpriseName: undefined,
  benefitId: undefined,
  benefitCode: undefined,
  benefitName: undefined,
  benefitTotal: undefined,
  benefitBalance: undefined,
  benefitUnit: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const benefitOptions = ref<{ name: string; id: number }[]>([]) // 权益下拉列表
/** 权益下拉列表 */
const getBenefitOptions = async () => {
  const data = await BenefitApi.getBenefitList()
  benefitOptions.value = data
}

// 权益显示相关工具函数
/** 判断是否为时间类权益 */
const isTimeBenefit = (unit: string): boolean => {
  return unit === '秒' || unit === '分钟' || unit === '天'
}

/** 计算到期时间 */
const calculateExpiryTime = (createTime: string, totalSeconds: number): Date => {
  const createDate = new Date(createTime)
  return new Date(createDate.getTime() + totalSeconds * 1000)
}

/** 格式化总量显示 */
const formatTotalDisplay = (row: EnterpriseBenefitVO): string => {
  if (row.benefitTotal === -1) return '无限'

  if (isTimeBenefit(row.benefitUnit)) {
    // 时间类权益：显示转换成天数
    const days = Math.floor(row.benefitTotal / (24 * 60 * 60))
    return `${days} 天`
  } else {
    // 次数类权益：显示原值
    return `${row.benefitTotal}`
  }
}

/** 格式化余量显示 */
const formatBalanceDisplay = (row: EnterpriseBenefitVO): string => {
  if (row.benefitBalance === -1) return '无限'

  if (isTimeBenefit(row.benefitUnit)) {
    // 时间类权益：显示到期时间
    const expiryTime = calculateExpiryTime(row.createTime, row.benefitTotal)
    const formattedExpiry = expiryTime.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    return `到期: ${formattedExpiry}`
  } else {
    // 次数类权益：显示原值
    return `${row.benefitBalance}`
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await EnterpriseBenefitApi.getEnterpriseBenefitPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await EnterpriseBenefitApi.deleteEnterpriseBenefit(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await EnterpriseBenefitApi.exportEnterpriseBenefit(queryParams)
    download.excel(data, '企业权益.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getBenefitOptions()
  getList()
})
</script>
