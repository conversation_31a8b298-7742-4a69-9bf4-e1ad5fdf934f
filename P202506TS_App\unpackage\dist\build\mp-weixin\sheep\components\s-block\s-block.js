"use strict";const t=require("../../../common/vendor.js"),e=require("../../index.js"),s={__name:"s-block",props:{styles:{type:Object,default(){}}},setup(s){const o=s,r=t.computed((()=>{if(o.styles){if("color"===o.styles.bgType)return{background:o.styles.bgColor};if("img"===o.styles.bgType)return{background:`url(${e.sheep.$url.cdn(o.styles.bgImage)}) no-repeat top center / 100% auto`}}})),d=t.computed((()=>{if(o.styles)return{marginTop:`${o.styles.marginTop||0}px`,marginBottom:`${o.styles.marginBottom||0}px`,marginLeft:`${o.styles.marginLeft||0}px`,marginRight:`${o.styles.marginRight||0}px`,paddingTop:`${o.styles.paddingTop||0}px`,paddingRight:`${o.styles.paddingRight||0}px`,paddingBottom:`${o.styles.paddingBottom||0}px`,paddingLeft:`${o.styles.paddingLeft||0}px`,borderTopLeftRadius:`${o.styles.borderTopLeftRadius||0}px`,borderTopRightRadius:`${o.styles.borderTopRightRadius||0}px`,borderBottomRightRadius:`${o.styles.borderBottomRightRadius||0}px`,borderBottomLeftRadius:`${o.styles.borderBottomLeftRadius||0}px`,overflow:"hidden"}}));return(e,s)=>({a:t.s(d.value),b:t.s(r.value)})}};wx.createComponent(s);
