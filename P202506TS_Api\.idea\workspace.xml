<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="81049ffd-cc7a-40de-8b66-7f6ba79d0da3" name="Changes" comment="渠道端上传课程&#10;管理端审核课程">
      <change beforePath="$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/enterprisebenefit/EnterpriseBenefitServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/enterprisebenefit/EnterpriseBenefitServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/yudao-server/src/main/resources/application-local.yaml" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\programer\tools\repository" />
        <option name="userSettingsFile" value="D:\programer\JAVA\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2uHviOG2xyGRZUGS5JOkNNGvaqm" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.HuoShanSmsClientIntegrationTest.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.PdfToRichTextUtilTest.testConvertFromUrl.executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao [validate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao [verify].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao-server [org.apache.maven.plugins:maven-clean-plugin:3.2.0:clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.yudao-server [org.apache.maven.plugins:maven-install-plugin:3.1.1:install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.YudaoServerApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/RuiZhiXingYuan/TianSuan/P202506TS_Api&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.47471264&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\programer\\IntelliJ IDEA 2024.1.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.Test.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.YeepayDirectTestRunner.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.YopRsaEncryptExample.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="cn.iocoder.yudao.module.member.controller.app.enterprisepartner" />
      <recent name="cn.iocoder.yudao.module.system.framework.sms.core.client.impl" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\RuiZhiXingYuan\TianSuan\P202506TS_Api" />
      <recent name="E:\RuiZhiXingYuan\TianSuan\P202506TS_Api\yudao-module-pay\yudao-spring-boot-starter-biz-pay\src\main\java\com\yeepay\yop\sdk\service" />
      <recent name="E:\RuiZhiXingYuan\TianSuan\P202506TS_Api\yudao-module-pay\yudao-spring-boot-starter-biz-pay\src\main\java\com\yeepay\yop\sdk" />
      <recent name="E:\RuiZhiXingYuan\TianSuan\P202506TS_Api\yudao-module-mall\yudao-module-trade-biz\src\main\java\cn\iocoder\yudao\module\trade\controller\admin" />
      <recent name="E:\RuiZhiXingYuan\TianSuan\P202506TS_Api\yudao-module-mall\yudao-module-trade-biz\src\main" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\RuiZhiXingYuan\P202501YQF_Api\yudao-module-member\yudao-module-member-biz\src\main\java\cn\iocoder\yudao\module\member\controller\app" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="cn.iocoder.yudao" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="cn.iocoder.yudao.module.member.message.qduser" />
      <recent name="cn.iocoder.yudao.module.member.mq.producer.qduser" />
      <recent name="cn.iocoder.yudao.module.member.convert.qdauth" />
      <recent name="cn.iocoder.yudao.module.member.controller.app.reportaisino.vo" />
      <recent name="cn.iocoder.yudao.module.member.controller.app.contractqys.vo" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.YudaoServerApplication">
    <configuration name="Test" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.Test" />
      <module name="yudao-server" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YeepayDirectTestRunner" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.framework.pay.core.client.impl.yeepay.YeepayDirectTestRunner" />
      <module name="yudao-spring-boot-starter-biz-pay" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.framework.pay.core.client.impl.yeepay.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YopRsaEncryptExample" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.framework.pay.core.client.impl.yeepay.YopRsaEncryptExample" />
      <module name="yudao-spring-boot-starter-biz-pay" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.framework.pay.core.client.impl.yeepay.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YudaoServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="yudao-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.iocoder.yudao.server.YudaoServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.YeepayDirectTestRunner" />
        <item itemvalue="应用程序.YopRsaEncryptExample" />
        <item itemvalue="应用程序.Test" />
        <item itemvalue="应用程序.YopRsaEncryptExample" />
        <item itemvalue="应用程序.Test" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.17011.79" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.17011.79" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="81049ffd-cc7a-40de-8b66-7f6ba79d0da3" name="Changes" comment="" />
      <created>1711368569632</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1711368569632</updated>
      <workItem from="1711368570655" duration="25000" />
      <workItem from="1724298328010" duration="518000" />
      <workItem from="1741918441889" duration="4212000" />
      <workItem from="1741927637817" duration="107000" />
      <workItem from="1742199387708" duration="26018000" />
      <workItem from="1742290044611" duration="2241000" />
      <workItem from="1742346666258" duration="13365000" />
      <workItem from="1742370790061" duration="7397000" />
      <workItem from="1742385437965" duration="9413000" />
      <workItem from="1742397362438" duration="1005000" />
      <workItem from="1742400617738" duration="61000" />
      <workItem from="1742400727842" duration="2000" />
      <workItem from="1742400783491" duration="2000" />
      <workItem from="1742400793737" duration="174000" />
      <workItem from="1742400978030" duration="2000" />
      <workItem from="1742401212493" duration="8000" />
      <workItem from="1742401302161" duration="241000" />
      <workItem from="1742430573062" duration="3030000" />
      <workItem from="1742436250401" duration="12842000" />
      <workItem from="1742482143655" duration="7099000" />
      <workItem from="1742530251664" duration="352000" />
      <workItem from="1742530611435" duration="1216000" />
      <workItem from="1742531851053" duration="52000" />
      <workItem from="1742531911814" duration="37593000" />
      <workItem from="1742778066293" duration="21604000" />
      <workItem from="1742813122463" duration="17000" />
      <workItem from="1742813953231" duration="1406000" />
      <workItem from="1742865380859" duration="2392000" />
      <workItem from="1742868955209" duration="90000" />
      <workItem from="1742890281001" duration="3333000" />
      <workItem from="1742903366813" duration="814000" />
      <workItem from="1742905747877" duration="596000" />
      <workItem from="1742907283837" duration="2690000" />
      <workItem from="1742918256288" duration="9000" />
      <workItem from="1742950860142" duration="3037000" />
      <workItem from="1742966930964" duration="11370000" />
      <workItem from="1743002572176" duration="157000" />
      <workItem from="1743006125101" duration="358000" />
      <workItem from="1743037240558" duration="17029000" />
      <workItem from="1743068030535" duration="245000" />
      <workItem from="1743068329761" duration="5712000" />
      <workItem from="1743076416480" duration="1274000" />
      <workItem from="1743085719241" duration="256000" />
      <workItem from="1743089661921" duration="1065000" />
      <workItem from="1743124987028" duration="39881000" />
      <workItem from="1743176259364" duration="55000" />
      <workItem from="1743213863275" duration="1792000" />
      <workItem from="1743218462111" duration="1602000" />
      <workItem from="1743224777846" duration="8000" />
      <workItem from="1743230188002" duration="33178000" />
      <workItem from="1743269012993" duration="4888000" />
      <workItem from="1743336971679" duration="13214000" />
      <workItem from="1743350905333" duration="45000" />
      <workItem from="1743382496057" duration="24767000" />
      <workItem from="1743410001293" duration="8633000" />
      <workItem from="1743431994461" duration="17000" />
      <workItem from="1743432154018" duration="595000" />
      <workItem from="1743439645920" duration="1478000" />
      <workItem from="1743476296598" duration="13560000" />
      <workItem from="1743497904107" duration="43000" />
      <workItem from="1743556987341" duration="20023000" />
      <workItem from="1743583361429" duration="317000" />
      <workItem from="1743601717470" duration="595000" />
      <workItem from="1743641748356" duration="26295000" />
      <workItem from="1743676390938" duration="5163000" />
      <workItem from="1743935614756" duration="15000" />
      <workItem from="1743988151090" duration="15263000" />
      <workItem from="1744036621517" duration="20000" />
      <workItem from="1744073673156" duration="17907000" />
      <workItem from="1744099867883" duration="6947000" />
      <workItem from="1744125566198" duration="2188000" />
      <workItem from="1744159943110" duration="16121000" />
      <workItem from="1744181853197" duration="744000" />
      <workItem from="1744182934898" duration="1942000" />
      <workItem from="1744190772735" duration="2163000" />
      <workItem from="1744246423536" duration="21902000" />
      <workItem from="1744333002784" duration="20580000" />
      <workItem from="1744447188290" duration="3364000" />
      <workItem from="1744456292294" duration="25000" />
      <workItem from="1744593299781" duration="2025000" />
      <workItem from="1744599335846" duration="41000" />
      <workItem from="1744615359846" duration="614000" />
      <workItem from="1744618717621" duration="62000" />
      <workItem from="1744767764552" duration="12378000" />
      <workItem from="1744797817459" duration="9000" />
      <workItem from="1744853039752" duration="1430000" />
      <workItem from="1745049700891" duration="917000" />
      <workItem from="1745064876720" duration="24000" />
      <workItem from="1745370081329" duration="17562000" />
      <workItem from="1745457618042" duration="23674000" />
      <workItem from="1745546314270" duration="15522000" />
      <workItem from="1745665027557" duration="11593000" />
      <workItem from="1745715890751" duration="8717000" />
      <workItem from="1745759716152" duration="597000" />
      <workItem from="1745802214490" duration="27651000" />
      <workItem from="1745843131498" duration="8055000" />
      <workItem from="1745888758510" duration="23980000" />
      <workItem from="1745917714490" duration="2423000" />
      <workItem from="1745975556834" duration="4865000" />
      <workItem from="1745982067662" duration="8087000" />
      <workItem from="1745999277053" duration="4323000" />
      <workItem from="1746225576562" duration="8036000" />
      <workItem from="1746234635149" duration="9826000" />
      <workItem from="1746265897397" duration="1152000" />
      <workItem from="1746271188856" duration="6927000" />
      <workItem from="1746493654067" duration="13626000" />
      <workItem from="1746607169660" duration="652000" />
      <workItem from="1746607841315" duration="3693000" />
      <workItem from="1746666777881" duration="6329000" />
      <workItem from="1746685819966" duration="2871000" />
      <workItem from="1746754372100" duration="5930000" />
      <workItem from="1746771483769" duration="1086000" />
      <workItem from="1746772577699" duration="11000" />
      <workItem from="1746773909453" duration="977000" />
      <workItem from="1746775299068" duration="601000" />
      <workItem from="1747041664043" duration="1379000" />
      <workItem from="1747051701919" duration="597000" />
      <workItem from="1747057345489" duration="34000" />
      <workItem from="1747098281273" duration="5317000" />
      <workItem from="1747103781044" duration="374000" />
      <workItem from="1747104167900" duration="3778000" />
      <workItem from="1747107974911" duration="1883000" />
      <workItem from="1747111846540" duration="612000" />
      <workItem from="1747112493888" duration="7000" />
      <workItem from="1747116941265" duration="73000" />
      <workItem from="1747119805685" duration="3218000" />
      <workItem from="1747127197509" duration="26000" />
      <workItem from="1747193216808" duration="675000" />
      <workItem from="1747199238528" duration="844000" />
      <workItem from="1747200124884" duration="318000" />
      <workItem from="1747200706744" duration="2046000" />
      <workItem from="1747203167128" duration="5000" />
      <workItem from="1747203869310" duration="1339000" />
      <workItem from="1747301505989" duration="1641000" />
      <workItem from="1747313645731" duration="5293000" />
      <workItem from="1747358861863" duration="1400000" />
      <workItem from="1747365484749" duration="2654000" />
      <workItem from="1747378547108" duration="8027000" />
      <workItem from="1747387114472" duration="4571000" />
      <workItem from="1747538912406" duration="1097000" />
      <workItem from="1747576351181" duration="2142000" />
      <workItem from="1747623088421" duration="1389000" />
      <workItem from="1747638204335" duration="48000" />
      <workItem from="1747655765341" duration="2307000" />
      <workItem from="1747789484070" duration="14745000" />
      <workItem from="1747821876234" duration="1119000" />
      <workItem from="1747831702519" duration="15000" />
      <workItem from="1747891485073" duration="319000" />
      <workItem from="1747892119800" duration="2130000" />
      <workItem from="1747899178433" duration="4031000" />
      <workItem from="1747909469326" duration="2067000" />
      <workItem from="1747962346976" duration="13056000" />
      <workItem from="1747987797243" duration="5647000" />
      <workItem from="1747997262992" duration="1608000" />
      <workItem from="1748046278860" duration="12022000" />
      <workItem from="1748079272935" duration="2033000" />
      <workItem from="1748180638586" duration="6811000" />
      <workItem from="1748221412266" duration="18413000" />
      <workItem from="1748245428624" duration="6326000" />
      <workItem from="1748260548008" duration="49000" />
      <workItem from="1748480038210" duration="2302000" />
      <workItem from="1748485445339" duration="12876000" />
      <workItem from="1748503870654" duration="7922000" />
      <workItem from="1748566099415" duration="35000" />
      <workItem from="1748912122681" duration="10540000" />
      <workItem from="1748936249619" duration="3959000" />
      <workItem from="1748944038110" duration="12000" />
      <workItem from="1749004794505" duration="1809000" />
      <workItem from="1749192118707" duration="145000" />
      <workItem from="1749194052712" duration="2685000" />
      <workItem from="1749288189359" duration="38000" />
      <workItem from="1749541056305" duration="1349000" />
      <workItem from="1749690157489" duration="4167000" />
      <workItem from="1749705964013" duration="2002000" />
      <workItem from="1749708734823" duration="1616000" />
      <workItem from="1749817747619" duration="5472000" />
      <workItem from="1749859267663" duration="6016000" />
      <workItem from="1749911736876" duration="4756000" />
      <workItem from="1750048466317" duration="12935000" />
      <workItem from="1750122238357" duration="22271000" />
      <workItem from="1750236855335" duration="858000" />
      <workItem from="1750295925275" duration="6233000" />
      <workItem from="1750311564116" duration="10073000" />
      <workItem from="1750326845484" duration="1204000" />
      <workItem from="1750333849321" duration="6350000" />
      <workItem from="1750381141711" duration="16923000" />
      <workItem from="1750402266107" duration="7596000" />
      <workItem from="1750590008931" duration="5112000" />
      <workItem from="1750595709188" duration="4557000" />
      <workItem from="1750605392773" duration="22883000" />
      <workItem from="1750658880409" duration="7250000" />
      <workItem from="1750670381877" duration="8684000" />
      <workItem from="1750682797422" duration="1142000" />
      <workItem from="1750684726844" duration="3831000" />
      <workItem from="1750728353634" duration="13289000" />
      <workItem from="1750746650699" duration="21000" />
      <workItem from="1750750264664" duration="10329000" />
      <workItem from="1750765000183" duration="3743000" />
      <workItem from="1750769661698" duration="119000" />
      <workItem from="1750770918402" duration="8557000" />
      <workItem from="1750782840540" duration="1176000" />
      <workItem from="1750825234459" duration="13454000" />
      <workItem from="1750861347862" duration="179000" />
      <workItem from="1750861553297" duration="97000" />
      <workItem from="1750862529341" duration="2094000" />
      <workItem from="1750867165570" duration="55000" />
      <workItem from="1750904664619" duration="5455000" />
      <workItem from="1750925710420" duration="629000" />
      <workItem from="1750928267325" duration="2747000" />
      <workItem from="1750938622694" duration="7744000" />
      <workItem from="1750950522291" duration="3689000" />
      <workItem from="1750993506764" duration="6799000" />
      <workItem from="1751004759020" duration="6374000" />
      <workItem from="1751083518917" duration="1607000" />
      <workItem from="1751087070672" duration="3689000" />
      <workItem from="1751091485064" duration="9717000" />
      <workItem from="1751251684068" duration="5378000" />
      <workItem from="1751262651928" duration="2372000" />
      <workItem from="1751267157140" duration="8000" />
      <workItem from="1751267578022" duration="385000" />
      <workItem from="1751269375493" duration="13004000" />
      <workItem from="1751303521240" duration="1470000" />
      <workItem from="1751331566171" duration="243000" />
      <workItem from="1751335121680" duration="12947000" />
      <workItem from="1751423263041" duration="19825000" />
      <workItem from="1751507964223" duration="7570000" />
      <workItem from="1751598563054" duration="5807000" />
      <workItem from="1751622355379" duration="4147000" />
      <workItem from="1751636538607" duration="3680000" />
      <workItem from="1751644799728" duration="1327000" />
      <workItem from="1751816583702" duration="28000" />
      <workItem from="1751817150792" duration="428000" />
      <workItem from="1751821737028" duration="158000" />
      <workItem from="1751821938391" duration="68000" />
      <workItem from="1751822054428" duration="7000" />
      <workItem from="1751855460079" duration="2062000" />
      <workItem from="1751871840852" duration="6000" />
      <workItem from="1751874897652" duration="7622000" />
      <workItem from="1751941156659" duration="15745000" />
      <workItem from="1751965356963" duration="2331000" />
      <workItem from="1752023526166" duration="15000" />
      <workItem from="1752024721844" duration="5730000" />
      <workItem from="1752037961781" duration="32000" />
      <workItem from="1752038008383" duration="7041000" />
      <workItem from="1752110234092" duration="1888000" />
      <workItem from="1752135446690" duration="277000" />
      <workItem from="1752135850865" duration="1304000" />
      <workItem from="1752201366679" duration="19923000" />
      <workItem from="1752242123618" duration="688000" />
      <workItem from="1752462508466" duration="10427000" />
      <workItem from="1752490047537" duration="1358000" />
      <workItem from="1752545923321" duration="10119000" />
      <workItem from="1752566012919" duration="13382000" />
      <workItem from="1752631357591" duration="6775000" />
      <workItem from="1752646600514" duration="2008000" />
      <workItem from="1752711542263" duration="55080000" />
      <workItem from="1752825501909" duration="9586000" />
      <workItem from="1752840668394" duration="6943000" />
      <workItem from="1752847712688" duration="1419000" />
      <workItem from="1752849147335" duration="2095000" />
      <workItem from="1752851265568" duration="3721000" />
      <workItem from="1752855028029" duration="10378000" />
      <workItem from="1752865575520" duration="43000" />
      <workItem from="1753065360525" duration="16214000" />
      <workItem from="1753173566708" duration="27511000" />
      <workItem from="1753340525416" duration="82000" />
      <workItem from="1753341588582" duration="1754000" />
      <workItem from="1753347918588" duration="554000" />
      <workItem from="1753409875387" duration="5245000" />
      <workItem from="1753422293741" duration="13090000" />
      <workItem from="1753439533790" duration="4619000" />
      <workItem from="1753685586360" duration="12988000" />
      <workItem from="1753752072171" duration="1138000" />
      <workItem from="1753758812259" duration="2353000" />
      <workItem from="1753766501962" duration="7969000" />
      <workItem from="1753838023967" duration="7671000" />
    </task>
    <task id="LOCAL-00113" summary="渠道商添加身份证号字段">
      <option name="closed" value="true" />
      <created>1748940194725</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1748940194726</updated>
    </task>
    <task id="LOCAL-00114" summary="管理端企业信息管理显示会员手机号">
      <option name="closed" value="true" />
      <created>1749195992655</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1749195992656</updated>
    </task>
    <task id="LOCAL-00115" summary="配置正式服mysql、redis">
      <option name="closed" value="true" />
      <created>1749823230275</created>
      <option name="number" value="00115" />
      <option name="presentableId" value="LOCAL-00115" />
      <option name="project" value="LOCAL" />
      <updated>1749823230275</updated>
    </task>
    <task id="LOCAL-00116" summary="企业注册后选择会员类型支付回调成功自动开通诺企安，电局手机号码使用企业用户的手机号、地区编码根据枚举自动获取">
      <option name="closed" value="true" />
      <created>1749916438102</created>
      <option name="number" value="00116" />
      <option name="presentableId" value="LOCAL-00116" />
      <option name="project" value="LOCAL" />
      <updated>1749916438102</updated>
    </task>
    <task id="LOCAL-00117" summary="1、企业注册时增加“电局手机号”必填字段&#10;2、根据企业注册地址自动匹配地区编码&#10;3、支付回调中修改开通诺企安相关代码，改为使用用户企业关系表中的点击手机号和地区编码进行开通">
      <option name="closed" value="true" />
      <created>1750139048640</created>
      <option name="number" value="00117" />
      <option name="presentableId" value="LOCAL-00117" />
      <option name="project" value="LOCAL" />
      <updated>1750139048640</updated>
    </task>
    <task id="LOCAL-00118" summary="渠道用户管理、渠道团队管理、渠道产品关联管理、渠道码管理基础增删改查">
      <option name="closed" value="true" />
      <created>1750312757230</created>
      <option name="number" value="00118" />
      <option name="presentableId" value="LOCAL-00118" />
      <option name="project" value="LOCAL" />
      <updated>1750312757230</updated>
    </task>
    <task id="LOCAL-00119" summary="渠道码管理随机生成渠道码备用、渠道团队管理多级管理、新增渠道团队自动分配可用渠道码">
      <option name="closed" value="true" />
      <created>1750325348977</created>
      <option name="number" value="00119" />
      <option name="presentableId" value="LOCAL-00119" />
      <option name="project" value="LOCAL" />
      <updated>1750325348977</updated>
    </task>
    <task id="LOCAL-00120" summary="新渠道用户登录方式&#10;税眸字样替换">
      <option name="closed" value="true" />
      <created>1750407209980</created>
      <option name="number" value="00120" />
      <option name="presentableId" value="LOCAL-00120" />
      <option name="project" value="LOCAL" />
      <updated>1750407209982</updated>
    </task>
    <task id="LOCAL-00121" summary="渠道团队管理">
      <option name="closed" value="true" />
      <created>1750616869074</created>
      <option name="number" value="00121" />
      <option name="presentableId" value="LOCAL-00121" />
      <option name="project" value="LOCAL" />
      <updated>1750616869075</updated>
    </task>
    <task id="LOCAL-00122" summary="渠道产品分配">
      <option name="closed" value="true" />
      <created>1750622500754</created>
      <option name="number" value="00122" />
      <option name="presentableId" value="LOCAL-00122" />
      <option name="project" value="LOCAL" />
      <updated>1750622500754</updated>
    </task>
    <task id="LOCAL-00123" summary="正式服配置">
      <option name="closed" value="true" />
      <created>1750648690744</created>
      <option name="number" value="00123" />
      <option name="presentableId" value="LOCAL-00123" />
      <option name="project" value="LOCAL" />
      <updated>1750648690745</updated>
    </task>
    <task id="LOCAL-00124" summary="正确显示渠道产品关联">
      <option name="closed" value="true" />
      <created>1750688823596</created>
      <option name="number" value="00124" />
      <option name="presentableId" value="LOCAL-00124" />
      <option name="project" value="LOCAL" />
      <updated>1750688823596</updated>
    </task>
    <task id="LOCAL-00125" summary="渠道端产看我的产品、给渠道分配产品、我的团队管理、给团队成员关联自己的渠道">
      <option name="closed" value="true" />
      <created>1750740006098</created>
      <option name="number" value="00125" />
      <option name="presentableId" value="LOCAL-00125" />
      <option name="project" value="LOCAL" />
      <updated>1750740006099</updated>
    </task>
    <task id="LOCAL-00126" summary="配置回眸小程序appid和秘钥，用于生成邀请码">
      <option name="closed" value="true" />
      <created>1750744461242</created>
      <option name="number" value="00126" />
      <option name="presentableId" value="LOCAL-00126" />
      <option name="project" value="LOCAL" />
      <updated>1750744461242</updated>
    </task>
    <task id="LOCAL-00127" summary="Web企业使用邀请码注册">
      <option name="closed" value="true" />
      <created>1750756622190</created>
      <option name="number" value="00127" />
      <option name="presentableId" value="LOCAL-00127" />
      <option name="project" value="LOCAL" />
      <updated>1750756622191</updated>
    </task>
    <task id="LOCAL-00128" summary="渠道端我的客户业绩统计">
      <option name="closed" value="true" />
      <created>1750768913927</created>
      <option name="number" value="00128" />
      <option name="presentableId" value="LOCAL-00128" />
      <option name="project" value="LOCAL" />
      <updated>1750768913928</updated>
    </task>
    <task id="LOCAL-00129" summary="删除多余代码">
      <option name="closed" value="true" />
      <created>1750769747740</created>
      <option name="number" value="00129" />
      <option name="presentableId" value="LOCAL-00129" />
      <option name="project" value="LOCAL" />
      <updated>1750769747740</updated>
    </task>
    <task id="LOCAL-00130" summary="管理端渠道业绩和客户统计">
      <option name="closed" value="true" />
      <created>1750773751286</created>
      <option name="number" value="00130" />
      <option name="presentableId" value="LOCAL-00130" />
      <option name="project" value="LOCAL" />
      <updated>1750773751286</updated>
    </task>
    <task id="LOCAL-00131" summary="查询可可访问团队（修bug）">
      <option name="closed" value="true" />
      <created>1750780311309</created>
      <option name="number" value="00131" />
      <option name="presentableId" value="LOCAL-00131" />
      <option name="project" value="LOCAL" />
      <updated>1750780311309</updated>
    </task>
    <task id="LOCAL-00132" summary="fix:二级渠道无法显示子级渠道">
      <option name="closed" value="true" />
      <created>1750939325147</created>
      <option name="number" value="00132" />
      <option name="presentableId" value="LOCAL-00132" />
      <option name="project" value="LOCAL" />
      <updated>1750939325147</updated>
    </task>
    <task id="LOCAL-00133" summary="fix:航信报告保存有误，使用了没有支付的企业信息">
      <option name="closed" value="true" />
      <created>1751098408389</created>
      <option name="number" value="00133" />
      <option name="presentableId" value="LOCAL-00133" />
      <option name="project" value="LOCAL" />
      <updated>1751098408390</updated>
    </task>
    <task id="LOCAL-00134" summary="增加会员用户删除功能">
      <option name="closed" value="true" />
      <created>1751105481659</created>
      <option name="number" value="00134" />
      <option name="presentableId" value="LOCAL-00134" />
      <option name="project" value="LOCAL" />
      <updated>1751105481659</updated>
    </task>
    <task id="LOCAL-00135" summary="fix:渠道优化">
      <option name="closed" value="true" />
      <created>1751260803874</created>
      <option name="number" value="00135" />
      <option name="presentableId" value="LOCAL-00135" />
      <option name="project" value="LOCAL" />
      <updated>1751260803875</updated>
    </task>
    <task id="LOCAL-00136" summary="fix:删除掉的企业出现在业绩统计当中">
      <option name="closed" value="true" />
      <created>1751264490358</created>
      <option name="number" value="00136" />
      <option name="presentableId" value="LOCAL-00136" />
      <option name="project" value="LOCAL" />
      <updated>1751264490358</updated>
    </task>
    <task id="LOCAL-00137" summary="发票管理、发票处理">
      <option name="closed" value="true" />
      <created>1751291958277</created>
      <option name="number" value="00137" />
      <option name="presentableId" value="LOCAL-00137" />
      <option name="project" value="LOCAL" />
      <updated>1751291958278</updated>
    </task>
    <task id="LOCAL-00138" summary="fix：注册企业匹配新的地区码（6位），诺企安注册失败不影响后续支付回调成功">
      <option name="closed" value="true" />
      <created>1751361403209</created>
      <option name="number" value="00138" />
      <option name="presentableId" value="LOCAL-00138" />
      <option name="project" value="LOCAL" />
      <updated>1751361403209</updated>
    </task>
    <task id="LOCAL-00139" summary="fix：规范调价权限功能： 1、只有当前渠道团队有调价权限并且茶品允许调价时才可以进行产品调价 2、只有当前渠道团队有调价权限并且茶品允许调价时次可以可以给下级渠道分配调价权限，否则只能设定固定价格">
      <option name="closed" value="true" />
      <created>1751440316351</created>
      <option name="number" value="00139" />
      <option name="presentableId" value="LOCAL-00139" />
      <option name="project" value="LOCAL" />
      <updated>1751440316353</updated>
    </task>
    <task id="LOCAL-00140" summary="业绩统计，按日、周、年统计">
      <option name="closed" value="true" />
      <created>1751465599420</created>
      <option name="number" value="00140" />
      <option name="presentableId" value="LOCAL-00140" />
      <option name="project" value="LOCAL" />
      <updated>1751465599420</updated>
    </task>
    <task id="LOCAL-00141" summary="fix:渠道管理员给渠道用户生成的二维码绑定了错误的邀请码，使用了当前登录用户的邀请码">
      <option name="closed" value="true" />
      <created>1751523424084</created>
      <option name="number" value="00141" />
      <option name="presentableId" value="LOCAL-00141" />
      <option name="project" value="LOCAL" />
      <updated>1751523424085</updated>
    </task>
    <task id="LOCAL-00142" summary="fix:根据city字段匹配地区码，u在使用注册地址匹配">
      <option name="closed" value="true" />
      <created>1751638931539</created>
      <option name="number" value="00142" />
      <option name="presentableId" value="LOCAL-00142" />
      <option name="project" value="LOCAL" />
      <updated>1751638931540</updated>
    </task>
    <task id="LOCAL-00143" summary="fix:根据city字段匹配地区码，u在使用注册地址匹配">
      <option name="closed" value="true" />
      <created>1751639098996</created>
      <option name="number" value="00143" />
      <option name="presentableId" value="LOCAL-00143" />
      <option name="project" value="LOCAL" />
      <updated>1751639098996</updated>
    </task>
    <task id="LOCAL-00144" summary="优化企业订单查询逻辑">
      <option name="closed" value="true" />
      <created>1751952243163</created>
      <option name="number" value="00144" />
      <option name="presentableId" value="LOCAL-00144" />
      <option name="project" value="LOCAL" />
      <updated>1751952243163</updated>
    </task>
    <task id="LOCAL-00145" summary="fix：匹配不到特殊城市也匹配不到省份时使用base字段匹配省份来保底">
      <option name="closed" value="true" />
      <created>1751955260631</created>
      <option name="number" value="00145" />
      <option name="presentableId" value="LOCAL-00145" />
      <option name="project" value="LOCAL" />
      <updated>1751955260631</updated>
    </task>
    <task id="LOCAL-00146" summary="fix：刻印发票可以不需要填税号">
      <option name="closed" value="true" />
      <created>1751955300869</created>
      <option name="number" value="00146" />
      <option name="presentableId" value="LOCAL-00146" />
      <option name="project" value="LOCAL" />
      <updated>1751955300869</updated>
    </task>
    <task id="LOCAL-00147" summary="企业信息管理搬到渠道端">
      <option name="closed" value="true" />
      <created>1752047458080</created>
      <option name="number" value="00147" />
      <option name="presentableId" value="LOCAL-00147" />
      <option name="project" value="LOCAL" />
      <updated>1752047458081</updated>
    </task>
    <task id="LOCAL-00148" summary="团队业绩同价添加“已付款金额总计”">
      <option name="closed" value="true" />
      <created>1752482936540</created>
      <option name="number" value="00148" />
      <option name="presentableId" value="LOCAL-00148" />
      <option name="project" value="LOCAL" />
      <updated>1752482936540</updated>
    </task>
    <task id="LOCAL-00149" summary="fix:团队业绩统计按注册时间、支付时间筛选">
      <option name="closed" value="true" />
      <created>1752484377777</created>
      <option name="number" value="00149" />
      <option name="presentableId" value="LOCAL-00149" />
      <option name="project" value="LOCAL" />
      <updated>1752484377777</updated>
    </task>
    <task id="LOCAL-00150" summary="fix:团队业绩统计按注册时间、支付日期筛选">
      <option name="closed" value="true" />
      <created>1752490072988</created>
      <option name="number" value="00150" />
      <option name="presentableId" value="LOCAL-00150" />
      <option name="project" value="LOCAL" />
      <updated>1752490072988</updated>
    </task>
    <task id="LOCAL-00151" summary="fix:团队业绩，部分业绩不显示，但条件查询能查出来">
      <option name="closed" value="true" />
      <created>1752572330070</created>
      <option name="number" value="00151" />
      <option name="presentableId" value="LOCAL-00151" />
      <option name="project" value="LOCAL" />
      <updated>1752572330070</updated>
    </task>
    <task id="LOCAL-00152" summary="添加易宝支付渠道">
      <option name="closed" value="true" />
      <created>1752582481164</created>
      <option name="number" value="00152" />
      <option name="presentableId" value="LOCAL-00152" />
      <option name="project" value="LOCAL" />
      <updated>1752582481164</updated>
    </task>
    <task id="LOCAL-00153" summary="易宝无sdk支付demo">
      <option name="closed" value="true" />
      <created>1752638285322</created>
      <option name="number" value="00153" />
      <option name="presentableId" value="LOCAL-00153" />
      <option name="project" value="LOCAL" />
      <updated>1752638285322</updated>
    </task>
    <task id="LOCAL-00154" summary="fix：渠道端业绩统计只按照支付时间进行排序">
      <option name="closed" value="true" />
      <created>1752711687468</created>
      <option name="number" value="00154" />
      <option name="presentableId" value="LOCAL-00154" />
      <option name="project" value="LOCAL" />
      <updated>1752711687469</updated>
    </task>
    <task id="LOCAL-00155" summary="对接易宝支付">
      <option name="closed" value="true" />
      <created>1752865453909</created>
      <option name="number" value="00155" />
      <option name="presentableId" value="LOCAL-00155" />
      <option name="project" value="LOCAL" />
      <updated>1752865453909</updated>
    </task>
    <task id="LOCAL-00156" summary="删除无用压缩包">
      <option name="closed" value="true" />
      <created>1753073401226</created>
      <option name="number" value="00156" />
      <option name="presentableId" value="LOCAL-00156" />
      <option name="project" value="LOCAL" />
      <updated>1753073401226</updated>
    </task>
    <task id="LOCAL-00157" summary="自动匹配邀请码">
      <option name="closed" value="true" />
      <created>1753087433304</created>
      <option name="number" value="00157" />
      <option name="presentableId" value="LOCAL-00157" />
      <option name="project" value="LOCAL" />
      <updated>1753087433305</updated>
    </task>
    <task id="LOCAL-00158" summary="管理端商学院课程分配给渠道">
      <option name="closed" value="true" />
      <created>1753260149932</created>
      <option name="number" value="00158" />
      <option name="presentableId" value="LOCAL-00158" />
      <option name="project" value="LOCAL" />
      <updated>1753260149933</updated>
    </task>
    <task id="LOCAL-00159" summary="管理端商学院课程分配给渠道">
      <option name="closed" value="true" />
      <created>1753260201623</created>
      <option name="number" value="00159" />
      <option name="presentableId" value="LOCAL-00159" />
      <option name="project" value="LOCAL" />
      <updated>1753260201623</updated>
    </task>
    <task id="LOCAL-00160" summary="fix：纠正河南生地区代号：hen">
      <option name="closed" value="true" />
      <created>1753691243730</created>
      <option name="number" value="00160" />
      <option name="presentableId" value="LOCAL-00160" />
      <option name="project" value="LOCAL" />
      <updated>1753691243730</updated>
    </task>
    <task id="LOCAL-00161" summary="渠道端上传课程&#10;管理端审核课程">
      <option name="closed" value="true" />
      <created>1753838084402</created>
      <option name="number" value="00161" />
      <option name="presentableId" value="LOCAL-00161" />
      <option name="project" value="LOCAL" />
      <updated>1753838084402</updated>
    </task>
    <option name="localTasksCounter" value="162" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/doc/11参考/public/src/store/modules/common.js" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix:渠道优化" />
    <MESSAGE value="fix:删除掉的企业出现在业绩统计当中" />
    <MESSAGE value="发票管理、发票处理" />
    <MESSAGE value="fix：注册企业匹配新的地区码（6位），诺企安注册失败不影响后续支付回调成功" />
    <MESSAGE value="fix：规范调价权限功能： 1、只有当前渠道团队有调价权限并且茶品允许调价时才可以进行产品调价 2、只有当前渠道团队有调价权限并且茶品允许调价时次可以可以给下级渠道分配调价权限，否则只能设定固定价格" />
    <MESSAGE value="业绩统计，按日、周、年统计" />
    <MESSAGE value="fix:渠道管理员给渠道用户生成的二维码绑定了错误的邀请码，使用了当前登录用户的邀请码" />
    <MESSAGE value="fix:根据city字段匹配地区码，u在使用注册地址匹配" />
    <MESSAGE value="优化企业订单查询逻辑" />
    <MESSAGE value="fix：匹配不到特殊城市也匹配不到省份时使用base字段匹配省份来保底" />
    <MESSAGE value="fix：刻印发票可以不需要填税号" />
    <MESSAGE value="企业信息管理搬到渠道端" />
    <MESSAGE value="团队业绩同价添加“已付款金额总计”" />
    <MESSAGE value="fix:团队业绩统计按注册时间、支付时间筛选" />
    <MESSAGE value="fix:团队业绩统计按注册时间、支付日期筛选" />
    <MESSAGE value="fix:团队业绩，部分业绩不显示，但条件查询能查出来" />
    <MESSAGE value="添加易宝支付渠道" />
    <MESSAGE value="易宝无sdk支付demo" />
    <MESSAGE value="fix：渠道端业绩统计只按照支付时间进行排序" />
    <MESSAGE value="对接易宝支付" />
    <MESSAGE value="删除无用压缩包" />
    <MESSAGE value="自动匹配邀请码" />
    <MESSAGE value="管理端商学院课程分配给渠道" />
    <MESSAGE value="fix：纠正河南生地区代号：hen" />
    <MESSAGE value="渠道端上传课程&#10;管理端审核课程" />
    <option name="LAST_COMMIT_MESSAGE" value="渠道端上传课程&#10;管理端审核课程" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint type="java-exception">
          <properties class="org.springframework.web.servlet.resource.NoResourceFoundException" package="org.springframework.web.servlet.resource" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint type="java-exception">
          <properties class="cn.iocoder.yudao.framework.common.exception.ServiceException" package="cn.iocoder.yudao.framework.common.exception" />
          <option name="timeStamp" value="3" />
        </breakpoint>
        <breakpoint type="java-exception">
          <properties class="org.quartz.ObjectAlreadyExistsException" package="org.quartz" />
          <option name="timeStamp" value="10" />
        </breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/framework/file/core/client/local/LocalFileClient.java</url>
          <line>44</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/contractremind/ContractRemindServiceImpl.java</url>
          <line>85</line>
          <option name="timeStamp" value="12" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/controller/admin/job/JobController.java</url>
          <line>88</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/service/job/JobServiceImpl.java</url>
          <line>181</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-framework/yudao-spring-boot-starter-job/src/main/java/cn/iocoder/yudao/framework/quartz/core/scheduler/SchedulerManager.java</url>
          <line>130</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/controller/app/EnterprisePartnerRiskConfig/AppEnterprisePartnerRiskConfigController.java</url>
          <line>29</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/enterprisepartnerthird/EnterprisePartnerThirdServiceImpl.java</url>
          <line>177</line>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/enterprisepartnerthird/EnterprisePartnerThirdServiceImpl.java</url>
          <line>187</line>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/shareenterprise/ShareEnterpriseServiceImpl.java</url>
          <line>127</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/shareenterprise/ShareEnterpriseServiceImpl.java</url>
          <line>135</line>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/shareenterprise/ShareEnterpriseServiceImpl.java</url>
          <line>156</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/controller/app/shareenterprise/AppShareEnterpriseController.java</url>
          <line>117</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/shareenterprise/ShareEnterpriseServiceImpl.java</url>
          <line>118</line>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/controller/app/shareenterprise/AppShareEnterpriseController.java</url>
          <line>101</line>
          <option name="timeStamp" value="45" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/controller/app/shareenterprise/AppShareEnterpriseController.java</url>
          <line>163</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-member/yudao-module-member-biz/src/main/java/cn/iocoder/yudao/module/member/service/shareenterprise/ShareEnterpriseServiceImpl.java</url>
          <line>241</line>
          <option name="timeStamp" value="48" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/service/sms/SmsTemplateServiceImpl.java</url>
          <line>181</line>
          <option name="timeStamp" value="70" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="cn.iocoder.yudao.module.member.controller.admin.contract.ContractController" memberName="contractService" />
        <PinnedItemInfo parentTag="java.lang.Long" memberName="value" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="        //使用426接口&#10;        JSONObject result = tianYanChaClient.getCbJudicial426(name);" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XPathView.XPathProjectComponent">
    <history />
    <find-history>
      <element expression="infra" />
    </find-history>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/P202506TS_Api$YeepayDirectTestRunner.ic" NAME="YeepayDirectTestRunner 覆盖结果" MODIFIED="1752721566360" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>cn.iocoder.yudao.framework.pay.core.client.impl.yeepay.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/P202501YQF_Api$PdfToRichTextUtilTest_testConvertFromUrl.ic" NAME="PdfToRichTextUtilTest.testConvertFromUrl 覆盖结果" MODIFIED="1747102117670" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="true">
      <FILTER>cn.iocoder.yudao.framework.common.util.pdf.*</FILTER>
    </SUITE>
  </component>
</project>