"use strict";const e=require("../../common/vendor.js"),t=require("../../sheep/index.js");if(!Array){e.resolveComponent("l-painter")()}Math;const r={__name:"pickUpVerify",props:{orderInfo:{type:Object,default(){}},systemStore:{type:Object,default(){}}},setup(r,{expose:o}){const s=r,a=e.reactive({qrcodeSize:145}),d=()=>{console.log(s.systemStore),s.systemStore.latitude&&s.systemStore.longitude?e.index.openLocation({latitude:s.systemStore.latitude,longitude:s.systemStore.longitude,scale:8,name:s.systemStore.name,address:s.systemStore.areaName+s.systemStore.detailAddress}):t.sheep.$helper.toast("缺少经纬度信息无法查看地图!")},i=e.ref(),p=e.ref(),n=e.ref(!0),l=e=>{p.value=e,n.value=!1};return o({markCode:e=>{(async e=>{await i.value.render(e)})({css:{width:`${a.qrcodeSize}px`,height:`${a.qrcodeSize}px`},views:[{type:"qrcode",text:e,css:{width:`${a.qrcodeSize}px`,height:`${a.qrcodeSize}px`}}]})}}),(o,s)=>e.e({a:2===r.orderInfo.deliveryType&&r.orderInfo.payStatus},2===r.orderInfo.deliveryType&&r.orderInfo.payStatus?e.e({b:!!p.value},p.value?{c:p.value,d:`${a.qrcodeSize}px`,e:`${a.qrcodeSize}px`}:{},{f:e.unref(t.sheep).$url.static("/static/images/writeOff.png","local"),g:e.t(r.orderInfo.pickUpVerifyCode)}):{},{h:2===r.orderInfo.deliveryType},2===r.orderInfo.deliveryType?{i:e.o(d)}:{},{j:n.value},n.value?{k:e.sr(i,"93d35884-0",{k:"painterRef"}),l:e.o(l),m:e.p({isCanvasToTempFilePath:!0,pathType:"url",hidden:!0})}:{})}},o=e._export_sfc(r,[["__scopeId","data-v-93d35884"]]);wx.createComponent(o);
